import { getDicts } from "@/api/system/dict/data";
import { listMaster } from "@/api/master/master";
import { listOrgRelation2 } from "@/api/sales/orgRelation/orgRelation";
import { listWerksInfo } from "@/api/sales/werksInfo/werksInfo";
import { listCustomerMaster,queryKunnrList } from "@/api/master/customerMaster";
import { getDictOptions,setDictOptions }from '@/utils/location'

import { listSupplierbasic2,  } from "@/api/master/supplierbasic";
import { listCostCenterConfig} from "@/api/master/costCenterConfig";
// import { listMaster } from "@/api/master/master";
const funType = {
  VKORG: listOrgRelation2,
  LGORT: listWerksInfo,
  KUNNR: queryKunnrList,
  EKORG:listOrgRelation2,
  LIFNR:listSupplierbasic2,
  ZZMATNR:listMaster,
  KOSTL:listCostCenterConfig
};

const state = {
  dictDataOptions: {},
};
const mutations = {
  SET_DATA_OPTIONS: (state, { key, value }) => {
    if(state?.dictDataOptions[key] && state?.dictDataOptions[key]?.length)return
    
    if (key !== null && key !== "") {
      const data = Array.isArray(value) ? value : [];
      const newData = data.map((item) => {
        return {
          ...item,
          value: item?.dictValue,
          label: item?.dictLabel,
        };
      });
      state.dictDataOptions[key] = newData;

      // console.log(state?.dictDataOptions,'state?.dictDataOptions')

      setDictOptions(state?.dictDataOptions || {})
    }
  },
};

const actions = {
  // 设置字典
  setDataOptions({ commit }, data) {
    commit("SET_DATA_OPTIONS", data);
  },

  async getFeatch({ commit, state }, { code, dataType = "", params }) {
    const codeData = code ? state?.dictDataOptions[code] || [] : [];
    let locData = getDictOptions()
    if(locData[code] && locData[code].length && (!codeData || codeData.length ===0) ){
       commit("SET_DATA_OPTIONS", { key: code, value: locData[code] });
      return
    }
    if (codeData && codeData.length) {
      return codeData;
    } else {
      try {
        let res = () => {};
        
        if (dataType && funType[dataType]) {
            params.pageSize = 10000
            params.pageNum = 1

          res = await funType[dataType](params);
        } else if(code) {
          res = await getDicts(code);
        }

        
        if (res?.code === 200 && res.data) {
          const obj = res?.data?.map((item) => {
            return {
              ...item,
              value: item?.dictValue,
              label: item?.dictLabel,
            };
          });
          commit("SET_DATA_OPTIONS", { key: code, value: obj });
          return obj;
        } else if (res?.code === 200 && res.rows) {
          const obj = res?.rows?.map((item) => {
            return {
              ...item,
              value: item?.dictValue,
              label: item?.dictLabel,
            };
          });
          // console.log(obj,dataType,code,'00000333')
          commit("SET_DATA_OPTIONS", { key: code, value: obj });
          return obj;
        } else {
          return [];
        }
      } catch (error) {
        console.log(error, "error");
        return "";
      }
    }
  },

  getOptions({ dispatch }, { dataColumns = [] }) {
    if (!dataColumns || dataColumns.length === 0) return;
    const propNameCode = dataColumns.filter((item) => item.backProps);
    if (propNameCode && propNameCode.length) {
      propNameCode.forEach(({ prop, params }) => {
        const dataType = [prop];
        const code = [prop];
        dispatch("getFeatch", { dataType, code, params });
      });
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
