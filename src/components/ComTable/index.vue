<template>
  <div class="comTable" v-loading="tableLoading">
    <div
     v-if="showHeaderConfig"
      :class="
        tableProps.lineBtnrightClass
          ? `lineBtnright ${tableProps.lineBtnrightClass} `
          : 'lineBtnright'
      "
    >
      <el-tooltip class="item" effect="dark" content="配置表头" placement="top">
        <el-button
          icon="el-icon-s-unfold"
          size="mini"
          @click="getColumnCinfig()"
        ></el-button>
      </el-tooltip>
    </div>

    <el-table
      ref="comTable"
      :border="border"
      :data="list"
      style="width: 100%"
      :row-key="tableProps.rowKey || rowKey || 'tableId'"
      v-bind="tableProps"
      highlight-current-row
      @select="select"
      @selection-change="handleSelectionChange"
      @current-change="handleCurrentChange"
      @cell-dblclick="cellDblclick"
    >
      <el-table-column
        v-if="isSelection"
        :reserve-selection="true"
        type="selection"
        width="50"
        align="center"
        fixed="left"
        :selectable="selectable"
      />
      <el-table-column
        v-if="typeIndex"
        label="序号"
        align="center"
        type="index"
        width="50"
        fixed="left"
      />
      <template v-for="(item) in tableColumn.filter(({visible = ''})=>(visible !=='1'))" >
        <el-table-column
          v-if="item.slotName"
          :prop="item.prop"
          :label="item.label"
          v-bind="item"
          :key="item.prop"
          :show-overflow-tooltip="
            item.prop === 'caozuo' ? false : showOverflowTooltip
          "
        >
          <template #header>
            <ComSearchItem
              v-if="
                !bottonValue && item.prop !== 'caozuo' && !item.hiddenSearch
              "
              v-model="searchObj[item.prop]"
              :item="item"
              @blur="refreshList"
              @change="changeInput($event, item)"
            />
            <span v-else>{{ item.label }}</span>
          </template>
          <template slot-scope="data">
            <slot :name="item.slotName" v-bind="data"></slot>
          </template>
        </el-table-column>

        <el-table-column
           v-else
          :prop="item.prop"
          :label="item.label"
          v-bind="item"
          :key="item.prop"
          :show-overflow-tooltip="showOverflowTooltip"
        >
          <template #header>
            <ComSearchItem
              v-if="
                !bottonValue && item.prop !== 'caozuo' && !item.hiddenSearch
              "
              v-model="searchObj[item.prop]"
              :item="item"
              @blur="refreshList"
              @change="changeInput($event, item)"
            />
            <span v-else>{{ item.label }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-pagination
      v-if="pagination.total > 0"
      :current-page="pagination.pageNum"
      :page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      v-bind="pagination"
      @size-change="
        (val) => {
          handleSizeChange(pagination.pageNum, val);
        }
      "
      @current-change="
        (val) => {
          handleSizeChange(val, pagination.pageSize);
        }
      "
    >
    </el-pagination>
    <DrawerColumn ref="DrawerColumn" @handOk="handOk"/>
  </div>
</template>

<script>
import ComSearchItem from "@/components/ComSearch/ComSearchItem.vue";
import DrawerColumn from "@/components/DrawerColumn";
import { setColumns } from "./config";
import { getColumnsOptions,setColumnsOptions } from '@/utils/location'
export default {
  name: "ComTable",
  components: {
    ComSearchItem,
    DrawerColumn,
  },
  // mixins:[mixinColumn],
  dicts:['table_mg'],
  props: {
    showHeaderConfig:{
      type: Boolean,
      default() {
        return true;
      },

    },
    border: {
      type: Boolean,
      default() {
        return true;
      },
    },
    isSelection: {
      type: Boolean,
      default() {
        return false;
      },
    },
    typeIndex: {
      type: Boolean,
      default() {
        return true;
      },
    },
    column: {
      type: Array,
      default() {
        return [];
      },
    },
    paginationProps: {
      type: Object,
      default() {
        return {
          total: 0,
          pageSize: 20,
          pageNum: 1,
        };
      },
    },
    loading: {
      type: Boolean,
      default() {
        return false;
      },
    },
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    tableProps: {
      type: Object,
      default() {
        return {};
      },
    },
    listSearchObj: {
      type: Object,
      default() {
        return {};
      },
    },
    selectable: {
      type: Function,
      default() {
        return () => true;
      },
    },
    isShowRightBtn: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showOverflowTooltip: {
      type: Boolean,
      default() {
        return true;
      },
    },
    rowKey:{
      type:String,
      default:''
    },
    keyId:{
       type:String,
        default:''
    }
  },
  data() {
    const c_key = this.keyId || location.pathname
    return {
      tableLoading: this._props.loading,
      tableColumn: setColumns(this.column,c_key),
      list: this.tableData,
      pagination: this.paginationProps,
      searchObj: {},
      bottonValue: true,
    };
  },
  watch: {
    searchObj(val) {
      // console.log(val, "val");
    },
    column(val) {
       const c_key = this.keyId || location.pathname
      this.tableColumn = setColumns(val,c_key);
    },

    tableData(data) {
      this.list = data;
    },
    paginationProps(val) {
      // console.log(val,val)
      this.pagination = val;
    },
    loading(val) {
      this.tableLoading = val;
    },
    bottonValue(val) {
      if (val) {
        const obj = {};
        Object.keys(this.searchObj).forEach((key) => {
          obj[key] = undefined;
        });
        this.searchObj = obj;
        this.refreshList();
      }
    },
  },
  computed: {
  
  },
  mounted() {
  },
  updated() {
    // let data = setColumns(this.column);

    // this.tableColumn = data;

    // console.log(this.tableColumn,'dadada')
  },
  methods: {
    getRowKey(row){
      const key = this.tableProps?.rowKey || this.rowKey || 'tableId'
      return row[key]

    },
    handleSizeChange(page, size) {
      this.$emit("changePage", { page, size });
    },
    select(selection, row) {
      this.$emit("select", selection, row);
    },
    handleSelectionChange(val) {
      this.$emit("selection-change", val);
    },
    handleCurrentChange(val) {
      this.$emit("current-change", val);
    },
    changeInput(val, item) {
      this.searchObj[item.prop] = val;
      if (item.type === "select" || item.type === "date") {
        this.refreshList();
      }
    },
    refreshList() {
      this.$emit("refreshList", this.searchObj);
    },
    cellDblclick(row, column, cell, event) {
      this.$emit("cell-dblclick", row, column, cell, event);
    },
    getColumnCinfig() {
      const pathname = this.keyId || location.pathname
      const cKey =  pathname

      let r_tableColumn = JSON.parse(JSON.stringify(this.tableColumn))
      if(getColumnsOptions(cKey)){
        r_tableColumn = getColumnsOptions(cKey)
      }else{
        r_tableColumn = r_tableColumn.map((item)=>{
           item.visible = true
           return item
        })
        setColumnsOptions(cKey,r_tableColumn)
      }
      this.$refs.DrawerColumn.drawer=true
      this.$nextTick(()=>{
        const _r_tableColumn = JSON.parse(JSON.stringify(r_tableColumn))
        this.$refs.DrawerColumn.tableData = _r_tableColumn
        this.$refs.DrawerColumn.getTable(_r_tableColumn)
      })

    },
    handOk(type,columns){
      const pathname = this.keyId || location.pathname
      const cKey = pathname

      setColumnsOptions(cKey,JSON.parse(JSON.stringify(columns)))
      const data =  setColumns(this.tableColumn,cKey)
       this.tableColumn = []
       this.$nextTick(()=>{
         this.tableColumn = [...data]
         this.$forceUpdate()
       })

    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-pagination {
  display: flex;
  justify-content: flex-end;
  margin: 10px;
}

.comTable {
  position: relative;
}

.lineBtnright {
  position: absolute;
  right: 10px;
  top: 6px;
  display: flex;
  z-index: 10;
  ::v-deep .el-switch__core {
    width: 56px !important;
  }
  .box {
    position: relative;
  }
  .search {
    position: absolute;
    top: 4px;
    right: 30px;
    font-size: 12px;
    color: #fff;
    cursor: pointer;
  }
  .search2 {
    right: 20px;
  }
}

@media screen and (max-width: 900px) {
  // .sidebar { display: none; }
  ::v-deep .caozuo {
    width: 200px;
  }
}
</style>
