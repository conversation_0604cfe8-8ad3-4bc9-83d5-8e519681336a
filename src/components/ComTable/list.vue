<template>
  <div class="ComTableList">
    <ComSearch
      :searchData="searchDataList"
      :searchInfo="searchTableInfo"
      :showSearch="true"
      ref="searchComTableInfo"
      v-bind="searchProps"
      @submitEmits="submitEmits2"
      v-if="searchShow"
    >
      <template v-for="(index, name) in $scopedSlots" v-slot:[name]="data">
        <slot :name="name" v-bind="data"></slot>
      </template>
    </ComSearch>
    <slot></slot>
    <ComTable
      :column="columnList"
      :paginationProps="paginationProps"
      :tableData="isPage ? tableData : pageData"
      :loading="loading"
      :isSelection="isSelection"
      :showHeaderConfig="showHeaderConfig"
      :tableProps="tableProps"
      :keyId="keyId || tableProps.keyId"
      :selectable="selectable"
      :isShowRightBtn="isShowRightBtn"
      ref="table"
      @select="select"
      @selection-change="handleSelectionChange"
      @current-change="handleCurrentChange"
      @changePage="handleSizeChange"
      @refreshList="refreshList"
      @cell-dblclick="cellDblclick"
    >
      <template v-for="(index, name) in $scopedSlots" v-slot:[name]="data">
        <slot :name="name" v-bind="data"></slot>
      </template>
    </ComTable>
  </div>
</template>

<script>
import ComSearch from "@/components/ComSearch/index.vue";
import ComTable from "@/components/ComTable/index.vue";
import { setTableDictValue } from "@/utils/index.js";


 

const aCreateTime = [
  {
    label: "创建日期",
    type: "daterange",
    prop: "createTimeS,createTimeE",
    hiddenTable: true,

  },
  {
    label: "创建日期",
    type: "date",
    prop: "create",
    hiddenSearch: true,
  },
]
  

export default {
  name: "ComTableList",
  components: {
    ComSearch,
    ComTable,
  },
  props: {
    showHeaderConfig:{
      type: Boolean,
      default() {
        return true;
      },

    },
     keyId:{
       type:String,
        default:''
     },
     isShowRightBtn: {
      type: Boolean,
      default() {
        return false;
      },
    },
    searchInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    searchShow: {
      type: Boolean,
      default() {
        return true;
      },
    },
    isinitData: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isPage: {
      type: Boolean,
      default() {
        return true;
      },
    },
    searchProps: {
      type: Object,
      default() {
        return {};
      },
    },
    tableProps: {
      type: Object,
      default() {
        return {};
      },
    },
    isSelection: {
      type: Boolean,
      default() {
        return false;
      },
    },
    searchData: {
      type: Array,
      default() {
        return [];
      },
    },
    column: {
      type: Array,
      default() {
        return [];
      },
    },
    searchQuery: {
      type: Object,
      default() {
        return {};
      },
    },
    getTableList: {
      type: Function,
      default() {
        return () => {};
      },
    },
    selectable: {
      type: Function,
      default() {
        return () => true;
      },
    },
    isaCreateTime:{
      type: Boolean,
      default() {
        return true ;
      },
    }
  },
  // dicts:['meins'],
  data() {
    const searchDataList = this.isaCreateTime ? [...this.searchData,...aCreateTime] : this.searchData
    return {
      loading: false,
      searchDataList:searchDataList ,
      columnList: [...this.column,...aCreateTime],
      paginationProps: {
        total: 0,
        pageSize: 10,
        pageNum: 1,
      },
      tableData: [],
      params: {},
      searchTableInfo: this.searchInfo,
    };
  },
  computed: {
    pageData() {
      const pageNum = this.paginationProps.pageNum;
      const pageSize = this.paginationProps.pageSize;
      const num1 = (pageNum - 1) * pageSize;
      const num2 = pageNum * pageSize;
      const data = this.tableData.slice(num1, num2);
      return data;
    },
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  watch: {
    searchData(val) {
      this.searchDataList = this.isaCreateTime ? [...val,...aCreateTime] : val
      this.getList();
    },
    column(val) {
      this.columnList = val;
    },
    searchInfo(val) {
      // console.log(val,'val')
      this.searchTableInfo = val;
    },
  },
  created() {
    // if (this.isinitData) {
    //   console.log(this.searchInfo)
    //   this.submitEmits(this.searchInfo);
    // }
  },
  mounted() {
    if (this.isinitData) {
      this.submitEmits(this.searchInfo);
    }

    //  const scrollContainer = document.getElementById('app-main')
    // console.log(scrollContainer.scrollHeight,scrollContainer.scrollTop,'scrollContainer.height')
  },
  methods: {
    submitEmits(data, type) {
      this.params = data;
      this.searchTableInfo = data || {}
      this.getList(1, data);
    },
    submitEmits2(data, type) {
      this.params = data;
      this.getList(1, data);
    },
    async getList(page, queryData = this.searchTableInfo) {
      if (page) this.paginationProps.pageNum = page;

      const param = {
        //  ...this.searchTableInfo,
        ...queryData,
        pageNum: this.paginationProps.pageNum,
        pageSize: this.paginationProps.pageSize,
       
      };

      if(param.createTimeS){
        param.createTimeS = `${param.createTimeS} 00:00:00`
      }
      if(param.createTimeE){
        param.createTimeE = `${param.createTimeE} 23:59:59`
      }

      if(param.docStatus){
        param.docStatusList = param.docStatus

        delete param.docStatus
      }

      try {
        // 查询
        this.loading = true;
        const request = this.getTableList(param);
        let res = null;
        if (request instanceof Promise) {
          res = await request;
        } else {
          res = request;
        }

        this.loading = false;
        if (!res || res.code !== 200 || !res.rows) {
          return;
        }

        if (Array.isArray(res.rows)) {
          const rows = res.rows;
          const _rows = setTableDictValue(
            this.columnList,
            rows,
            this.dictDataOptions
          );
          this.tableData = _rows;
          this.paginationProps.total = res.total;
        } else if (res.data.rows && Array.isArray(res.data.rows)) {
          const rows = res.data.rows;
          const _rows = setTableDictValue(
            this.columnList,
            rows,
            this.dictDataOptions
          );

         
          this.tableData = _rows;
          this.paginationProps.total = res.data.rows;
        }
      } catch (error) {
        console.log(error,'error')
        this.loading = false;
        this.tableData = [];
        this.paginationProps = {
          total: 0,
          pageSize: 10,
          pageNum: 1,
        };
      }
    },
    handleSizeChange({ page, size }) {
      this.paginationProps = {
        ...this.paginationProps,
        pageNum:page,
        pageSize:size
      }
      if (this.isPage) {
        this.getList();
      }

      // this.$emit("changePage", { page, size });
    },
    select(selection, row) {
      this.$emit("select", selection, row);
    },
    handleSelectionChange(val) {
      this.$emit("selection-change", val);
    },
    handleCurrentChange(val) {
      // this.currentRow = val;
      this.$emit("current-change", val);
    },
  
    refreshList(searchData) {
      this.getList(1, { ...this.searchTableInfo,...searchData});
    },
    cellDblclick(	row, column, cell, event){
       this.$emit("cell-dblclick", row, column, cell, event);
    }
  },
};
</script>

<style lang="scss" scoped>
.UniformCss {
  margin-bottom: 20px;
}
</style>
