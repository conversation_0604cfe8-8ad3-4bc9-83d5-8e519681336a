import { getColumnsOptions } from "@/utils/location";
export const setColumns = (data,Key) => {
  let locKey = Key || location.pathname;

  let columnsData = [...data].filter(({ hiddenTable = false }) => !hiddenTable);
  let htmlWidth =
    document.documentElement.clientWidth || document.body.clientWidth;

  const old = getColumnsOptions(locKey);

  if (locKey && old) {
    const locData = old;
    let ab = old.map(({ prop }) => prop);

    const sortData = columnsData
      .map((item) => {
        const index = ab.findIndex((p) => p === item.prop);
        if (index > -1) {
          item.visible = locData[index].visible;
        } else {
          index === 1000;
        }
        item.sortNum = index;
        return item;
      })
      .sort((a, b) => {
        return a.sortNum - b.sortNum;
      });

    columnsData = [...sortData];
  }

  if (htmlWidth < 500) {
    columnsData = columnsData.map((item) => {
      item.width = undefined;

      return item;
    });
    return columnsData;
  }
  return columnsData;
};
