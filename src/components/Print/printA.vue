<template>
  <div>
    <el-dialog
      :visible.sync="printDialogVisible"
      :class="'printDialog'"
      width="90%"
      :destroy-on-close="true"
    >
      <Print
        :info="printData"
        :type="type"
        :TableConfig="TableList"
        :tableFooterConfig="tableFooter"
        :headerConfig="header"
        :footerConfig="footer"
        :title="printTitile"
      />
      <div class="print-actions" slot="footer" ref="content">
        <el-button type="primary" @click="printHand" icon="el-icon-printer"
          >打印</el-button
        >
        <el-button @click="close" icon="el-icon-close">取消</el-button>
        <!-- <el-button icon="el-icon-download" @click="convertToPDF">导出PDF</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ComSearch from "../ComSearch/index.vue";
import ComTable from "../ComTable/index.vue";
import Print from "./index.vue";
import { printInfo } from "@/api/sales/salesDelivery/salesDelivery";
import html2pdf from "html2pdf.js";
import { setRem } from "@/utils/rem";
import {
  headerConfig,
  tableFooterConfig,
  TableConfig,
  footerConfig,
  getData,
  setStyles,
  setWidth,
} from "./config";
// import { getPdf } from './getPdf'
export default {
  components: {
    ComSearch,
    ComTable,
    Print,
  },

  props: {
    headerConfig: {
      type: Array,
      default() {
        return headerConfig;
      },
    },
    tableFooterConfig: {
      type: Array,
      default() {
        return tableFooterConfig;
      },
    },
    TableConfig: {
      type: Array,
      default() {
        return TableConfig;
      },
    },

    footerConfig: {
      type: Array,
      default() {
        return footerConfig;
      },
    },
    title: {
      type: String,
      default() {
        return "";
      },
    },
    info: {
      type: Array,
      default() {
        return [];
      },
    },
    ids: {
      type: String,
      default() {
        return "";
      },
    },
    printInfoData: {
      type: Function,
    },
  },
  name: "PrintA",
  data() {
    return {
      // printList,
      printData: [],
      printDialogVisible: false,
      tableProps: {
        border: false,
        height: "254px",
      },
      TableList: this.$props.TableConfig,
      tableFooter: this.$props.tableFooterConfig,
      header: this.$props.headerConfig,
      footer: this.$props.footerConfig,
      fixHeader: "",
      printItemClass: "printItem",
      showPrint: false,
      type: "",
      printTitile:this.$props.title
    };
  },
  watch: {
    headerConfig(val) {
      this.header = val;
    },
    tableFooterConfig(val) {
      this.tableFooter = val;
    },
    TableConfig(val) {
      this.TableList = val;
    },
    footerConfig(val) {
      this.footer = val;
    },
  },
  dicts: ["zterm"],
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  mounted() {
    window.addEventListener("keydown", this.getKeydown);
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.getKeydown);
  },
  methods: {
    getKeydown(event) {
      if (event.ctrlKey && event.key === "p") {
        event.preventDefault(); // 阻止默认的打印对话框弹出
        this.printHand2();
      }
    },
    printInfo(ids, type = "",title) {
      if (this.loading) return;
      this.type = type;
      this.printTitile = title
      // console.log(type,'yyyy')
      if (!ids) return;
      if (ids.indexOf(",") > -1) {
        ids = ids.split(",");
      } else {
        ids = [ids];
      }

      this.TableList = JSON.parse(JSON.stringify(this.TableConfig)).filter(
        ({ prop }) => {
          if (type !== "money") {
            return !["amount", "netpr"].includes(prop);
          }
          return true;
        }
      );

      this.tableFooter = JSON.parse(JSON.stringify(this.tableFooterConfig))
        .filter(({ prop }) => {
          if (type !== "money") {
            return !["monenyxiaoji", "monenytotal"].includes(prop);
          }
          return true;
        })
        .map((item2) => {
          if (type !== "money") {
            item2.span = 12;
          }
          return item2;
        });

      const getFun = ids.map((deliveryHeadId) => {
        return printInfo(deliveryHeadId);
      });
      Promise.allSettled(getFun).then((results) => {
        const resData = results
          .filter(({ status }) => status === "fulfilled")
          .map(({ value }) => value);
        let data = getData(resData, this.dictDataOptions);
        this.printData = data;
        this.loading = false;
      });
      // .then((res) => {
      //   let data = getData(res, this.dictDataOptions);
      //   this.printData = data;
      //   this.loading = false;
      // })
      // .catch((error) => {
      //   console.log(error);
      //   this.loading = false;
      // });
    },

    printInfo2(ids, type = "") {
      if (this.loading) return;
      this.type = type;
      // console.log(type,'yyyy')
      if (!ids) return;
      if (ids.indexOf(",") > -1) {
        ids = ids.split(",");
      } else {
        ids = [ids];
      }

      const getFun = ids.map((deliveryHeadId) => {
        return this.printInfoData(deliveryHeadId);
      });
      Promise.allSettled(getFun).then((results) => {
        const resData = results
          .filter(({ status }) => status === "fulfilled")
          .map(({ value }) => value);
        let data = getData(resData, this.dictDataOptions);
        this.printData = data;
        this.loading = false;
      });
    },
    handleScroll(event) {
      const scrollTop = event.target.scrollTop;
      if (scrollTop > 100) {
        this.fixHeader = "fixHeader";
      } else {
        this.fixHeader = "";
      }
    },
    printHand() {
      const printApp = document.getElementById("print-app");
      const app = document.getElementById("app");
      const printContent = document.getElementById("print").innerHTML;
      printApp.innerHTML = printContent;

      app.style.display = "none";
      printApp.style.display = "block";
      this.$nextTick().then(() => {
        setStyles(this.type, this.printData);
        this.printHand2();
      });
    },
    printHand2() {
      const printApp = document.getElementById("print-app");
      const app = document.getElementById("app");
      const printContent = document.getElementById("print").innerHTML;
      printApp.innerHTML = printContent;
      const htmlDom = document.getElementsByTagName("html")[0];

      if (this.type === 'money') {
        htmlDom.style.fontSize = 14 + "px";
      } else {
        htmlDom.style.fontSize = 16 + "px";
      }

      app.style.display = "none";
      printApp.style.display = "block";
      this.$nextTick().then(() => {
        window.print();
        setRem();
        // console.log
        if(this.type !== 'inventory'){
          setWidth();
        }
        const printApp = document.getElementById("print-app");
        printApp.innerHTML = "";
        app.style.display = "block";
        printApp.style.display = "none";
        this.close()
      });
    },
    close() {
      this.printDialogVisible = false;
      const printApp = document.getElementById("print-app");
      printApp.innerHTML = "";
    },
    convertToPDF() {
      const printContent = document.getElementById("print");
      const printBody = document.getElementsByClassName("printBody");
      printBody.forEach((item) => {
        item.classList.remove("printItem");
      });
      const content = printContent;

      const options = {
        margin: 0,
        filename: "mypdf.pdf",
        image: { type: "png" },
        pagebreak: {
          mode: ["css"],
        },
        html2canvas: { scale: 2 },
        jsPDF: { unit: "mm", format: "a5", orientation: "landscape" },
      };

      this.$nextTick(() => {
        html2pdf()
          .set(options)
          .from(content)
          .save()
          .toPdf()
          .get("pdf")
          .then((res) => {
            const printBody = document.getElementsByClassName("printBody");
            printBody.forEach((item) => {
              item.classList.add("printItem");
            });
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.printDialog2 {
  background: #fff;
  page-break-after: always;
}
.printDialog {
  page-break-after: always;

  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }

  ::v-deep .el-dialog__body {
    height: calc(100vh - 100px);
    overflow: auto;
  }
}
</style>

<style lang="scss">
#showPrint {
  width: 100%;
  height: 100%;
  background: #fff;
  position: fixed;
  overflow: auto;
  top: 0px;
  left: 0px;
  // overflow: auto;
  z-index: 30000;
}

#print-app {
  ::v-deep .el-table__cell .cell {
    width: 100% !important;
  }
}
</style>

<style media="print" lang="scss">
@media print {
  #showPrint {
    height: auto !important;
    overflow: auto;
  }

  .v-modal {
    display: none !important;
  }
}
</style>
