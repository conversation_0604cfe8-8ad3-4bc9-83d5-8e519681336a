<template>
  <div
    id="print"
    ref="printSection"
    class="print_root"
    v-show="show"
    style="page-break-after: always; border: 1px solid transparent"
  >
    <template v-for="(printList, indexRoot) in printData">
      <div
        v-for="(item, index) in printList"
        :key="indexRoot + index"
        :class="`printBody ${printItemClass}`"
        style="page-break-after: always; border: 1px solid transparent"
      >
        <h1>{{ item.companyName + title }}</h1>
        <ComSearch
          :searchData="headerConfig"
          :searchInfo="item"
          :showNum="headerConfig.length"
          :showHeader="false"
          :labelWidth="''"
        />

        <ComTable
          class="printTable"
          :tableProps="tableProps"
          :column="tableList"
          :tableData="item.tableList"
          :border="false"
          :isSelection="false"
          :showOverflowTooltip="false"
          :typeIndex="false"
          :showHeaderConfig="false"
        ></ComTable>

        <ComSearch
          :searchInfo="item"
          :searchData="totalData"
          :showHeader="false"
          :showNum="tableFooterConfig.length"
          :labelWidth="''"
          class="tableTatal"
        />
        <ComSearch
          class="fomFooter"
          :searchInfo="item"
          :searchData="footerConfig2"
          :showHeader="false"
          :labelWidth="''"
          :showNum="footerConfig2.length"
        />
      </div>
    </template>
  </div>
</template>

<script>
import ComSearch from "../ComSearch/index.vue";
import ComTable from "../ComTable/index.vue";
// import Print from "vue-print-nb";
import {
  headerConfig,
  tableFooterConfig,
  TableConfig,
  footerConfig,
} from "./config";
// import { printListAll } from "./config2";
export default {
  components: {
    ComSearch,
    ComTable,
  },

  props: {
    type:{
      type:String,
      default:''
    },
    headerConfig: {
      type: Array,
      default() {
        return headerConfig;
      },
    },
    tableFooterConfig: {
      type: Array,
      default() {
        return tableFooterConfig;
      },
    },
    TableConfig: {
      type: Array,
      default() {
        return TableConfig;
      },
    },

    footerConfig: {
      type: Array,
      default() {
        return footerConfig;
      },
    },
    title: {
      type: String,
      default() {
        return "提货单";
      },
    },
    info: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  name: "Print",
  data() {
    return {
      show: true,
      // printList,
      tableProps: {
        border: false,
        // height: "254px",
        'highlight-current-row':false
      },
      fixHeader: "",
      printItemClass: "printItem",
    };
  },
  computed: {
    printData() {
       return this.info;
    },
    tableList(){
      return this.TableConfig
    },
    totalData(){
      return this.tableFooterConfig
    },
    footerConfig2(){
      return this.footerConfig
    }

  },

  methods: {
 
  },
};
</script>

<style></style>

<style lang="scss" scoped media="print">
@page {
  size: auto;
  margin: 8mm 6mm;


}

html {
  /*打印缩放，防止显示不全*/
  // zoom: 50%;
}

/*避免出现空白尾页*/
html,
body {
  height: 98% !important;
  margin: 0px;
  padding: 0px;
  overflow: hidden;
}

::v-deep .UniformCss .el-form-item__label {
  text-align: left;
}

.printBody {
  padding: 6px 20px;

  color: #000;
  ::v-deep .UniformCss .el-col {
    height: unset;
  }
  ::v-deep .el-form-item--small.el-form-item {
    margin-bottom: 0px;
  }

  ::v-deep .UniformCss .el-form-item__label {
    margin-top: 0px;
    line-height: 24px;
  }
  ::v-deep .el-form-item--small .el-form-item__content {
    line-height: 24px;
  }

  ::v-deep .UniformCss .el-col {
    margin-bottom: 0px;
  }

  ::v-deep .el-table--scrollable-x .el-table__body-wrapper {
    overflow-x: hidden;
  }

  ::v-deep .el-table::before,
  ::v-deep .el-table--group::after,
  ::v-deep .el-table--border::after {
    background-color: unset;
    height: 0;
  }


  ::v-deep [prop="vbelnDr"] ,::v-deep [prop="vbeln"],::v-deep [prop="bstkd"]{
   font-weight: 550;
  }
  ::v-deep [prop = "hMemo"],::v-deep [prop ="harvestAddress"]{
    line-height: 16px !important;
    display: block;
  }

   ::v-deep .UniformCss .el-form-item__label{
    color: #000;
   }
   ::v-deep .el-table, ::v-deep .el-table .el-table__header-wrapper th, ::v-deep .el-table .el-table__fixed-header-wrapper th{
       color: #000;
  }

  ::v-deep .el-table .cell{
    line-height: unset;
  }

  ::v-deep .el-table__body tr.current-row > td.el-table__cell, ::v-deep .el-table__body tr.selection-row > td.el-table__cell{
    background: unset;

  }

  // ::v-deep .el-table__body{
  //   min-height: 254px;
  // }


  ::v-deep [for="tishi"]{
    margin-left: 20px;
  }

}
.tableTatal {
  // width: 50%;
  display: flex;
  justify-content: flex-end;
  margin-top: 5px 0px;
  border-top: 1px dashed;
  margin-right: 50px;
}
.printTable {
  ::v-deep .el-table--border .el-table__cell {
    border-right: 0px;
  }

  ::v-deep .el-table--border {
    border: 0px;
  }
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border-bottom: 1px solid #000;
  }
  ::v-deep .el-table .el-table__header-wrapper th,
  ::v-deep .el-table .el-table__fixed-header-wrapper th {
    background-color: unset;
  }

  ::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell,
  ::v-deep .el-table .el-table__header-wrapper th {
    text-align: left;
  }

  ::v-deep .el-table--medium .el-table__cell {
    padding-bottom: 0px;
  }

  // ::v-deep .el-table th.el-table__cell.is-leaf,
  ::v-deep .el-table td.el-table__cell {
    border-bottom: unset;
  }

  ::v-deep .el-table--medium .el-table__cell {
    padding: 0px 0;
  }

  ::v-deep .el-table__empty-text {
    display: none;
  }
  ::v-deep .el-table .el-table__header-wrapper th {
    height: unset;
  }

  ::v-deep .el-table__body-wrapper{
    min-height: 280px !important;
  }


}

.fomFooter{
  // top: -14px;
 }


h1 {
  text-align: center;
  font-size: 18px;
  margin-top: 10px;
  margin-bottom: 0px;
}
</style>

<style media="print">

.printItem {
  margin-bottom: 20px;
  /* box-shadow: 3px 3px 3px 3px rgba(0, 0, 0, 0.5); */
  /* box-shadow: 3px 3px 3px 3px red; */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
#app {
  padding: 20px;
}
@media print {
  @page {
    size: auto;
    margin: 8mm 6mm;
  }
  html {
    /*打印缩放，防止显示不全*/
    zoom: 96%;
    font-size: 18px;
  }

  .el-table {
    table-layout: auto !important;
    width: 100vw !important;
    /* zoom: 80%; */
  }
  #print .el-table__header-wrapper .el-table__header {
    width: 100% !important;
  }
  #print .el-table__body-wrapper .el-table__body {
    width: 100% !important;
    height: 280px !important;
  }



   /* 打印时确保表格不换行 */
 /* #print .el-table__body-wrapper td, #print .el-table__body-wrapper th {
    white-space: nowrap !important;
    font-size: 16pt !important;
  } */

  .el-table_1_column_1 {
    width: 50px !important;
  }

  .el-table_1_column_2 {
    width: 160px !important;
  }

  .textSpan {
    display: block;
    /* white-space: pre-wrap !important; */
    /* font-size: 12px !important; */
  }

  .el-form-item {
    /* white-space: pre-wrap !important; */
  }

  /* .el-table--border .el-table__cell, .el-table__body-wrapper td {
      border: solid 1px #f2f2f2;
    }
    td.el-table__cell{
      border: solid 1px #f2f2f2;
    } */

  #preview-area-container #print-app .printTable .el-table {
    width: 100% !important;
  }

  #preview-area-container #print-app .printTable .el-table .el-table__header,
  #preview-area-container #print-app .printTable .el-table .el-table__body {
    width: 100% !important;
  }

  #preview-area-container #print-app .el-table .has-gutter .el-table__cell {
    border-bottom: 2px solid #000 !important;
}


  .printItem {
  margin-bottom: 0px;
  box-shadow: unset;
 }

 .printBody .UniformCss .el-col,.UniformCss .el-form-item__label{
  font-weight: 600 !important;
 }
 h1{
  font-weight: 700 !important;
 }

 .fomFooter{
  /* top: -14px; */
 }
}

.printBtn {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
}

.printHideCss {
  display: none;
}


</style>

<style lang="scss">
#print-app .printTable .el-table {
  width: 100% !important;
}

#print-app .printTable .el-table .el-table__header,
#print-app .printTable .el-table .el-table__body {
  width: 100% !important;
}
</style>
