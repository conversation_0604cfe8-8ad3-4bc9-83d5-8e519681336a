export const headerConfig = [
  {
    label: "客户PO号",
    prop: "po",
  },
  {
    label: "客户",
    prop: "poName",
  },

  {
    label: "提货单号",
    prop: "no",
  },

  {
    label: "销售人员",
    prop: "salesName",
  },

  {
    label: "联系电话",
    prop: "tel",
  },

  {
    label: "联系人",
    prop: "telName",
  },

  {
    label: "地址简称",
    prop: "address",
    span: 16,
  },

  {
    label: "发运方式",
    prop: "type",
  },

  {
    label: "收货地址",
    prop: "address2",
    span: 24,
  },

  {
    label: "付款条件",
    prop: "tjian",
    span: 5,
  },

  {
    label: "仓储组",
    prop: "cchu",
    span: 4,
  },

  {
    label: "销售订单号",
    prop: "dingdanhao",
    span: 6,
  },

  {
    label: "熏蒸",
    prop: "xunzheng",
    span: 4,
  },

  {
    label: "",
    prop: "yeshu",
    span: 5,
    class: "yeshu",
  },
].map((item) => {
  item.type = "text";
  return item;
});

export const TableConfig = [
  {
    label: "序号",
    prop: "xuhao",
    width: 60,
  },
  {
    label: "图号",
    prop: "tuhao",
    width:160
  },

  {
    label: "物料名称",
    prop: "name",
  },

  {
    label: "数量",
    prop: "num",
  },

  {
    label: "重量(Kg)",
    prop: "kg",
  },
  {
    label: "字库",
    prop: "ziku",
  },
  {
    label: "扫码",
    prop: "shaoma",
  },

  {
    label: "货位",
    prop: "huowei",
  },
];

export const tableFooterConfig = [
  {
    label: "数量小计",
    prop: "numxiaoji",
    span: 12,
  },

  {
    label: "合重⼩计",
    prop: "kgxiaoji",
    span: 12,
  },
  {
    label: "数量总计",
    prop: "numtotal",
    span: 12,
  },

  {
    label: "合重总计",
    prop: "kgtotal",
    span: 12,
  },
].map((item) => {
  item.type = "text";
  return item;
});

export const footerConfig = [
  {
    label: "挑库通知日期",
    prop: "riqi",
    span: 24,
  },

  {
    label: "发运说明",
    prop: "shuoming",
    span: 24,
  },

  {
    label: "主管",
    prop: "zhuzguan",
    span: 4,
  },

  {
    label: "业务",
    prop: "yewu",
    span: 4,
  },

  {
    label: "出纳",
    prop: "chuna",
    span: 4,
  },

  {
    label: "经办人",
    prop: "jingbanren",
    span: 4,
  },

  {
    label: "复核人",
    prop: "fuhe",
    span: 4,
  },

  {
    label: "提货人",
    prop: "tihuoren",
    span: 4,
  },

  {
    label: "温馨提示",
    prop: "tishi",
    span: 24,
  },
].map((item) => {
  item.type = "text";
  return item;
});

const objData = {
  title: "⼴西⽟柴机器专卖发展有限公司销售提货单",
  po: "20702250700545",
  poName: "武汉⽟柴机器专卖发展有限公司",
  no: "1001020250709994",
  salesName: "陈祖林",
  tel: "15307295067",
  telName: "陈丽",
  address: "武汉⽟柴机器专卖发展有限公司",
  type: "汽⻋托运",
  address2: "湖北省武汉市东西湖区⼩⽲实业(⻝品⼀路北35号)",
  tjian: "挂帐",
  cchu: "G02 ",
  dingdanhao: "1000100185907",
  xunzheng: "N",
  yeshu: "",
  numxiaoji: "",
  kgxiaoji: "",
  numtotal: "",
  kgtotal: "",
  riqi: "2025-07-29 17:47:26",
  shuoming: "7⽉29⽇加单，⽋缺发",
  zhuzguan: "徐誉华",
  yewu: "陈祖林",
  chuna: "",
  jingbanren: "",
  fuhe: "",
  tihuoren: "",
  tishi:
    "此单为退换货依据，请勿丢失；当⽉凭此单开发票，跨⽉不受理；零售15天后不办理退货",
};

const getData = () => {
  let arr = [];
  for (let i = 0; i < 16; i++) {
    arr.push({
      xuhao: i + 1,
      tuhao: "130R00-3708100" + i,
      name: "颗粒过滤器" + i,
      num: 1,
      kg: 1.2,
      ziku: 10010,
      shaoma: "Y",
      huowei: "",
    });
  }

  return arr;
};

export const tableList = getData();

function chunkArray(arr, chunkSize) {
  let result = [];
  for (let i = 0; i < arr.length; i += chunkSize) {
    result.push(arr.slice(i, i + chunkSize));
  }
  return result;
}

let numtotal = 0;
let kgtotal = 0;

tableList.forEach((item) => {
  numtotal += item.num * 1 || 0;
  kgtotal += item.kg * 1 || 0;
});

// 示例
const chunkedArray = chunkArray(tableList, 10);

export const printList = chunkedArray.map((itemList, index) => {
  let obj = objData;
  let numxiaoji = 0;
  let kgxiaoji = 0;

  itemList.forEach((item) => {
    numxiaoji += item.num * 1 || 0;
    kgxiaoji += item.kg * 1 || 0;
  });

  obj = {
    ...obj,
    numxiaoji,
    kgxiaoji:parseFloat(kgxiaoji.toFixed(1)),
    numtotal,
    kgtotal:parseFloat(kgtotal.toFixed(1)),
    yeshu: `共${chunkArray.length}⻚NO.${index + 1}` ,
    tableList: itemList,
  };

  return obj;
});


export const printListAll = [
  printList,
  printList  
]


console.log(printListAll,'printListAll')
