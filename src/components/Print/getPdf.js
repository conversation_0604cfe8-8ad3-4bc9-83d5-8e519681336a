import html2Canvas from "html2canvas";
import JsPDF from "jspdf";

export const getPdf = (reportName, isDownload = true) => {
  //     var target = document.getElementsByClassName("right-aside")[0];
  // target.style.background = "#FFFFFF";
  return new Promise((resolve, reject) => {
    var title = reportName;
    html2Canvas(document.getElementById("print"), {
      allowTaint: true,
    })
      .then((canvas) => {
        try {
          let contentWidth = canvas.width;
          let contentHeight = canvas.height;
          //一页pdf显示html页面生成的canvas高度;
          //   对应‌756点×567点‌
          //   let pageHeight = contentWidth / 592.28 * 841.89
          let pageHeight = contentWidth / (756.0 * 567.0);
          //
          //未生成pdf的html页面高度
          let leftHeight = contentHeight;
          //页面偏移
          let position = 0;
          //   1480×2016‌
          //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
          let imgWidth = 756.0;
          let imgHeight = (756.0 / contentWidth) * contentHeight;
          // let imgWidth = 2016
          //   let imgHeight = 2016 / contentWidth * contentHeight
          let pageData = canvas.toDataURL("image/jpeg", 1.0);
          let PDF = new JsPDF("landscape", "pt", "a5");
          //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
          //当内容未超过pdf一页显示的范围，无需分页
          if (leftHeight < pageHeight) {
            PDF.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
          } else {
            while (leftHeight > 0) {
              PDF.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
              leftHeight -= pageHeight;
              position -= 567.0;
              //避免添加空白页
              if (leftHeight > 0) {
                PDF.addPage();
              }
            }
          }
          if (isDownload) {
            PDF.save(title + ".pdf");
          }
        } catch (error) {
          console.log(error, "error");
        }
        // 删除本地存储的base64字段
        var pdfData = PDF.output("datauristring"); //获取base64Pdf
        resolve(pdfData);
      })
      .catch((error) => {
        console.log(error, "error");
        reject(error);
      });
  });
};
