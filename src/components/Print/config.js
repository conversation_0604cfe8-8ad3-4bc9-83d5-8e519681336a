import { formatNowDate, setTableDictValue } from "@/utils/index.js";

export const headerConfig = [
  {
    label: "客户PO号",
    prop: "bstkd",
  },
  {
    label: "客户",
    prop: "name1",
  },

  {
    label: "提货单号",
    prop: "vbelnDr",
  },

  {
    label: "销售人员",
    prop: "salesPerson",
  },

  {
    label: "联系电话",
    prop: "telf1",
  },

  {
    label: "联系人",
    prop: "ihrez",
    otherBind: (val) => {
      return {
        style: {
          // "font-size": val && val.length > 6 ? "12px" : "14px",
          "white-space": "nowrap",
        },
      };
    },
  },

  {
    label: "地址简称",
    prop: "address",
    span: 16,
  },

  {
    label: "发运方式",
    prop: "shipMethod",
    optionsCode: "ship_method",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "shipMethod",
      },
    ],
  },

  {
    label: "收货地址",
    prop: "harvestAddress",
    span: 24,
  },

  {
    label: "付款条件",
    prop: "zterm",
    optionsCode: "zterm",
    span: 6,
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "zterm",
      },
    ],
  },

  {
    label: "仓储组",
    prop: "ware",
    span: 4,
  },

  {
    label: "销售订单号",
    prop: "vbeln",
    span: 6,
  },

  {
    label: "熏蒸",
    prop: "fumigation",
    span: 3,
  },

  {
    label: "",
    prop: "yeshu",
    span: 5,
    class: "yeshu",
  },
].map((item) => {
  item.type = "text";
  return item;
});

export const TableConfig = [
  {
    label: "序号",
    prop: "xuhao",
    width: 60,
  },
  {
    label: "图号",
    prop: "ZZMATNR",
    width: 200,
    // width:160
  },

  {
    label: "物料名称",
    prop: "MAKTX_ZH",
    width: 200,
  },

  {
    label: "数量",
    prop: "KWMENG",
    width: 60,
  },

  {
    label: "销售单价(元)",
    prop: "netpr",
    width: 100,

    // width: 60,
  },
  {
    label: "总价(元)",
    prop: "amount",

    // width: 60,
  },
  {
    label: "重量(Kg)",
    prop: "BRGEW",
  },
  {
    label: "子库",
    prop: "LGORT",
    // dictOptionCode: "LGORT",
    // backProps: [
    //   {
    //     backProp: "lgort",
    //     backLable: "lgortName",
    //     prop: "LGORT",
    //   },
    // ],
  },
  {
    label: "扫码",
    prop: "scanCode",
  },

  {
    label: "货位",
    prop: "storageLocation",
  },
  {
    label: "标签",
    prop: "tag",
  },
];

export const tableFooterConfig = [
  {
    label: "数量小计",
    prop: "numxiaoji",
    span: 8,
  },

  {
    label: "金额小计",
    prop: "monenyxiaoji",
    span: 8,
  },

  {
    label: "合重⼩计",
    prop: "kgxiaoji",
    span: 8,
  },

  {
    label: "数量总计",
    prop: "numtotal",
    span: 8,
  },

  {
    label: "金额总计",
    prop: "monenytotal",
    span: 8,
  },

  {
    label: "合重总计",
    prop: "kgtotal",
    span: 8,
  },
].map((item) => {
  item.type = "text";
  return item;
});

export const footerConfig = [
  {
    label: "挑库通知日期",
    prop: "riqi",
    span: 24,
  },

  {
    label: "发运说明",
    prop: "hMemo",
    span: 24,
  },

  {
    label: "主管",
    prop: "supervisor",
    span: 4,
  },

  {
    label: "业务",
    prop: "salesPerson",
    span: 4,
  },

  {
    label: "出纳",
    prop: "chuna",
    span: 4,
  },

  {
    label: "经办人",
    prop: "jingbanren",
    span: 4,
  },

  {
    label: "复核人",
    prop: "fuhe",
    span: 4,
  },

  {
    label: "提货人",
    prop: "tihuoren",
    span: 4,
  },

  {
    label: "温馨提示",
    prop: "tishi",
    span: 24,
  },
].map((item) => {
  item.type = "text";
  return item;
});

function chunkArray(arr, chunkSize) {
  let result = [];
  for (let i = 0; i < arr.length; i += chunkSize) {
    result.push(arr.slice(i, i + chunkSize));
  }
  return result;
}

export const getPrintList = (objData = {}) => {
  let objDataTableList = objData.tableList.map((item, index) => {
    item.xuhao = index + 1;
    return item;
  });
  const chunkedArray = chunkArray(objDataTableList, 10);
  // console.log(chunkedArray,'chunkedArray')

  let numtotal = 0;
  let kgtotal = 0;
  let netprTotal2 = 0;

  objDataTableList.forEach((item) => {
    numtotal += item.KWMENG * 1 || 0;
    kgtotal += item.BRGEW * 1 || 0;
    netprTotal2 += item.amount;
  });

  const printList2 = chunkedArray.map((itemList, index) => {
    let obj = objData;
    let numxiaoji = 0;
    let kgxiaoji = 0;
    let monenyxiaoji = 0;

    itemList.forEach((item) => {
      numxiaoji += item.KWMENG * 1 || 0;
      kgxiaoji += item.BRGEW * 1 || 0;
      monenyxiaoji += item.amount;
    });

    obj = {
      ...obj,
      numxiaoji,
      riqi: formatNowDate("sfm"),
      kgxiaoji: parseFloat(kgxiaoji.toFixed(1)),
      numtotal,
      kgtotal: parseFloat(kgtotal.toFixed(1)),
      monenyxiaoji: parseFloat(monenyxiaoji.toFixed(2)),
      monenytotal: parseFloat(netprTotal2.toFixed(2)),
      yeshu: `共${chunkedArray.length}⻚NO.${index + 1}`,
      tableList: itemList,
      tishi:
        "此单为退换货依据，请勿丢失；当⽉凭此单开发票，跨⽉不受理；零售15天后不办理退货",
    };

    return obj;
  });

  return printList2;
};

export const getData = (alist, options) => {
  let newList = [];
  alist.forEach((element) => {
    if (element.code === 200 && element.data) {
      let data = setTableDictValue(
        TableConfig,
        element.data.salesDeliveryLineList,
        options
      ).map((item) => {
        item.netpr = item.netpr || 0;
        return item;
      });

      const obj = {
        ...element.data,

        tableList: data,
      };
      newList.push(obj);
    }
  });

  let list = newList.map((item) => {
    return getPrintList(item);
  });

  return list;
};

export const setStyles = (type) => {
  const printApp = document.getElementById("print");
  const table = printApp.querySelectorAll(".el-table__header,.el-table__body");
  //el-table 打印不全的问题
  for (let i = 0; i < table.length; i++) {
    const tableItem = table[i];
    tableItem.style.width = "100%";
    const child = tableItem.childNodes;
    for (let j = 0; j < child.length; j++) {
      const element = child[j];
      if (element.localName == "colgroup") {
        element.innerHTML = "";
      }
    }
  }
  printApp.querySelectorAll(".has-gutter").forEach((item) => {
    item.childNodes.forEach((item2) => {
      item2.childNodes.forEach((item3, index) => {
        if (type === "money") {
          item3.style.fontSize = "9pt";
          if ([6, 7].includes(index)) {
            item3.style.width = "70px";
          }
          if (index === 1) {
            item3.style.width = "140px";
          }
          if ([2].includes(index)) {
            item3.style.width = "120px";
            item3.style.fontSize = "9pt";
          }
          if ([4, 5].includes(index)) {
            item3.style.width = "90px";
          }
        } else if (type === "inventory") {
          if ([0].includes(index) ) {
            item3.style.width = "60px";
          }
          if ([2, 1].includes(index)) {
            item3.style.width = "150px";
          }
        } else {
          if ([0].includes(index) === 0) {
            item3.style.width = "60px";
          }
          if ([6].includes(index) === 0) {
            item3.style.width = "80px";
          }
          if ([2, 1].includes(index)) {
            item3.style.width = "150px";
          }
          if ([4, 5].includes(index)) {
            item3.style.width = "120px";
          }
        }
      });
    });
  });
  printApp.querySelectorAll(".el-table__row").forEach((item) => {
    item.childNodes.forEach((item3, index) => {
      if (type === "money") {
        item3.style.lineHeight = "16pt";
        item3.style.fontSize = "10pt";
        if ([6, 7].includes(index)) {
          item3.style.width = "70px";
        }
        if (index === 1) {
          item3.style.width = "132px";
          item3.style.fontSize = "9pt";
        }
        if ([2].includes(index)) {
          item3.style.width = "120px";
          item3.style.fontSize = "9pt";
        }
        if ([4, 5].includes(index)) {
          item3.style.width = "90px";
        }
      } else if (type === "inventory") {
        if ([0].includes(index)) {
          item3.style.width = "60px";
        }
         item3.style.fontSize = "11pt";
        if ([2, 1].includes(index)) {
          item3.style.width = "150px";
          item3.style.fontSize = "10pt";
        }
      } else {
        if ([0].includes(index) === 0) {
          item3.style.width = "60px";
        }
        item3.style.lineHeight = "16pt";
        if ([6].includes(index) === 0) {
          item3.style.width = "80px";
        }
        item3.style.fontSize = "11pt";
        if ([2, 1].includes(index)) {
          item3.style.width = "150px";
          item3.style.fontSize = "10pt";
        }
        if ([4, 5].includes(index)) {
          item3.style.width = "120px";
        }
      }
    });
  });
};

export const setWidth = () => {
  const printApp = document.getElementById("print");
  printApp.querySelectorAll(".has-gutter").forEach((item) => {
    item.childNodes.forEach((item2) => {
      item2.childNodes.forEach((item3, index) => {
        item3.style.width = "unset";
        if ([0].includes(index)) {
          item3.style.width = "60px";
        }
        if ([2, 1].includes(index)) {
          item3.style.width = "200px";
        }
      });
    });
  });
  printApp.querySelectorAll(".el-table__row").forEach((item) => {
    item.childNodes.forEach((item3, index) => {
      item3.style.width = "unset";
      if ([0].includes(index)) {
        item3.style.width = "60px";
      }
      if ([2, 1].includes(index)) {
        item3.style.width = "200px";
      }
    });
  });
};
