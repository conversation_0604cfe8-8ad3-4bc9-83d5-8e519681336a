<template>
  <div>
    {{ valueData }}
  </div>
</template>

<script>
import { returnValue } from "@/utils/index.js";
export default {
  name: "DictText",
  props: {
    params: {
      type: Object,
      default: () => {
        return {};
      },
    },
    backProps: {
      type: [Object, Array],
      default: () => {
        return {};
      },
    },
    value: {
      type: [String, Number],
      default: () => {
        return "";
      },
    },
  },
  data() {
    return {
      valueText: "",
    };
  },
  computed: {
    dictDataOptions() {
      const data = this.$store.state.dictData.dictDataOptions;
      // console.log(data, this.$store.state,"data");
      return data;
    },
    valueData() {
      // console.log(this.backProps,'backProps')
      if (!this.backProps) return this.value;
      const text = returnValue(
        this.dictDataOptions[this.backProps.prop],
        this.value,
        this.backProps
      );
      return text;
    },
  },
  
  created() {
    // this.getData();
    // console.log(this.dictDataOptions,'this')
  },
  mounted() {
    // this.getData();
    // console.log(this,'this')
  },
  filters: {},
  methods: {},
};
</script>
<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
