export default {
  data() {
    return {};
  },
  methods: {
    setLineLgort(newdata, oldData) {
      //   console.log(this, "this");
      if (!this.$parent.$refs?.lineTable?.salesOrderLineList) return;

      if (
        newdata?.WERKS &&
        oldData.WERKS &&
        (newdata?.WERKS !== oldData.WERKS || newdata?.werks !== oldData.werks)
      ) {
        // 头部信息重置
        const clearsearchKey = ["LGORT"];

        Object.keys({ ...newdata }).forEach((key) => {
          if (clearsearchKey.includes(key)) {
            const index = [...this.searchData].findIndex(
              ({ prop }) => prop === key
            );

            // console.log(index,key,'key')

            if (this.searchData[index]?.backProps) {
              const {
                backProp = "",
                backLable = "",
                prop,
                name = "",
                propLabels,
              } = this.searchData[index].backProps[0];
              this.dataInfo[key] = undefined;
              if (propLabels && propLabels.length) {
                const [val1, val2] = propLabels;
                this.dataInfo[`${val1},${val2}`] = undefined;
              } else {
                this.dataInfo[`${prop},${backLable}`] = undefined;
              }
            } else {
              this.dataInfo[key] = undefined;
            }
          }
        });

        // 行信息重置

        const salesOrderLineList =
          this.$parent.$refs.lineTable.salesOrderLineList;
        const salesOrderLineCloumn =
          this.$parent.$refs.lineTable.salesOrderLineCloumn;

        const clearListKey = ["LGORT"];
        const _salesOrderLineList = [...salesOrderLineList].map((item) => {
          Object.keys(item).forEach((key) => {
            if (clearListKey.includes(key)) {
              const index = salesOrderLineCloumn.findIndex(
                ({ prop }) => prop === key
              );
              if (index > -1) {
                const {
                  backProp = "",
                  backLable = "",
                  prop,
                  name = "",
                } = salesOrderLineCloumn[index].backProps[0];
                const label = `${prop},${backLable}`;
                item[key] = undefined;
                item[label] = [undefined, undefined];
              } else {
                item[key] = undefined;
              }
            }
          });

          return item;
        });

        this.$parent.$refs.lineTable.salesOrderLineList = _salesOrderLineList;
      }
    },
  },
};
