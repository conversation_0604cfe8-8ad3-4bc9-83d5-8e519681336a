import { listOrgRelation } from "@/api/sales/orgRelation/orgRelation";
import { getRequestData, changeProp } from "./config";
import { getaddSearch } from "./utils";

const changeData = (activeProps, rows, self) => {
  const prop = activeProps.prop;
  let params = {};
  // 公司
  if (["companyCode", "BUKRS"].includes(prop)) {
    let backProps = activeProps.backProps;
    if (rows && rows.length > 1) {
      backProps = backProps.slice(0, 1);
    }
    if (backProps && backProps.length) {
      const row = rows[0];
      params = getaddSearch(activeProps.prop, row, backProps);
    }
  } else {
    const changeProp2 = changeProp[prop] || prop;
    let backProps = activeProps.backProps;
    if (backProps && backProps.length) {
      const row = rows[0];
      // console.log(activeProps,'activeProps')
      if (activeProps.setBackParams) {
        params = activeProps.setBackParams(prop, row, self);
      } else {
        params = getaddSearch(prop, row, backProps);
      }

      if (activeProps?.isSetChildren && activeProps.changechildParams) {
        // params.id= `p${row[prop]}`s
        const pa = activeProps.changechildParams(params, rows, self);
        params = {
          ...pa,
        };
      }
    }
  }

  return params;
};

export const getRequestList = async (self, _prop, oldParams = {}) => {
  try {
    const prop = changeProp[_prop] || _prop;
    const fun = getRequestData[prop];
    let params = {
      ...oldParams,
    };

    const datainfo = self.dataInfo
      ? { ...self.dataInfo }
      : { ...self.editItem };
    const activeProps = self.activeProps;

    if (activeProps.backProps && activeProps.backProps.length) {
      const { backProp = "", backLable = "" } = activeProps.backProps[0] || {};
      if (!/\p{Script=Han}/u.test(datainfo[_prop])) {
        params[backProp] = datainfo[_prop];
      } else {
        params[backLable] = datainfo[_prop];
      }
    } else {
      params[_prop] = datainfo[_prop];
    }

    const res = fun ? await fun(params) : await listOrgRelation(params);

    let setObj = {};
    if (
      prop === "ZZMATNR" &&
      (!activeProps.backProps || activeProps.backProps.length === 0)
    ) {
      activeProps.backProps = [
        {
          backProp: "ZZMATNR",
          backLable: "ZZMATNR",
          prop: "ZZMATNR",
        },
        {
          backProp: "maktxZh",
          backLable: "maktxZh",
          prop: "MAKTX",
        },
        {
          backProp: "MEINS",
          backLable: "MSEHT",
          prop: "MEINS",
        },
        {
          backProp: "ZZMLYLX",
          backLable: "ZZMLYLX",
          prop: "ZZMLYLX",
        },
      ];
    }

    if (activeProps.backProps && activeProps.backProps.length) {
      let backProps = activeProps?.backProps[0] || {};
      const key = `${backProps.prop},${backProps.backLable}`;
      setObj = {
        [key]: ["", ""],
        [backProps.prop]: "",
      };

      // console.log(self.showBtn,res.code === 200 && res.rows && res.rows.length,'res.code === 200 && res.rows && res.rows.length')

      if (res.code === 200 && res.rows && res.rows.length) {
        if (self?.checkSelectMsg && self.checkSelectMsg(res.rows[0], self)) {
          setObj = {
            [key]: ["", ""],
            [backProps.prop]: "",
          };
        } else {
          setObj = changeData(activeProps, res.rows, self);
        }
      } else {
        // 查询时回显
        if (self.showBtn) {
          setObj = {
            [key]: [datainfo[_prop], datainfo[_prop]],
            [backProps.prop]: datainfo[_prop],
          };
        }
      }
    }

    if (self.dataInfo) {
      self.dataInfo = {
        ...datainfo,
        ...setObj,
      };
    }

    if (self.editItem) {
      self.editItem = {
        ...self.editItem,
        ...setObj,
      };
    }

    return setObj;
  } catch (error) {
    console.log(error, "error");
  }
};
