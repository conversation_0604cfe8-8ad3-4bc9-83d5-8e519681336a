import { listOrgRelation } from "@/api/sales/orgRelation/orgRelation";
import { listWerksInfo } from "@/api/sales/werksInfo/werksInfo";
import { listCustomerMaster } from "@/api/master/customerMaster";
import { listMaster } from "@/api/master/master";
import { listSupplierbasic } from "@/api/master/supplierbasic";
import { changeInfoData } from "../LineTable/units";
import { changeProp } from './config'

export const changeParams = (parantdataInfo, self) => {
  // const wrtWERKS = parantdataInfo.WERKS;
  const dataInfo = JSON.parse(JSON.stringify(parantdataInfo))
  
  const info = changeInfoData(dataInfo);
  const changePropName = changeProp[self.activeProps.prop] || self.activeProps.prop;

  let params = {}
  // 工厂
  if (changePropName === "WERKS" || changePropName === "werks") {
    if (info.BUKRS) {
      params.company = info.BUKRS;
    } else if (info.companyCode) {
      params.company = info.companyCode;
    }
  }

  // 库存地点
  if (["LGORT", "zzzzjLgort", "lgort"].includes(changePropName)) {
    if (info.werks) {
      params.werks = info.werks;
    } else if (info.WERKS) {
      params.werks = info.WERKS;
    }

    if (info.unwrk && self.activeProps.prop === "UMLGO") {
      params.werks = info.unwrk;
    }
  }

  // console.log(info,changePropName,'info')

  //成本中心
  if (["KOSTL"].includes(changePropName)) {
    if (info.companyCode) {
      params.bukrs = info.companyCode;
    }
    // console.log(params,'params')
  }

   if (["KUNNR"].includes(changePropName)) {
    if (info?.VKORG || info?.vkorg) { 
      params.VKORG = info?.VKORG || info?.vkorg;
    }
   }

  return params;
};

const obj = {
  prop: "errMsg",
  label: "失败信息",
  type: "textarea",
  span: 24,
  rows: 2,
  hiddenSearch: true,
  attr: {
    disabled: true,
  },
};
export const filterSearchData = (dataList, num) => {
  if (!dataList || dataList.length === 0) return {};

  let list = [...dataList, obj].filter(
    ({ hiddenSearch = "" }) => !hiddenSearch
  );

  const searchOutList = list.filter(({ searchOut = false }) => searchOut) || [];

  const searchdata = list.filter(
    (item) => !item.hiddenSearch && !item.searchOut
  );
  const searchdata1 = searchdata
    .filter((item) => !item.hiddenSearch)
    .slice(0, num);
  const searchdata2 = searchdata
    .filter((item) => !item.hiddenSearch)
    .slice(num);

  const optionsCodes = searchdata
    .filter((item) => item.optionsCode)
    .map((item) => item.optionsCode);

  return {
    searchdata1,
    searchdata2,
    optionsCodes,
    searchOutList,
  };
};

export const getaddSearch = (type, data = {}, backProps) => {
  if (!backProps) {
    return {
      [type]: data[type],
    };
  }
  // console.log(backProps,'oooo')
  let obj = {};
  backProps.forEach((item) => {
    const { prop, backProp, backLable,propLabels = [] } = item;
    // 单个
    if(propLabels && propLabels.length){
    const [ keyProp,label] = propLabels
    const key = `${keyProp},${label}`
       obj = {
        ...obj,
        [keyProp]:data[label],
        // [key]:[data[keyProp],data[label]]
      }
    }else{
      const key = `${prop},${backLable}`;
      obj = {
        ...obj,
        [key]: [data[backProp] || "", data[backLable] || ""],
        [prop]: data[backLable] || "",
      };
    }
    
  });
  return obj;
};

export const closesearch = (backProps, prop) => {
  // 客户编码只删除本身
  const nomoreProp = ["KUNNR"];
  if (!backProps || nomoreProp.includes(prop)) {
    return {
      [prop]: "",
    };
  }

  let obj = {};
  backProps.forEach((item) => {
    const { prop, backProp, backLable } = item;
    const key = `${prop},${backLable}`;
    obj = {
      ...obj,
      [key]: undefined,
      [prop]: "",
    };
  });
  return obj;
};

export const SearchInitkeyValue = (searchColums, dataInfo) => {
  const list = [...searchColums];
  const data = { ...dataInfo };

  list.forEach((item) => {
    const propKey = item.prop;
    if (propKey.indexOf(",") > -1) {
      let arr = [];
      propKey.split(",").forEach((key2) => {
        arr.push(data[key2]);
      });
      data[propKey] = arr[0] ? arr : [];
    }
  });

  // console.log(data,'dadadad')

  return data;
};

export const setinitInfoData = (data = {}, dataInfo = {}, item) => {
  try {
    const { backProps, prop } = item;
    const _backProps = JSON.parse(JSON.stringify(backProps)).slice(0, 1);
    let obj = {};
    // console.log(_backProps,'000000')
    _backProps.forEach((item) => {
      const { backProp = '', backLable = '',propLabels = [],name = '' } = item;
      let key = `${prop},${backLable}`;
      const propValue = dataInfo[prop] || "";
      // console.log(propValue,prop,'vvvvvv')
      const index = data.findIndex((inde) => {
        // console.log(inde[backProp],propValue,'000000')
        return inde[backProp] === propValue;
      });
      let value = dataInfo[backLable] || dataInfo[name] || propValue;

      if(dataInfo[backLable] || dataInfo[name]){
         value = dataInfo[backLable] || dataInfo[name] || propValue; 

      }else if(propLabels && propLabels.length){
        const [va1,va2] = propLabels
        value = dataInfo[va2] ||  value
        key = `${prop},${va2}`
      }else if (index > -1) {
        value =   dataInfo[backLable] || dataInfo[name] || data[index][backLable];
      } else {
        value = propValue;
      }

      obj = {
        ...obj,
        [key]: propValue ? [propValue, value] : "",
        [prop]: value,
      };
    });
    return obj;
  } catch (error) {
    console.log(error, "errop");
    return dataInfo;
  }
};

export const setSubmitEmitsInfo = () => {};

export const getRequestData = {
  LGORT: listWerksInfo,
  zzzzjLgort: listWerksInfo,
  KUNNR: listCustomerMaster,
  ZZMATNR: listMaster,
  LIFNR: listSupplierbasic,
  FLIEF: listSupplierbasic,
  lifnr: listSupplierbasic,
};
