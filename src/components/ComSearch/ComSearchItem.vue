<template>
  <span>
    <el-input
      v-if="item.type === 'number'"
      :class="className"
      clearable
      :placeholder="item.placeholder || '请输入' + item.label"
      @blur="blur(newValue)"
      @input="input($event)"
      v-bind="otherBind(item,row) || item.attr || {}"
      v-model.number="newValue"
      @keyup.enter.native.prevent="keyup(newValue)"
    >
    </el-input>
    <el-input
      :class="item.append ? `${className} activeclose` : `${className}`"
      v-else-if="item.type === 'input' || !item.type"
      v-model="newValue"
      :clearable="!item.append"
      :placeholder="item.placeholder || '请输入' + item.label"
      @blur="blur(newValue)"
      @input="input($event)"
      v-bind="otherBind(item,row) || item.attr || {}"
      @keyup.enter.native.prevent="keyup(newValue)"
    >
      <i
        v-if="
          item.append && newValue && !(item.attr && item.attr.disabled)
        "
        style="cursor: pointer"
        class="el-icon-circle-close"
        slot="suffix"
        @click.stop="closeProp(item)"
      ></i>
      <el-button
        v-if="item.append"
        size="mini"
        @click.stop="handClickSearch(item)"
        slot="append"
        icon="el-icon-search"
        :disabled="((item.attr && item.attr.disabled) || otherBind(item,row).disabled)"
      ></el-button>
    </el-input>
    <el-select
      :class="className"
      v-else-if="item.type === 'select'"
      v-model="newValue"
      :multiple="item.multiple"
      v-bind="item.selectotherBind ? item.selectotherBind(item,row) :item.attr"
      :placeholder="item.placeholder || '请选择' + item.label"
      clearable
      @visible-change="visibleChange"
    >
      <el-option
        v-for="(item2, i2) in item.optionsCode
          ? selectList[item.optionsCode]
          : selectList[item.dictOptionCode] || item.options || []"
        :key="i2"
        v-bind="item.otherBind ? item.otherBind(item2) :item2"
        :value="item2.value || item2[item.backProps[0].backProp]"
        v-show="item.otherBind ? !item.otherBind(item2).disabled : !item2.disabled"
        :label="
          `${item2.label} (${item2.value})` ||
          `${item2[item.backProps[0].backLable]} (${
            item2[item.backProps[0].backProp]
          })`
        "
      />
    </el-select>
    <el-date-picker
      :class="className"
      v-else-if="item.type === 'date'"
      v-model="newValue"
      type="date"
      placeholder="选择日期"
      :value-format="item.format || 'yyyy-MM-dd'"
      v-bind="item.attr"
    >
    </el-date-picker>
    <el-date-picker
      :class="className"
      v-else-if="item.type === 'daterange'"
      v-model="newValue"
      v-bind="item.attr"
      type="daterange"
      range-separator="至"
      unlink-panels
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :value-format="item.format || 'yyyy-MM-dd'"
      :placeholder="item.placeholder || '请选择日期'"
      style="width: 100%"
      :default-time="['00:00:00', '23:59:59']"
    />
    <el-cascader
      :class="className"
      v-else-if="item.type === 'cascader'"
      v-model="newValue"
      v-bind="item.attr"
      :options="item.options"
      clearable
    />
    <span class="textSpan" v-else-if="item.type === 'text'" v-bind="item.otherBind ? item.otherBind(item,newValue) :item">{{ newValue }}</span>
    <el-input
      :class="className"
      v-else-if="item.type === 'textarea'"
      :rows="item.rows || 1"
      v-model="newValue"
      v-bind="item.attr"
      clearable
    >
    </el-input>
  </span>
</template>

<script>
export default {
  name: "ComSearchItem",
  props: {
    className: {
      type: [String],
      default() {
        return "";
      },
    },
    value: {
      type: [String, Number, Array],
      default() {
        return "";
      },
    },
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    row: {
      type: Object,
      default() {
        return {};
      },
    },
    options: {
      type: Array,
      default() {
        return [];
      },
    },
    inputChange: {
      type: [Function],
    },
    tableList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      num: 0,
      newValue: this.value,
    };
  },
  computed: {
    selectList() {
      return this.$store.state.dictData.dictDataOptions;
    },
    lineTable(){
      return this.tableList
    }
    // newValue(){
    //   ret
    // }
  },
  watch: {
    selectList(val) {
      // console.log(val, "va");
    },
    value(val) {
      // console.log(val,this.item,'val')
      this.newValue = val;
    },
    newValue(val) {
      this.$emit("change", val);
    },
  },
  created() {},
  mounted() {
    // console.log(this.newValue,this.item,'newewew')
  },
  methods: {
    handClickSearch(item) {
      this.$emit("handClickSearch", item);
    },
    visibleChange(val) {
      if (val && this.num === 0) {
        // this.selectList  = [...this.selectList]
        // console.log(this.selectList,'00000')
        this.num = 1;
        this.$forceUpdate();
      }
    },
    closeProp(item) {
      if(this.otherBind){
        const disabled = this.otherBind()?.disabled || false
        if(disabled){
          return 
        }
      }
      this.newValue = undefined;
      this.$emit("closeProp", item);
    },
    blur(val) {
      this.$emit("blur", val);
    },
    input(val) {
      // console.log(val,'valllllll')
      if (this.item?.type === "number") {
        const num = Number(val);
        const oldVal = Number(this.newValue);
        let _val = val || `${num}` !== "null" ? num : undefined;

        if (`${num}` === "NaN" || num < 1) {
          _val = `${oldVal}` === "NaN" ? undefined : oldVal;
        }

        if (this?.item?.inputChange) {
          _val = this.item.inputChange(_val, this.newValue, this);
        }
        this.newValue = _val >=0 ? _val : undefined;
      } else if (this?.item.inputChange) {
        const num = this.item.inputChange(val, this.newValue, this);
        this.newValue = num;
      } else {
        this.$emit("input", val, this.newValue);
      }
    },
    otherBind() {
      const attr = this.item?.attr || {};
      let obj = {
        ...attr,
      };

      if (this.item.otherBind) {
        const _obj = this.item.otherBind(this.item, this.row, this);
        obj = {
          ...obj,
          ..._obj,
        };
        // console.log(obj,this.item.prop,'obk')
      }

      return obj;
    },
    keyup() {
      // console.log('aaaaaa')
      this.$emit("keyup");
    },
  },
};
</script>

<style lang="scss" scoped>
.UniformCss {
  .el-form-item {
    width: 100%;
  }

  ::v-deep .el-form-item__label {
    // font-size: 14px;
    line-height: 15px;
    margin-top: 10px;
    color: #666;
    font-weight: 400;
  }

  .el-form-item__content {
    width: calc(100% - 110px);
    display: inline-block;
    margin: 0 !important;

    .el-input,
    .el-select,
    .el-cascader {
      width: 100%;
    }

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
  }

  .el-col {
    height: 38px;
    margin-bottom: 12px;
  }

  .el-input.is-disabled .el-input__inner {
    color: #6a6a6a;
  }

  .search-button {
    text-align: right;
  }
}

.activeclose {
  ::v-deep .el-input__suffix {
    top: 8px;
  }

  ::v-deep .el-input__suffix-inner {
    display: none;
  }
}

.activeclose:hover {
  ::v-deep .el-input__suffix-inner {
    display: block;
  }
}

 .textSpan{
      // white-space: nowrap !important;
      // font-size: 12px !important;
}


</style>
