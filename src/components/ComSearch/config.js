import { listAssemblyPart } from "@/api/master/assemblyPart";
import { listWerksInfo } from "@/api/sales/werksInfo/werksInfo";
import { listCustomerMaster } from "@/api/master/customerMaster";
import { listMaster } from "@/api/master/master";
import { listSupplierbasic } from "@/api/master/supplierbasic";
import { mainrColums, mainrSearchData } from "../LineTable/config";
import { listOrgRelation } from "@/api/sales/orgRelation/orgRelation";
import { listCostCenterConfig } from "@/api/master/costCenterConfig";

export const VKORGcolumns = [
  {
    label: "公司代码",
    type: "input",
    prop: "company",
  },

  {
    label: "公司名称",
    type: "input",
    prop: "companyName",
  },
  {
    label: "工厂",
    type: "input",
    prop: "werks",
  },

  {
    label: "工厂名称",
    type: "input",
    prop: "werksName",
  },

  {
    label: "销售组织",
    type: "input",
    prop: "salesOrg",
  },

  {
    label: "销售组织名称",
    type: "input",
    prop: "salesOrgName",
  },
  
  
   {
    label: "地址",
    type: "input",
    prop: "sasAddress",
    searchOut:true
  },

  {
    label: "联系人",
    type: "input",
    prop: "IHREZ",
  },

  {
    label: "客户联系电话",
    type: "input",
    prop: "TELF1",
  },
  
  {
    label: "发运方式",
    type: "select",
    prop: "shipMethod",
    optionsCode: "ship_method",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "shipMethod",
      },
    ], 
  },
];

export const BUKRScolumns = VKORGcolumns;
// [
//   {
//     label: "公司代码",
//     type: "input",
//     prop: "company",
//   },

//   {
//     label: "公司名称",
//     type: "input",
//     prop: "companyName",
//   },
// ];

export const werksColumns = VKORGcolumns;
// [
//   {
//     label: "工厂",
//     type: "input",
//     prop: "werks",
//   },

//   {
//     label: "工厂名称",
//     type: "input",
//     prop: "werksName",
//   },
// ];

export const lgortColumns = [
  {
    label: "工厂",
    type: "input",
    prop: "werks",
  },

  {
    label: "工厂名称",
    type: "input",
    prop: "werksName",
  },

  {
    label: "库存地点编码",
    type: "input",
    prop: "lgort",
  },

  {
    label: "库存地点名称",
    type: "input",
    prop: "lgortName",
  },
];

export const KUNNRcolumns = [
  {
    label: "客户编码",
    type: "input",
    prop: "KUNNR",
  },

  {
    label: "客户名称",
    type: "input",
    prop: "NAME1",
  },

  {
    label: "客户联系电话",
    type: "input",
    prop: "telNumber",
  },
   {
    label: "联系人",
    type: "input",
    prop: "IHREZ",
  },

  {
    label: "送货详细地址",
    type: "input",
    prop: "STREET",
  },
  {
    label: "价格清单",
    type: "input",
    prop: "PLTYP",
    optionsCode:'pltyp',
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "PLTYP",
      },
    ],
  },
  {
    label: "付款方式",
    type: "select",
    prop: "ZTERM",
    optionsCode:'zterm',
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "ZTERM",
      },
    ], 
  },
  
  {
    label: "发运方式",
    type: "input",
    prop: "KVGR2",
    optionsCode: "ship_method",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "shipMethod",
      },
    ], 
  },
];

export const LIFNRColumns = [
  {
    label: "供应商编号",
    type: "input",
    prop: "LIFNR",
  },

  {
    label: "名称",
    type: "input",
    prop: "NAME1",
  },
];

export const zzzcZzmatnrcolumns = [
  {
    label: "组装件物料号",
    type: "input",
    prop: "zzzcZzmatnr",
  },
  {
    label: "组装件子件序号",
    type: "input",
    prop: "ZZZJXH",
  },
  {
    label: "组装件最小组装数量",
    type: "input",
    prop: "zzzcMengeBom",
  },
  {
    label: "组装件子件物料号",
    type: "input",
    prop: "zzzjZzmatnr",
  },
  {
    label: "组装件子件数量",
    type: "input",
    prop: "zzzjMenge",
  },
  {
    label: "基本单位",
    type: "select",
    prop: "meins",
    optionsCode: "meins",
    backProps: [
      {
        backProp: "value",
        backLable: "label",
        prop: "meins",
      },
    ],
  },
];

export const KOSTLColumns = [
  {
    label: "公司代码",
    type: "input",
    prop: "bukrs",
  },
  {
    label: "公司名称",
    type: "input",
    prop: "butxt",
  },
  {
    label: "成本中心编码",
    type: "input",
    prop: "kostl",
  },
  {
    label: "成本中心短文本",
    type: "input",
    prop: "ktext",
  },
  {
    label: "负责人",
    type: "input",
    prop: "verak",
  },
];

export const ColumnsObj = {
  LGORT: lgortColumns,
  zzzzjLgort: lgortColumns,
  WERKS: werksColumns,
  BUKRS: BUKRScolumns,
  VKORG: VKORGcolumns,
  EKORG: VKORGcolumns,
  KUNNR: KUNNRcolumns,
  ZZMATNR: mainrColums,
  LIFNR: LIFNRColumns,
  FLIEF: LIFNRColumns,
  lifnr: LIFNRColumns,
  zzzcZzmatnr: zzzcZzmatnrcolumns,
  KOSTL: KOSTLColumns,
};

export const SearchDataObj = {
  LGORT: lgortColumns,
  zzzzjLgort: lgortColumns,
  WERKS: werksColumns,
  BUKRS: BUKRScolumns,
  VKORG: VKORGcolumns.slice(0,6),
  EKORG: VKORGcolumns.slice(0,6),
  KUNNR: KUNNRcolumns.slice(0, 2),
  ZZMATNR: mainrColums.slice(0, 2),
  LIFNR: LIFNRColumns,
  FLIEF: LIFNRColumns,
  lifnr: LIFNRColumns,
  zzzcZzmatnr: zzzcZzmatnrcolumns.slice(0, 2),
  KOSTL: KOSTLColumns,
};

export const tableModalobj = {
  KOSTL: {
    key: "KOSTL",
    title: "选择成本中心",
    rowProps: {
      label: "选择成本中心",
      prop: "ktext",
      rowKey: "kostl",
    },
  },
  FLIEF: {
    key: "FLIEF",
    title: "选择供应商",
    rowProps: {
      label: "选择供应商",
      prop: "NAME1",
      rowKey: "LIFNR",
    },
  },
  lifnr: {
    key: "lifnr",
    title: "选择供应商",
    rowProps: {
      label: "选择供应商",
      prop: "NAME1",
      rowKey: "LIFNR",
    },
  },
  LIFNR: {
    key: "LIFNR",
    title: "选择供应商",
    rowProps: {
      label: "选择供应商",
      prop: "NAME1",
      rowKey: "LIFNR",
    },
  },
  ZZMATNR: {
    key: "ZZMATNR",
    title: "选择专卖物料号",
    rowProps: {
      label: "选择专卖物料号",
      prop: "ZZMATNR",
      rowKey: "masterLineId",
    },
  },
  VKORG: {
    key: "VKORG",
    title: "选择销售组织",
    rowProps: {
      label: "销售组织名称",
      prop: "salesOrgName",
      rowKey: "orgId",
    },
  },
  EKORG: {
    key: "EKORG",
    title: "选择采购组织",
    rowProps: {
      label: "选择采购组织",
      prop: "salesOrgName",
      rowKey: "orgId",
    },
  },
  BUKRS: {
    key: "BUKRS",
    title: "选择公司",
    rowProps: {
      prop: "companyName",
      label: "公司名称",
      rowKey: "orgId",
    },
  },
  WERKS: {
    key: "WERKS",
    title: "选择工厂",
    rowProps: {
      prop: "werksName",
      label: "工厂名称",
      rowKey: "orgId",
    },
  },
  LGORT: {
    key: "LGORT",
    title: "选择库存地点",
    rowProps: {
      prop: "lgortName",
      label: "库存地点",
      rowKey: "werksId",
    },
  },
  zzzzjLgort: {
    key: "zzzzjLgort",
    title: "选择库存地点",
    rowProps: {
      prop: "lgortName",
      label: "库存地点",
      rowKey: "werksId",
    },
  },

  KUNNR: {
    key: "KUNNR",
    title: "客户编号",
    rowProps: {
      prop: "KUNNR",
      label: "NAME1",
      rowKey: "stomerMasterId",
    },
  },
  zzzcZzmatnr: {
    key: "zzzcZzmatnr",
    title: "选择组装件物料号",
    rowProps: {
      label: "zzzcZzmatnr",
      prop: "zzzcZzmatnr",
      rowKey: "assemblyPartId",
    },
  },
};

export const componentsModal = Object.values(tableModalobj);

export const getRequestData = {
  LGORT: listWerksInfo,
  zzzzjLgort: listWerksInfo,
  KUNNR: listCustomerMaster,
  ZZMATNR: listMaster,
  LIFNR: listSupplierbasic,
  FLIEF: listSupplierbasic,
  lifnr: listSupplierbasic,
  zzzcZzmatnr: listAssemblyPart,
  VKORG: listOrgRelation,
  KOSTL: listCostCenterConfig,
};

// prop转换取值弹窗
export const changeProp = {
  lgort: "LGORT",
  UMLGO: "LGORT",
  companyCode: "BUKRS",
  werks: "WERKS",
  unwrk: "WERKS",
  vstel: "WERKS",
  bukrs: "BUKRS",
  zzmatnr: "ZZMATNR",
  vkorg: "VKORG",
  kunnr:'KUNNR'
};

export const getTop = () => {
  let htmlWidth =
    document.documentElement.clientWidth || document.body.clientWidth;
  let h2 = document.getElementById("headerNav").scrollHeight;
  let num = h2 - 10 + "px";
  if (htmlWidth < 400) {
    num = h2 - 1 + "px";
    return num;
  }

  if (htmlWidth < 600) {
    num = h2 - 1 + "px";
    return num;
  }
  if (htmlWidth < 900) {
    num = h2 - 10 + "px";
    return num;
  }

  if (htmlWidth < 1000) {
    num = h2 - 20 + "px";
    return num;
  }

  if (htmlWidth < 1600) {
    num = h2 - 10 + "px";
    return num;
  }

  if (htmlWidth < 1920) {
    num = h2 + 20 + "px";
    return num;
  }
  return num;
};
