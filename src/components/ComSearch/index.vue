<template>
  <div class="comSearch">
    <ComModalSearch
      v-if="
        searchData.findIndex(
          ({ prop }) => (changeProp[prop] || prop) === item.key
        ) > -1
      "
      v-for="(item, index) in componentsModal"
      :key="index"
      :ref="'ComModalSearch' + item.key"
      v-bind="item"
      :searchData="SearchDataObj[item.key]"
      :column="ColumnsObj[item.key]"
      :checkSelectMsg="checkSelectMsg"
      @handenOk="handenOk(item.key, $event)"
      :getTableList="getTableList"
    />
    <el-row
      id="searchHeader_root"
      :style="{ textAlign: 'right', top: fixHeader ? top : 0 }"
      v-if="showHeader"
      :class="fixHeader ? 'fixHeader' : ''"
    >
      <el-col>
        <el-row justify="end" :class="'search-button search-button2'">
          <el-button
            :type="'danger'"
            size="mini"
            v-if="cancelShow"
            v-loading="cancelLoading"
            @click="hand('cancel')"
            >取消</el-button
          >
          <slot name="searchButton"></slot>

          <template v-if="showBtn">
            <el-button
              :type="'primary'"
              icon="el-icon-search"
              size="mini"
              @click="onSubmit('search')"
              >查询</el-button
            >
            <el-button
              size="mini"
              :type="btnInfo.reset || 'default'"
              icon="el-icon-refresh-right"
              @click="onSubmit('reset')"
              >重置</el-button
            >
          </template>
          <right-toolbar
            :showSearch.sync="show"
            :showResh="showBtn"
            @queryTable="onSubmit('search')"
          ></right-toolbar>
        </el-row>
      </el-col>
    </el-row>

    <el-collapse-transition>
      <el-form
        @submit.native.prevent
        v-show="show"
        ref="ComSearch"
        :model="dataInfo"
        :rules="rules"
        :label-width="labelWidth2"
        size="small"
        class="UniformCss"
      >
        <el-row>
          <el-col
            v-for="(item, i) in searchData1"
            :key="i"
            :span="span || item.span || 8"
          >
            <el-form-item
              v-if="!item.hiddenSearch"
              :label="item.label"
              :prop="item.prop"
            >
              <ComSearchItem
                :item="{ ...item, multiple: showBtn ? true : false }"
                v-model="dataInfo[item.prop]"
                :row="dataInfo"
                @handClickSearch="handClickSearch"
                @closeProp="closeProp"
                @change="changeInput($event, item.prop, item)"
                @keyup="keyup($event, item, item.prop, 'keyup')"
                @blur="keyup($event, item, item.prop, 'blur')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-collapse v-if="searchData2 && searchData2.length > 0">
          <el-collapse-item :title="collapseTitle" name="1">
            <el-row>
              <el-col
                v-for="(item, i) in searchData2"
                :key="i"
                :span="span || item.span || 8"
              >
                <el-form-item
                  v-if="!item.hiddenSearch"
                  :label="item.label"
                  :prop="item.prop"
                >
                  <ComSearchItem
                    :item="item"
                    v-model="dataInfo[item.prop]"
                    :row="dataInfo"
                    @handClickSearch="handClickSearch"
                    @closeProp="closeProp"
                    @change="changeInput($event, item.prop, item)"
                    @keyup="keyup($event, item, item.prop, 'keyup')"
                    @blur="keyup($event, item, item.prop, 'blur')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>

        <el-row
          style="margin-top: 10px"
          v-if="searchOutList && searchOutList.length > 0"
        >
          <el-col v-for="(item, i) in searchOutList" :key="i" :span="24">
            <el-form-item
              v-if="!item.hiddenSearch"
              :label="item.label"
              :prop="item.prop"
            >
              <el-input
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 4 }"
                v-model="dataInfo[item.prop]"
                v-bind="item.attr"
                clearable
                :placeholder="item.placeholder || '请输入' + item.label"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px" v-if="dataInfo.errMsg">
          <el-col :span="24">
            <el-form-item :label="'返回信息'" :prop="'errMsg'">
              <el-input
                type="textarea"
                :autosize="{ minRows: 1, maxRows: 4 }"
                v-model="dataInfo.errMsg"
                clearable
                disabled
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-collapse-transition>
  </div>
</template>

<script>
import _ from "lodash";
import { listOrgRelation } from "@/api/sales/orgRelation/orgRelation";
import {
  tableModalobj,
  ColumnsObj,
  componentsModal,
  SearchDataObj,
  getRequestData,
  changeProp,
  getTop,
} from "./config";
import {
  filterSearchData,
  getaddSearch,
  setinitInfoData,
  closesearch,
  SearchInitkeyValue,
  changeParams,
} from "./utils";
import { getLookcolumns, trim } from "@/utils/index.js";
import { getRequestList } from "./requestConf";
import IndexMixins from "./index";

export default {
  name: "SearchForm",
  components: {
    ComModalSearch: () => import("@/components/ComModalSearch/index.vue"),
    ComSearchItem: () => import("./ComSearchItem.vue"),
  },
  mixins: [IndexMixins],
  props: {
    cancelShow: {
      type: Boolean,
      default() {
        return false;
      },
    },
    cancelLoading: {
      type: Boolean,
      default() {
        return false;
      },
    },
    handFuntion: {
      type: Function,
      default() {
        return () => {};
      },
    },
    showNum: {
      type: Number,
      default() {
        return 6;
      },
    },
    collapseTitle: {
      type: String,
      default() {
        return "高级搜索";
      },
    },
    labelWidth: {
      type: [String, Number],
      default() {
        return "120px";
      },
    },
    searchInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    searchData: {
      type: Array,
      default() {
        return [];
      },
    },
    btnInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    showHeader: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showBtn: {
      type: Boolean,
      default() {
        return true;
      },
    },
    showSearch: {
      type: Boolean,
      default() {
        return true;
      },
    },
    rules: {
      type: Object,
      default() {
        return {};
      },
    },
    addSearch: {
      type: Function,
      default() {
        return () => {};
      },
    },
    closesearch: {
      type: Function,
    },
    // 输入框选择前校验
    checksearchMsg: {
      type: Function,
    },
    addSearchKey: {
      type: Array,
      default() {
        return [];
      },
    },
    // 选择弹窗时校验
    checkSelectMsg: {
      type: Function,
    },
    // 选择数据后回传
    getSearchcallBack: {
      type: Function,
    },
    // 重设输入的值
    setInput: {
      type: Function,
    },
  },
  data() {
    let num = this.showNum;
    const { searchdata1, searchdata2, optionsCodes } = filterSearchData(
      getLookcolumns(this.$props.searchData, this),
      num
    );
    const selectDataObj = {};
    if (optionsCodes && optionsCodes.length) {
      optionsCodes.forEach((code) => (selectDataObj[code] = []));
    }
    return {
      show: this.$props.showSearch,
      fixHeader: "",
      changeProp,
      SearchDataObj,
      componentsModal,
      ColumnsObj,
      activeProps: {},
      tableModalobj,
      copy: Object.assign({}, {}),
      selectData: Object.assign({}, selectDataObj),
      searchData1: searchdata1,
      searchData2: searchdata2,
      dataInfo: this.$props.searchInfo,
      num: 0,
      span: 0,
      top: 90,
      labelWidth2: this.$props.labelWidth,
    };
  },
  dicts: ["demand_send_status"],
  created() {
    // this.getCodes();
    // console.log(this.searchInfo,'seeeee')
  },

  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
    searchOutList() {
      let num = this.showNum;
      const { searchOutList } = filterSearchData(
        getLookcolumns(this.searchData, this),
        num
      );
      return searchOutList;
    },
  },
  watch: {
    labelWidth(val) {
      this.labelWidth2 = val;
    },
    showSearch(val) {
      this.show = val;
    },
    searchData(val) {
      // console.log(val,'0000000')
      const num = this.showNum;
      const { searchdata1, searchdata2 } = filterSearchData(val, num);
      this.searchData1 = searchdata1;
      this.searchData2 = searchdata2;
    },
    searchInfo(val) {
      this.dataInfo = val;
      // this.copy = Object.assign({}, val);
      // console.log(val,'val')
      setTimeout(() => {
        this.getInfo(val);
      }, 200);
    },
    dataInfo(val) {},
  },
  mounted() {
    this.copy = Object.assign({}, this.searchInfo);
    this.getInfo(this.searchInfo);
    this.handleResize();
    const scrollContainer = document.getElementById("app-main");
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", this.handleScroll);
    }

    window.addEventListener("resize", this.handleResize);
  },

  beforeDestroy() {
    const scrollContainer = document.getElementById("app-main");
    if (scrollContainer) {
      scrollContainer.removeEventListener("scroll", this.handleScroll);
    }

    window.removeEventListener("resize", this.handleResize);
  },

  updated() {
    this.handleResize();
  },
  methods: {
    hand(type) {
      if (type === "cancel") {
        this.$confirm(`是否整单取消数据?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.handFuntion();
        });
      }
    },
    handleScroll(event) {
      const scrollTop = event.target.scrollTop;
      let searchHeader_root = document.getElementById("searchHeader_root");

      if (this.showHeader && scrollTop > 100 && !this.fixHeader) {
        this.fixHeader = "fixHeader";
        searchHeader_root.style.top = getTop();
        // if(this.top === h2)return
        // this.top = h2
      } else if (scrollTop < 30) {
        this.fixHeader = "";
        searchHeader_root.style.top = 0 + "px";
      }
    },
    handleResize(e) {
      this.getWidth();
      let htmlWidth =
        document.documentElement.clientWidth || document.body.clientWidth;

      let span = 0;
      if (htmlWidth < 900) {
        span = 24;
      } else {
        span = 0;
      }

      // console.log(htmlWidth,this.span,span,'00000')
      if (span === this.span) return;

      this.span = span;
    },

    getWidth() {
      let htmlWidth =
        document.documentElement.clientWidth || document.body.clientWidth;
      if (htmlWidth < 500) {
        this.labelWidth2 = "60px";
      } else {
        this.labelWidth2 = this.labelWidth;
      }
    },

    getWeark() {
      let params = changeParams({ ...this.dataInfo }, this);
      // console.log(params,this.dataInfo,'0000')
      if (this.activeProps.setPropParams) {
        let ohterParams = this.activeProps.setPropParams(
          this.activeProps,
          this.dataInfo,
          params
        );
        params = {
          ...params,
          ...ohterParams,
        };
      }

      return params;
    },
    async keyup(e, item, prop, keyType) {
      if (!this.dataInfo[prop] || this.dataInfo[prop] === e) return;
      if (item.append && item.backProps) {
        this.activeProps = item;
        // console.log(this.dataInfo,this,'this', "000001");
        const oldDatainfo = JSON.parse(JSON.stringify(this.dataInfo));
        const paramsObj = this.getWeark();
        const obj = await getRequestList(this, prop, paramsObj);
        this.setLineLgort(this.dataInfo, oldDatainfo);
        if (this.getSearchcallBack) {
          this.getSearchcallBack(obj, this.activeProps);
        }

        // console.log(this.dataInfo, "00000");
      }
      if (this.showBtn && keyType === "keyup") {
        this.onSubmit("search");
      }
    },
    onSubmit(data = "") {
      if (data === "reset") {
        this.dataInfo = Object.assign({}, this.copy);
      } else {
        // console.log('search',this.$refs.ComSearch)
        this.$refs.ComSearch.validate((valid, options) => {
          // console.log( this.dataInfo,"va111111111a");
          if (valid) {
            try {
              const searchInfo2 = this.changeInfoData(data);

              this.$emit("submitEmits", searchInfo2, data, this.dataInfo);
            } catch (error) {
              console.log(error, "err");
            }
          } else {
            console.log("请输入必填项");
            this.$message({
              type: "error",
              message: "请输入必填项!",
            });
            return false;
          }
        });
      }
    },

    changeInfoData(type = "") {
      const _searchInfo = JSON.parse(JSON.stringify({ ...this.dataInfo }));
      const params = {
        ..._searchInfo,
      };
      Object.keys(_searchInfo).forEach((item) => {
        // console.log(item,_searchInfo,'00000')
        if (item.indexOf(",") > -1 && _searchInfo[item]) {
          item.split(",").forEach((key, index) => {
            params[key] = _searchInfo[item][index] || _searchInfo[key];
          });
          if (type !== "search") {
            delete params[item];
          }
        }
      });
      const searchInfo2 = JSON.parse(JSON.stringify(params));
      return searchInfo2;
    },
    getInfo(val) {
      const propNameCode = this.searchData.filter(
        (item) => item.backProps && !item.hiddenSearch
      );
      // console.log(propNameCode, this.searchData, "propNameCode");
      if (propNameCode && propNameCode.length) {
        let dataObj = {};
        let newData = { ...val } || { ...this.dataInfo };
        newData = SearchInitkeyValue(this.searchData, newData);

        propNameCode.forEach((item) => {
          const { dictOptionCode = "", optionsCode = "" } = item;
          let code = dictOptionCode || optionsCode;
          const data = this.dictDataOptions[code] || [];
          // console.log(data,this.dictDataOptions,code,'dadadada')
          // if (!data || (data && data.length === 0)) return;
          const obj = setinitInfoData(data, newData, item);
          dataObj = {
            ...dataObj,
            ...obj,
          };
        });

        const changeNeData = {
          ...newData,
          ...dataObj,
        };

        const isEqual = _.isEqual(newData, changeNeData);

        // console.log(isEqual,changeNeData, "222");
        if (!isEqual) {
          this.dataInfo = {
            ...newData,
            ...dataObj,
          };

          this.$forceUpdate();
        }
      }

      // console.log(this.dataInfo,propNameCode,'0000')
    },
    handClickSearch(item) {
      const changePropName = changeProp[item.prop] || item.prop;
      if (this.checksearchMsg && this.checksearchMsg(item, this.dataInfo))
        return;

      if (
        ["LGORT", "zzzzjLgort", "ZZMATNR"].includes(changePropName) &&
        !(this.dataInfo.WERKS || this.dataInfo.BUKRS || this.dataInfo.werks)
      ) {
        this.$modal.msgError("请先选择组织或工厂");
        return;
      }

      if (["KOSTL"].includes(changePropName) && !this?.dataInfo?.companyCode) {
        this.$modal.msgError("请先公司");
        return;
      }

      this.activeProps = item;
      const list = [...this.componentsModal];
      if (list.findIndex(({ key }) => key === changePropName) > -1) {
        const name = "ComModalSearch" + changePropName;
        const ref = this.$refs[name];
        if (ref.dialogVisible) {
          ref.dialogVisible = true;
          ref.$refs.mainrList.submitEmits();
        } else {
          ref[0].dialogVisible = true;
          if (ref[0]?.$refs?.mainrList?.submitEmits) {
            ref[0].$refs.mainrList.searchTableInfo = {};
            ref[0].currentRow = {};
            ref[0].$refs.mainrList.submitEmits();
          }
        }
      } else {
        this.$emit("handClickSearch", item);
      }
    },
    async getTableList(params) {
      // console.log
      try {
        const changePropName =
          changeProp[this.activeProps.prop] || this.activeProps.prop;
        const objOj = this.getWeark();
        const fun = getRequestData[changePropName];

        // console.log(objOj,params,{ ...params, ...objOj },'obk')

        if (fun) {
          return fun({ ...params, ...objOj });
        }
        // 销售组织
        return listOrgRelation({ ...params, ...objOj });
      } catch (error) {
        console.log(error, "error");
      }
    },
    handenOk(type, data = {}) {
      try {
        // const changePropName =
        //   changeProp[this.activeProps.prop] || this.activeProps.prop;
        if (["zzzcZzmatnr", ...this.addSearchKey].includes(type)) {
          const obj = this.addSearch(type, data, this.activeProps?.backProps);
          this.dataInfo = {
            ...this.dataInfo,
            ...obj,
          };
          return;
        }
        if (type === "salesOrg") {
          if (!data?.salesOrg || !data?.EKORG) {
            this.$modal.msgError("请选择选择销售组织");
            return;
          }
        }
        const oldDatainfo = JSON.parse(JSON.stringify(this.dataInfo));
        if (this.activeProps.backProps) {
          let obj = {};
          if (this.activeProps.setBackParams) {
            obj = this.activeProps.setBackParams(
              this.activeProps.prop,
              data,
              this
            );
          } else {
            obj = getaddSearch(type, data, this.activeProps.backProps);
          }

          this.dataInfo = {
            ...this.dataInfo,
            ...obj,
          };
        }

        if (this.getSearchcallBack) {
          this.getSearchcallBack(this.dataInfo, this.activeProps);
        }
        this.setLineLgort(this.dataInfo, oldDatainfo);
      } catch (error) {
        console.log(error, "error");
      }
    },

    closeProp(item) {
      let obj = {};
      if (this.closesearch) {
        obj = this.closesearch(item.backProps, item.prop);
      } else {
        obj = closesearch(item.backProps, item.prop);
      }

      this.dataInfo = {
        ...this.dataInfo,
        ...obj,
      };
      // console.log(obj,'obj')
    },
    visibleChange(val) {
      if (val && this.num === 0) {
        this.$forceUpdate();
      }
    },
    changeInput(value, prop, activeProps) {
      let val = trim(value);
      this.activeProps = activeProps;
      let obj = {
        ...this.dataInfo,
        [prop]: val,
      };
      if (this.setInput) {
        let otherObj = this.setInput(val, activeProps, this);
        obj = {
          ...obj,
          ...otherObj,
        };
      }
      this.dataInfo = { ...obj };

      if (this.getSearchcallBack && activeProps.type === "select") {
        this.getSearchcallBack(this.dataInfo, activeProps);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.comSearch {
  position: relative;
}
.app-main .fixHeader {
  // top: 100px;
  right: 0px;
  z-index: 4;
  position: fixed;
  padding-right: 20px;
  background: #fff;
  width: 100%;
  box-shadow: 0 3px 4px rgba(0, 0, 0, 0.5);
}
.UniformCss {
  .el-form-item {
    width: 100%;
  }

  ::v-deep .el-form-item__label {
    // font-size: 14px;
    line-height: 15px;
    margin-top: 10px;
    color: #666;
    font-weight: 400;
  }

  .el-form-item__content {
    width: calc(100% - 110px);
    display: inline-block;
    margin: 0 !important;

    .el-input,
    .el-select,
    .el-cascader {
      width: 100%;
    }

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
  }

  .el-col {
    height: 38px;
    margin-bottom: 12px;
  }

  .el-input.is-disabled .el-input__inner {
    color: #6a6a6a;
  }

  .search-button {
    text-align: right;
  }
}

.search-button2 {
  margin-bottom: 10px;
}

.activeclose {
  ::v-deep .el-input__suffix {
    top: 8px;
  }

  ::v-deep .el-input__suffix-inner {
    display: none;
  }
}

.activeclose:hover {
  ::v-deep .el-input__suffix-inner {
    display: block;
  }
}

@media screen and (max-width: 900px) {
  // .sidebar { display: none; }
  ::v-deep .el-form-item--small.el-form-item {
    margin-left: 0px !important;
  }
}
</style>
