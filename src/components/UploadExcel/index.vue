<template>
  <div style="display: inline;margin-left: 10px;margin-right: 10px;">
    <el-button
      ref="uploadbtn"
      type="primary"
      @click="clickDaoru"
      icon="el-icon-upload2"
      size="mini"
      >导入</el-button
    >
    <el-upload
      class="upload-demo"
      action="#"
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :file-list="fileList"
    >
      <el-button type="primary" ref="upload" icon="el-icon-upload2" size="mini"
        >导入</el-button
      >
    </el-upload>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import { trim } from "@/utils/index.js";
import { Loading } from "element-ui";
export default {
  name: "UploadExcel",
  props: {
    checkUpload: {
      type: Function,
    },
  },
  data() {
    return {
      fileList: [],
      downloadLoadingInstance:undefined
    };
  },
  methods: {
    clickDaoru() {
      if (this.checkUpload && this.checkUpload()) return
      this.$refs.upload.$el.click()
   
    },
    handleBeforeUpload(file) {
         const downloadLoadingInstance = Loading.service({ text: "正在导入数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
      this.downloadLoadingInstance = downloadLoadingInstance
      // const isLt2M = file.size / 1024 / 1024 < 5; // 文件大小限制为2MB
      // if (!isLt2M) {
      //   this.$message.error("上传文件大小不能超过 5MB!");
      //   return false;
      // }
      if (
        file.type !==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" &&
        file.type !== "application/vnd.ms-excel"
      ) {
        this.$message.error("只能上传 xls/xlsx 文件!");
        return false;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: "binary" });
        const sheetName = workbook.SheetNames[0]; // 获取第一个工作表名称
        const worksheet = workbook.Sheets[sheetName]; // 获取工作表数据
        let jsonData = XLSX.utils.sheet_to_json(worksheet); // 将工作表转换为 JSON 数据格式
        // console.log(jsonData,'jsonData')
        jsonData = jsonData.map((item,index) => {
          Object.keys(item).forEach((key) => {
            const newKey = trim(key);
            item[newKey] = trim(item[key]);
          });
          item['序号'] = index + 2
          return item;
        });
        console.log(jsonData); // 处理 JSON 数据，例如发送到后端或显示在前端等。
        this.downloadLoadingInstance.close()
        this.$emit("getUploadExcel", jsonData);
      };
      reader.readAsBinaryString(file); // 以二进制字符串形式读取文件内容
      return false; // 阻止自动上传行为，因为我们已经手动处理了文件读取。
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList); // 可选，用于处理文件移除的逻辑。
    },
  },
};
</script>

<style scoped>
.upload-demo {
  display: inline-block;
  margin: 0px 10px;
  display: none;
}
</style>
