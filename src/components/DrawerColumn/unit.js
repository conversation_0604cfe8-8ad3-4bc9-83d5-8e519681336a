import Sortable from "sortablejs";
export const mixinColumn = {
  data() {
    return {
      //   tableData,
      tableKey: "456",
      dialogVisibleShow: false,
    };
  },

  async mounted() {
    //表格拖拽方法
    // this.columnDrop();
    // console.log(this.tableData, this, "tableData");
  },
  methods: {
    handMove(type, scope) {
      console.log(type,'0000')
      if (type === 0) {
        // this.moveDown()
        this.moveUpward(scope.row, scope.$index);
      } else {
        this.moveDown(scope.row, scope.$index);
      }
    },
    /**
     * 表格字段下移方法
     */
    moveDown(row) {
        const prop1 = row.prop
       let index = this.tableData.findIndex(({prop})=>(prop === prop1))
      if (index + 1 == this.tableData.length) {
        this.$message({
          message: "已经是最后一条，下移失败",
          type: "error",
        });
      } else {
        let downData = this.tableData[index + 1];
        this.tableData.splice(index + 1, 1);
        this.tableData.splice(index, 0, downData);
      }
    },
    /**
     * 表格字段上移方法
     */
    moveUpward(row, ) {
      // tableData 列数据
        const prop1 = row.prop
        let index = this.tableData.findIndex(({prop})=>(prop === prop1))

      if (index > 0) {
        let upData = this.tableData[index - 1];
        this.tableData.splice(index - 1, 1);
        this.tableData.splice(index, 0, upData);
        // console.log("移动成功");
      } else {
        // console.log("第一条数据");
        this.$message({
          message: "已经是第一条，上移失败",
          type: "error",
        });
      }
    },

    /**
     * 列拖拽
     */
    columnDrop() {
      const _this = this;
      // console.log("数据", this.tableData);
      const wrapperTr = document.querySelector(
        ".comTable .el-table__header-wrapper tbody tr"
      );

      this.sortable = Sortable.create(wrapperTr, {
        animation: 180,
        delay: 0,
        onEnd: (evt) => {
          const empty = 2;
          // 跳过显示的列数量，如开头我们用了一个多选框，h和序号
          const oldItem = this.tableData[evt.oldIndex - empty];
          this.tableData.splice(evt.oldIndex - empty, 1);
          this.tableData.splice(evt.newIndex - empty, 0, oldItem);
          _this.reDrawTable();
          // 每一次拖拽后都要重绘一次
        },
      });
    },
    /**
     * 触发表格重绘
     */
    reDrawTable() {
      this.tableKey = Math.random();
        console.log( this.tableData,' this.tableData')
      this.$nextTick(() => {
        this.columnDrop();
      });
    },
  },
};
