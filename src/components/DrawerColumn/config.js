export const columns = [
  {
    label: "标题",
    prop: "label",
    // width:60
  },
  {
    label: "操作",
    prop: "caozuo",
    // width:60,
    slotName:'caozuo'

  },
  
];

export const tableList = [
  {
    no: "no1",
    title: "标题",
    width: 100,
    fixed: "right",
    sortNum: 1,
    visible: "是",
    id: "1",
  },
  {
    no: "no2",
    title: "标题2",
    width: 100,
    fixed: "right",
    sortNum: 2,
    visible: "否",
    id: "2",
  },
];

export const btnProps = {
  add: {
    isShow: false,
    hasPermi: [""],
  },
  batchDelete: {
    isShow: false,
    hasPermi: [""],
  },
  deleteLine: {
    isShow: false,
    hasPermi: [""],
  },
  caozuoShow: {
    isShow: false,
  },
};

export const getColumns = (list) => {
  let data = list.map((item) => {
    item.prop = item.prop;
    item.label = item.label;
    item.visible = true
   
    return item
  });

  return data;
};


export const sortList = (list)=>{

  let tableList = [...list].sort((a,b)=>(a.sortNum-b.sortNum))

  return tableList

}
