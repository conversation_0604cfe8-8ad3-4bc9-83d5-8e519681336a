<template>
  <el-drawer
    title="表头配置"
    :before-close="close"
    :visible.sync="drawer"
    direction="rtl"
    size="30%"
    class="drawerRight"
    append-to-body
  >
    <div class="dramBody">
      <ComTable
        :tableData="
          tableData.filter(
            ({ prop, fixed = '' }) => prop !== 'caozuo' && !fixed
          )
        "
        :tableProps="tableProps"
        :column="columns"
        :isSelection="true"
        :showHeaderConfig="false"
        :typeIndex="false"
        :border="false"
        @selection-change="selectionChange"
        ref="dramBodyComTable"
      >
        <template #caozuo="scope">
          <span style="cursor: pointer">
            <el-tooltip
              class="item"
              effect="dark"
              content="下移"
              placement="top"
            >
              <i class="el-icon-sort-down" @click.stop="handMove(1, scope)"></i>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              content="上移"
              placement="top"
            >
              <i class="el-icon-sort-up" @click.stop="handMove(0, scope)"></i>
            </el-tooltip>
          </span>
        </template>
      </ComTable>
      <div class="demo-drawer__footer">
        <el-button @click="close" size="mini">取 消</el-button>
        <el-button type="primary" size="mini" @click="ok">确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { columns } from "./config";
import { mixinColumn } from "./unit";

export default {
  name: "DrawerColumn",
  mixins: [mixinColumn],
  components: {
    ComTable: () => import("../ComTable/index.vue"),
  },
  props: {},
  data() {
    return {
      columns,
      tableData: [],
      drawer: false,
      tableProps: {
        showHeader: false,
        rowKey: "prop",
      },
      selectList: [],
    };
  },
  computed: {},
  created() {},
  watch: {},
  methods: {
    getTable(valtableData) {
      this.tableData = valtableData;
      const rows = valtableData.filter(({ visible }) => visible !== "1");
      rows.forEach((row) => {
        this.$refs.dramBodyComTable.$refs.comTable.toggleRowSelection(row);
      });
    },
    close() {
      this.drawer = false;
      // this.$emit("handOk", "close", this.tableData);
    },
    ok() {
      const data = this.tableData.map((item) => {
        item.visible = this.selectList
          .map(({ prop }) => prop)
          .includes(item.prop)
          ? "2"
          : "1";
        return item;
      });
      this.tableData = data;
      this.selectList = [];
      this.$refs.dramBodyComTable.$refs.comTable.clearSelection();
      this.$emit("handOk", "ok", data);
      this.drawer = false;
    },
    closeEdit() {},
    selectionChange(data) {
      this.selectList = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.drawerRight {
  ::v-deep .el-drawer__body {
    padding: 20px;
    padding-top: 0px;
  }

  ::v-deep .el-drawer__header {
    margin-bottom: 0px;
  }

  .demo-drawer__footer {
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
  .dramBody {
    height: 100%;
  }

  .dramBody {
    height: calc(100vh - 100px);
    overflow: auto;
  }
}
</style>
