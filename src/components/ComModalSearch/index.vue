<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="90%"
    :append-to-body="true"
    class="ComModalSearch"
    @close="dialogVisible = false"

  >
    <div class="border">
      <el-tag
        size="small"
        closable
        @close="setCurrentRow()"
        v-if="currentRow && currentRow[rowProps.prop]"
        >{{ currentRow[rowProps.prop] || "" }}</el-tag
      >
    </div>
    <ComTableList
      ref="mainrList"
      isinitData
      :isSelection="false"
      :typeIndex="false"
      :searchData="searchData"
      :column="column"
      :getTableList="getList"
      :tableProps="tableProps"
      :showHeaderConfig="false"
      :isaCreateTime="false"
      @current-change="currentChange"
      @cell-dblclick="cellDblclick"
    >
    </ComTableList>
    <div slot="footer" class="dialog-footer" style="text-align: right">
      <el-button type="primary" size="mini" @click="handenOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { changeInfoData } from "../LineTable/units";
export default {
  name: "ComModalSearch",
  components: {
    ComTableList: () => import("@/components/ComTable/list.vue"),
  },
  dicts:['meins','ZTERM'],
  props: {
    title: {
      type: String,
      default() {
        return "标题";
      },
    },
    rowProps: {
      type: Object,
      default() {
        return {
          prop: "ZZMATNR",
          label: "ZZMATNR",
          rowKey: "ZZMATNR",
        };
      },
    },
    searchData: {
      type: Array,
      default() {
        return [];
      },
    },
    column: {
      type: Array,
      default() {
        return [];
      },
    },
    getTableList: {
      type: Function,
      default() {
        return () => {};
      },
    },
    checkSelectMsg:{
      type: Function,
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectIds: [],
      currentRow: {},
      tableProps: {
        typeIndex: false,
        maxHeight: 400,
        highlightCurrentRow: true,
        rowKey: this?.rowProps?.rowKey,
      },
      // newRowProps:this.rowProps
    };
  },
  watch: {
    // rowProps
    currentRow(val) {
      if (!val || !val[this?.rowProps?.prop]) {
        this.$nextTick(()=>{
          this.setCurrentRow();
        })
        
      }
    },
  },
  computed: {
    newRowProps() {
      return this.rowProps;
    },
  },
  created() {},
  mounted() {},
  methods: {
    /** 查询销售订单头信息列表 */
    getList(queryParams) {
      // console.log(queryParams,'0000')
      const res = this.getTableList(queryParams);
      return res;
    },
    handenOk() {
      if (
        this?.$parent?.editItem &&
        ["LGORT", "UMLGO"].includes(this?.$parent?.activeProps?.prop)
      ) {
        const LGORT = this?.$parent?.editItem?.LGORT || "";
        const UMLGO = this?.$parent?.editItem?.UMLGO || "";
        let LGORTKey = this?.$parent?.activeProps?.prop === "LGORT";
        let UMLGOKey = this?.$parent?.activeProps?.prop === "UMLGO";
        const newLgort = this.currentRow.lgortName;

        // 接受库存地点
        if (LGORT && UMLGOKey && LGORT === newLgort) {
          this.$modal.msgError("发出库存地点与接受库存地点不能相同，请重新选择");
          return;
        } else if (UMLGO && LGORTKey && UMLGO === newLgort) {
          // 发出库存地点
          this.$modal.msgError("发出库存地点与接受库存地点不能相同，请重新选择");
          return;
        }
      }

      if(
        this?.$parent?.editItem &&
        ["ZZMATNR"].includes(this?.$parent?.activeProps?.prop)
      ){
        let list = this?.$parent?.salesOrderLineList || []
        let changeProp = {
          ZZMATNR:'ZZMATNR'
        }
        let prop = changeProp[this?.$parent?.activeProps?.prop] || ''

        let asize = list?.filter(({ZZMATNR:z = ''})=>(z ===  this.currentRow[prop]))

        if(asize && asize.length){
          this.$modal.msgError("该物料专卖号已选择，请重新选择");
          return
        }
      }


      if(this.checkSelectMsg && this.checkSelectMsg(this.currentRow,this)){
        return
      }


      const asList = this.$refs.mainrList?.tableData || []



      this.$emit("handenOk", changeInfoData(this.currentRow),asList);
      this.currentRow = {}
      this.dialogVisible = false;

    },
    currentChange(val) {
      // console.log(,'000000')
      this.currentRow = val;
    },
    setCurrentRow(row) {
      const comTable = this.$refs.mainrList.$refs.table.$refs.comTable;
      comTable?.setCurrentRow(row);
    },
    cellDblclick(val) {
      // const comTable = this.$refs.mainrList.$refs.table.$refs.comTable;
      // comTable?.setCurrentRow(val);
      this.currentRow = val;
      this.handenOk();
    },
  },
};
</script>

<style lang="scss" scoped>
.border {
  border: 1px dashed #000;
  min-height: 60px;
  padding: 5px;
  margin-bottom: 10px;
}
::v-deep .comSearch {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;

  .UniformCss {
    flex: 1;
    margin-right: 20px;
  }
}

.ComModalSearch {
  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }
  ::v-deep .el-dialog__body {
    padding: 0px 20px;
    min-height: 558px;
  }
}
</style>
