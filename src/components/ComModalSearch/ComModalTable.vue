<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="90%"
    class="ComModalSearch"
    @close="dialogVisible = false"
  >
    <ComTableList
      ref="ComTableList"
      :isinitData="true"
      :isSelection="false"
      :typeIndex="false"
      :searchData="searchData"
      :column="column"
      :getTableList="getTableList"
      :isPage="isPage"
      :tableProps="tableProps"
      :showHeaderConfig="false"
      :isaCreateTime="false"
      :searchInfo="searchInfo"
      :searchProps="searchProps"
    >
    </ComTableList>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: right; "
    >
      <slot></slot>
      <el-button type="primary" size="mini" @click="handenOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ComTableList from "../ComTable/list.vue";
export default {
  name: "ComModalTable",
  components: {
    ComTableList,
  },
  props: {
    isPage: {
      type: Boolean,
      default() {
        return true;
      },
    },
    title: {
      type: String,
      default() {
        return "标题";
      },
    },
    searchInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    searchProps: {
      type: Object,
      default() {
        return {};
      },
    },
    searchData: {
      type: Array,
      default() {
        return [];
      },
    },
    column: {
      type: Array,
      default() {
        return [];
      },
    },
    getTableList: {
      type: Function,
      default() {
        return () => {};
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      tableProps: {
        typeIndex: false,
        maxHeight: 460,
        showHeaderConfig:false
      },
    };
  },
  created() {},
  mounted() {
    // this.$refs.ComTableList?.submitEmits && this.$refs.ComTableList.submitEmits(this.searchInfo)
  },
  methods: {
    handenOk() {
      this.dialogVisible = false;
      this.$emit('handenOk')
    },
    open(tableList){
      this.dialogVisible = true;
      this.$nextTick(()=>{
        this.$refs.ComTableList.tableData = tableList
        this.$refs.ComTableList.paginationProps.total = tableList?.length || 0

      })
    }
  },
};
</script>

<style lang="scss" scoped>
.border {
  border: 1px dashed #000;
  min-height: 60px;
  padding: 5px;
  margin-bottom: 10px;
}
::v-deep .comSearch {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;

  .UniformCss {
    flex: 1;
    margin-right: 20px;
  }
}

.ComModalSearch {
  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }
  ::v-deep .el-dialog__body {
    padding: 0px 20px;
    min-height: 558px;
  }
}
</style>
