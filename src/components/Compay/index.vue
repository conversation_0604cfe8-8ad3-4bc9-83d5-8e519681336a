<template>
  <el-dialog
    title="选择公司"
    :visible.sync="dialogVisible"
    width="80%"
    @close="dialogVisible = false"
  >
    <div><span>{{ currentRow.name  }}</span></div>
    <ComTableList
      :column="column"
      :searchData="column"
      :getTableList="getTableList"
      :tableProps="{
        highlightCurrentRow:true,
      }"
       @currentChange=currentChange
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
      <el-button type="primary" @click="dialogVisible = false" size="mini"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import ComTableList from "../ComTable/list.vue";
export default {
  components: {
    ComTableList,
  },
  data() {
    return {
      dialogVisible: false,
      column: [
        {
          prop: "a",
          label: "公司编码",
          hiddenSearch: true,
        },
        {
          prop: "b",
          label: "公司名称",
        },
      ],
      currentRow :{}
    };
  },
  methods: {
   async getTableList() {
      return {
        code: 1,
        data:[]
      };
    },
    currentChange(val){
        this.currentRow = val
    }
  },
};
</script>
