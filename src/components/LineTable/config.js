import { getColumnsOptions,setColumnsOptions } from "@/utils/location";
export const mainrSearchData = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    span: 16,
    // width:140
  },
];

export const mainrColums = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    // width:140
  },
  {
    prop: "maktxZh",
    label: "中文物料描述",
    // width:140
  },
  // {
  //   prop: "MEINS",
  //   label: "基本单位",
  //   // width:140
  // },
  {
    prop: "MSEHT",
    label: "基本单位描述",
    // width:140
  },

  {
    prop: "ZZMLYLX",
    label: "来源类型",
    // width:140
  },
  // {
  //   prop: "BESKZName",
  //   label: "采购类型",
  //   // width:140
  // },

  {
    prop: "MVGR2",
    label: "仓储组",
    // width:140
  },
  {
    prop: "goodsLocation",
    label: "货位",
    // width:140
  },
   {
    prop: "ZZMSQBZ",
    label: "是否授权",
    optionsCode:'y_n',
    
  },

  
];

export const activeHandok = (selef, searchData, data, allList) => {
  const propNameCode = searchData.filter((item) => item.backProps);
  let editItem = {
    ...selef.editItem,
  };
  if (propNameCode && propNameCode.length) {
    propNameCode.forEach(({ backProps, prop: oldProp }) => {
      if (oldProp === selef.activeProps.prop) {
        backProps.forEach((item) => {
          const { backProp, backLable, prop } = item;
          // console.log(data,'data')
          const _data = data || {};
          const key = `${prop},${backLable}`;
          editItem = {
            ...editItem,
            [prop]: _data[backLable] || "",
            [key]: [data[backProp], data[backLable]],
            // id:`p${data[backProp]}`s
          };
        });
      }
    });
  }

  // 子集的新增
  if (
    selef.activeProps?.isSetChildren &&
    selef?.activeProps?.changechildParams
  ) {
    const params = selef.activeProps.changechildParams(
      editItem,
      allList,
      selef
    );
    editItem = {
      ...editItem,
      ...params,
    };
  }
  selef.editItem = {
    ...editItem,
  };
};

export const changeNewLineList = (list) => {
  if (!list || (Array.isArray(list) && list.length === 0)) return list;

  const _list = JSON.parse(JSON.stringify(list)).map((item) => {
    const _searchInfo = { ...item };
    Object.keys(_searchInfo).forEach((item) => {
      if (item.indexOf(",") > -1 && _searchInfo[item]) {
        item.split(",").forEach((key, index) => {
          _searchInfo[key] = _searchInfo[item][index];
        });
        delete _searchInfo[item];
      }
    });

    if (item.children && item.children) {
      _searchInfo.children = changeNewLineList(item.children);
    }

    return _searchInfo;
  });
  return _list;
};

// 计算总的审批数量
export const setParantNum = (editItem, val, self) => {
  const { zsasOrderLineId: pzsasOrderLineId } = editItem.parant || {};
  const { zsasOrderLineId: chzsasOrderLineId } = editItem;
  const lineTable =
    [...self.salesOrderLineList].filter(
      ({ zsasOrderLineId }) => pzsasOrderLineId === zsasOrderLineId
    )[0].children || [];
  let list = [];
  if (lineTable && lineTable.length) {
    list = lineTable
      .filter(({ zsasOrderLineId }) => zsasOrderLineId !== chzsasOrderLineId)
      .map(({ KWMENG_SP = 0 }) => KWMENG_SP || 0);
  }
  const otherSum = list.reduce(
    (accumulator, currentValue) => accumulator * 1 + currentValue * 1,
    0
  );

  const KWMENG_SP = otherSum + val;

  const PsalesOrderLineList = [...self.salesOrderLineList].map((item) => {
    if (item[self.keyId] === pzsasOrderLineId) {
      item.KWMENG_SP = KWMENG_SP;
    }
    return item;
  });

  return PsalesOrderLineList;
};

// 检测专卖物料号
export const checkZZMATNR = (self) => {
  let changeProp = {
    ZZMATNR: "ZZMATNR",
  };

  let prop = changeProp[self.activeProps?.prop] || "";

  if(prop !== 'ZZMATNR')return false

  let list = self.salesOrderLineList.filter((item)=>(self.editId !== item[self.keyId])) || [];


  let asize = list?.filter(({ [prop]: z = "" }) => ((z === self.editItem[prop]) &&  self.editItem[prop]));



  if (asize && asize.length) {
    self.$modal.msgError("该物料专卖号已选择，请重新选择");

   
    return true;
  }

  return false
};


export const clearCheckZZMATNRProp = (self)=>{
   if (self.activeProps?.backProps) {
      let backProps = self.activeProps.backProps || [];
      // let obj = {};
      backProps.forEach((item) => {
        self.editItem[item.prop] = undefined;
        self.editItem[`${item.prop},${item.backLable}`] = undefined;
      });
      // return obj
    } else {
      self.editItem.ZZMATNR = undefined;
      self.editItem.MAKTX = undefined;
      self.editItem.MEINS = undefined;
      self.editItem["MEINS,MSEHT"] = undefined;
      
    }

}


export const  getColumnCinfig =(self,tableColumn)=> {
      const pathname = self?.columnKey || self.keyId || location.pathname
      const cKey =  pathname

      let r_tableColumn = JSON.parse(JSON.stringify(tableColumn))
      if(getColumnsOptions(cKey)){
        r_tableColumn = getColumnsOptions(cKey)
      }else{
        r_tableColumn = r_tableColumn.map((item)=>{
           item.visible = true
           return item
        })
        setColumnsOptions(cKey,r_tableColumn)
      }
      self.$refs.DrawerColumn.drawer=true
      self.$nextTick(()=>{
        const _r_tableColumn = JSON.parse(JSON.stringify(r_tableColumn))
        self.$refs.DrawerColumn.tableData = _r_tableColumn
        self.$refs.DrawerColumn.getTable(_r_tableColumn)
      })

 }
