import { Loading } from "element-ui";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { getRequestData, changeProp } from "../ComSearch/config";
import { setTableDictValue } from "@/utils/index.js";
import { getaddSearch } from "../ComSearch/utils";
// types：demand/salesOrder/purApply/purOrder/YK/DB/LL/PD/ZZD
export const downKey = {
  "demand-detail": {
    key: "demand",
    label: "需求单",
  },
  "SalesOrder-detail": {
    key: "salesOrder",
    label: "销售订单",
  },
  "purApply-detail": {
    key: "purApply",
    label: "采购申请",
  },
  "purOrder-detail": {
    key: "purOrder",
    label: "采购订单",
  },
  "moving-detail": {
    key: "YK",
    label: "移库",
  },
  "allot-detail": {
    key: "DB",
    label: "调拨",
  },
  "picking-detail": {
    key: "LL",
    label: "领料",
  },
  "check-detail": {
    key: "PD",
    label: "盘点",
  },
  "assembly-detail": {
    key: "ZZD",
    label: "组装单",
  },
  "application-detail": {
    key: "salesReOrder",
    label: "退货申请",
  },
};

export const initbtn = {
  add: {
    isShow: true,
    hasPermi: [""],
  },
  batchDelete: {
    isShow: true,
    hasPermi: [""],
  },
  deleteLine: {
    isShow: true,
    hasPermi: [""],
  },
  caozuoShow: {
    isShow: true,
    hasPermi: [""],
  },
  uploadExcel: {
    isShow: false,
    hasPermi: [""],
  },
};

export const propsObj = {
  isShowBtnRight:{
    type: Boolean,
    default() {
      return true;
    },
  },
  isShowCancel:{
    type: Boolean,
    default() {
      return false;
    },
  },
  cancelFuntion:{
    type: Function,
    default() {
      return ()=>{};
    },
  },
  searchDetail: {
    type: Object,
    default() {
      return {};
    },
  },
  showHeaderConfig: {
    type: Boolean,
    default() {
      return true;
    },
  },
  checkUploadExcel: {
    type: Function,
  },
  indexXH: {
    type: Boolean,
    default() {
      return true;
    },
  },
  lineCloumn: {
    type: Array,
    default() {
      return [];
    },
  },
  showSelection: {
    type: Boolean,
    default() {
      return true;
    },
  },
  lineList: {
    type: Array,
    default() {
      return [];
    },
  },
  // 唯一key
  keyId: {
    type: String,
    default() {
      return "demandLineId";
    },
  },
  // 唯一父级key
  parantId: {
    type: String,
    default() {
      return "parantId";
    },
  },
  // 行号
  POSNRKey: {
    type: String,
    default() {
      return "POSNR";
    },
  },
  columnKey: {
    type: String,
  },
  // 是否手工分页数据
  isManualPage: {
    type: Boolean,
    default() {
      return true;
    },
  },
  //  是否展示分页
  isShowPage: {
    type: Boolean,
    default() {
      return true;
    },
  },

  btnProps: {
    type: Object,
    default() {
      return {
        ...initbtn,
      };
    },
  },

  // 必填项key
  requiredKeys: {
    type: Array,
    default() {
      return ["ZZMATNR"];
    },
  },

  tableProps: {
    type: Object,
    default() {
      return {};
    },
  },

  dataProps: {
    type: Object,
    default() {
      return {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
        },
      };
    },
  },
  selectable: {
    type: Function,
    // default() {
    //   return () => true;
    // },
  },
  checkNum: {
    type: Function,
  },
  isShowRightBtn: {
    type: Boolean,
    default() {
      return false;
    },
  },
  checkUploadOther: {
    type: Function,
  },
};

export const getDownKey = (href) => {
  let obj = {};
  Object.keys(downKey).forEach((key) => {
    if (href.indexOf(key) > -1) {
      obj = downKey[key];
    }
  });
  return obj;
};

export const changeInfoData = (dataInfo) => {
  const _searchInfo = JSON.parse(JSON.stringify(dataInfo));
  const params = {
    ..._searchInfo,
  };
  Object.keys(_searchInfo).forEach((item) => {
    if (item.indexOf(",") > -1 && _searchInfo[item]) {
      item.split(",").forEach((key, index) => {
        params[key] = _searchInfo[item][index] || _searchInfo[key];
      });
      delete params[item];
    }
  });
  const searchInfo2 = JSON.parse(JSON.stringify(params));
  return searchInfo2;
};

export const getLineParams = (parantdataInfo, wrtWERKS, self) => {
  let params = {};
  const changePropName =
    changeProp[self.activeProps.prop] || self.activeProps.prop;

  const info = changeInfoData(parantdataInfo);
  const editItem = changeInfoData(self.editItem);

  // 库存地点
  if (["LGORT", "zzzzjLgort", "ZZMATNR"].includes(changePropName)) {
    if (editItem.WERKS) {
      params.werks = editItem.WERKS || info.WERKS;
    } else if (info.WERKS) {
      params.werks = info.WERKS;
    } else {
      params.werks = wrtWERKS;
    }

    if (changePropName === "ZZMATNR") {
      params.VKORG = info.EKORG || info?.VKORG || "";
    }

    if (info.unwrk && self.activeProps.prop === "UMLGO") {
      params.werks = info.unwrk;
    } else if (info.werks) {
      // console.log(info,'0000')
      params.werks = info.werks;
    }
  }

  //成本中心
  if (["KOSTL"].includes(changePropName)) {
    if (info.BUKRS) {
      params.bukrs = info.BUKRS;
    } else if (info.companyCode) {
      params.bukrs = info.companyCode;
    }
  } else if (["KUNNR"].includes(changePropName)) {
    if (info.companyCode) {
      params.BUKRS = info.companyCode;
    }
    if (info.werks) {
      params.VKORG = info.werks;
    }
  }

  return params;
};

const changeParams = (prop, self) => {
  const wrtWERKS = self.parantdataInfo.WERKS;
  const info = changeInfoData(self.parantdataInfo);
  // console.log(info,'info')
  let params = {};
  if (info.WERKS) {
    params.werks = info.WERKS;
  } else {
    params.werks = wrtWERKS;
  }

  if (prop === "ZZMATNR") {
    params.VKORG = info.VKORG;
  }

  if (info.unwrk && ["UMLGO", "LGORT"].includes(prop)) {
    params.werks = info.unwrk;
  } else if (info.werks) {
    // console.log(info,'0000')
    params.werks = info.werks;
  }

  //成本中心
  if (["KOSTL"].includes(prop)) {
    if (info.companyCode) {
      params.bukrs = info.companyCode;
    }
  }
  // 查找客户数据
  if (["KUNNR"].includes(prop)) {
    if (info.companyCode) {
      params.BUKRS = info.companyCode;
    }
    if (info.werks) {
      params.VKORG = info.werks;
    }
  }

  return params;
};

// 校验物料号是否重复
export const checkZZMATNRData = (newList, oldList) => {
  let newData = [];
  if (newList && newList.length) {
    newList.forEach((item) => {
      const index = [...oldList, ...newData].findIndex(
        ({ ZZMATNR }) => ZZMATNR === item.ZZMATNR
      );
      if (index > -1) {
        item.errMsg = `该物料已选择,${item?.errMsg || ""}`;
      }
      newData.push(item);
    });
  }
  return newData;
};

// 校验专卖物料号
const checkZZMATNRDtaRes = (resRows, list, codeList, self) => {
  let newData = list;
  if (resRows && resRows.length) {
    // 检测物料是否有值
    let isZZMATNRList = resRows.findIndex((item2) => item2.ZZMATNR) > -1;
    newData = newData.map((newItem) => {
      let item = {
        ...newItem,
      };
      // 校验专卖物料号
      if (isZZMATNRList) {
        let ZZMATNRData = resRows.filter(
          (item2) => item2.ZZMATNR === item.ZZMATNR
        );
        let objbackProps =
          codeList.filter(({ prop }) => prop === "ZZMATNR")?.[0]?.backProps ||
          [];

        if (!ZZMATNRData || ZZMATNRData.length === 0) {
          item.errMsg = `该物料不存在,${item?.errMsg || ""}`;
        } else {
          let row = ZZMATNRData[0];
          // 是否授权
          if(item.ZZMSQBZ){
            objbackProps = objbackProps.filter(({prop})=>(prop !== 'ZZMSQBZ'))
          }
          // 来源类型
          if(item.ZZMLYLX){
             objbackProps = objbackProps.filter(({prop})=>(prop !== 'ZZMLYLX'))
          }
          let obj = getaddSearch("ZZMATNR", row, objbackProps);
          item = {
            ...item,
            ...obj,
          };
        }
      }
      return item;
    });
  } else {
    newData = newData.map((item) => {
      item.errMsg = `该物料不存在,${item?.errMsg || ""}`;
      return item;
    });
  }
  return newData;
};
// 库存地点
const checkLgort = (resRows, list, self) => {
  let newData = list;
  let requiredKeys = self.requiredKeys;
  // 检测物料是否有值
  const LineCloumnLGORT = self.salesOrderLineCloumn.filter(
    ({ dictOptionCode, prop }) =>
      dictOptionCode === "LGORT" && requiredKeys.includes(prop)
  );

  // console.log(self.requiredKeys,self.salesOrderLineCloumn,'self.requiredKeys')

  if (resRows && resRows.length) {
    newData = newData.map((item) => {
      // console.log(LineCloumnLGORT,'LineCloumnLGORT')
      LineCloumnLGORT.forEach(({ prop, label, backProps }) => {
        let lgortData = resRows.filter((item2) => item2.lgort === item[prop]);
        if (!lgortData || lgortData.length === 0) {
          item.errMsg = `${label}不存在,${item?.errMsg || ""}`;
        } else {
          let row = lgortData[0];
          let obj = getaddSearch(prop, row, backProps);
          // console.log(obj,'00000')
          item = {
            ...item,
            ...obj,
          };
        }
      });
      return item;
    });
  } else {
    newData = newData.map((item) => {
      LineCloumnLGORT.forEach(({ label }) => {
        item.errMsg = `${label}不存在,${item?.errMsg || ""}`;
      });
      return item;
    });
  }
  return newData;
};

const keys = {
  KOSTL: "kostl",
  KUNNR: "KUNNR",
};
const checkRes = (key, resRows, list, self) => {
  const lineCloumn = self.salesOrderLineCloumn?.filter(
    ({ prop }) => prop === key
  );
  let newData = list;
  if (lineCloumn && lineCloumn.length) {
    const { backProps, prop, label } = lineCloumn[0];
    if (resRows && resRows.length) {
      newData = newData.map((item) => {
        // console.log(LineCloumnLGORT,'LineCloumnLGORT')
        let KL = resRows.filter(
          (item2) => item2[keys[prop] || prop] === item[prop]
        );
        if (!KL || KL.length === 0) {
          item.errMsg = `${label}不存在,${item?.errMsg || ""}`;
        } else {
          let row = KL[0];
          let obj = getaddSearch(prop, row, backProps);
          item = {
            ...item,
            ...obj,
          };
        }
        return item;
      });
    } else {
      newData = newData.map((item) => {
        item.errMsg = `${label}不存在,${item?.errMsg || ""}`;
        return item;
      });
    }
    return newData;
  }
  return newData;
};

export const checkExportList = async (data, self) => {
  // console.log(data,'dadada12121')
  const dictDataOptions = self?.dictDataOptions?.ekgrp || [];
  const oldLineData = self.salesOrderLineList || [];
  // const requiredKeys = self.requiredKeys || [];
  let changeKey = {
    UMLGO: "LGORT",
  };
  let ZZMATNRbackProps = [
    {
      backProp: "ZZMATNR",
      backLable: "ZZMATNR",
      prop: "ZZMATNR",
      propLabels:['ZZMATNR','ZZMATNR']
    },
    {
      backProp: "maktxZh",
      backLable: "maktxZh",
      prop: "MAKTX",
      propLabels:['MAKTX','maktxZh']
    },
    {
      backProp: "MEINS",
      backLable: "MSEHT",
      prop: "MEINS",
      propLabels:['MEINS','MEINS']
    },
    //  {
    //   backProp: "MSEHT",
    //   backLable: "MSEHT",
    //   prop: "MSEHT",
    // },
    {
      backProp: "ZZMLYLX",
      backLable: "ZZMLYLX",
      prop: "ZZMLYLX",
      propLabels:['ZZMLYLX','ZZMLYLX']
      
    },
  ];
  const codeList = self.salesOrderLineCloumn
    .filter(({ append, prop }) => append)
    .map((item) => {
      if (item.prop === "ZZMATNR" && !item.backProps) {
        item.backProps = ZZMATNRbackProps;
      }
      return item;
    });

   const dataInfo = self?.$parent?.$refs?.ComSearchInfo.dataInfo;
   let dictOptionCodeKey = codeList.map(({ prop }) => prop).map((key) => key);

    //   dictOptionCodeKey = dictOptionCodeKey.filter((key) => key !== "LPEIN");
    // if (dataInfo?.LGORT) {
    //   dictOptionCodeKey = dictOptionCodeKey.filter((key) => key !== "LGORT");
    // }
    // if (dataInfo?.EKGRP) {
    //   dictOptionCodeKey = dictOptionCodeKey.filter((key) => key !== "EKGRP");
    // }

    // console.log(dataInfo,dictOptionCodeKey,'dictOptionCodeKey')

  const codeKeys = [...new Set(dictOptionCodeKey)];

  // const downloadLoadingInstance = Loading.service({
  //   text: "正在导入数据，请稍候",
  //   spinner: "el-icon-loading",
  //   background: "rgba(0, 0, 0, 0.7)",
  // });

  const exKeys = {};

  data.forEach((item) => {
    codeKeys.forEach((key) => {
      Object.keys(item).forEach((key2) => {
        let id = changeKey[key] || key;
        // console.log(id, item[key], key, "00000");
        if (key === key2 && item[key]) {
          let arr = exKeys[id] || [];
          const aitem =  changeInfoData(item)
          // console.log(aitem,'aitem')
          exKeys[id] = [...arr, aitem[key]];
        }
      });
    });
  });

  // console.log(data,'dadadad')

  try {
    // 请求物料数据
    let funArr = [];
    Object.keys(exKeys).forEach((key) => {
      let fun = getRequestData[key];
      const queryList = [...new Set(exKeys[key])];
      const params = changeParams(key, self);
      // console.log(queryList,'queryList')
      if (fun && queryList && queryList.length) {
        const _queryList = [...new Set(queryList)];
        const query = {
          ...params,
          lgortList: key === "LGORT" ? _queryList : undefined,
          zzmatnrList: key === "ZZMATNR" ? _queryList : undefined,
          kunnrList: key === "KUNNR" ? _queryList : undefined,
          kostlList: key === "KOSTL" ? _queryList : undefined,
          pageNum: 1,
          pageSize: 10000000,
        };

        funArr.push(fun(query));
      }
    });

    let EKGRPArr = self.salesOrderLineCloumn.filter(
      ({ prop }) => prop === "EKGRP"
    );

    let newData = checkZZMATNRData([...data], [...oldLineData]);

    // console.log(newData,'newdadada')

    // 校验采购组
    if (!dataInfo?.EKGRP && EKGRPArr && EKGRPArr.length) {
      newData = newData.map((item) => {
        let obj = {};
        const EKGRPData = dictDataOptions.filter(
          ({ value }) => value == item.EKGRP
        );
        if (EKGRPData && EKGRPData.length) {
          let obj2 = getaddSearch("EKGRP", EKGRPData[0], [
            {
              prop: "EKGRP",
              backProp: "value",
              backLable: "label",
            },
          ]);
          obj = {
            ...obj,
            ...obj2,
          };
        } else {
          obj.errMsg = "采购组不存在";
        }
        return {
          ...item,
          ...obj,
        };
      });
    }

    let results = await Promise.allSettled(funArr);

    const resData = results
      .filter(({ status }) => status === "fulfilled")
      .map(({ value }) => value);
    const resDataError = results.filter(({ status }) => status === "rejected");

    const keys_H = [];
    resData.forEach((res) => {
      const resRows = res?.rows || [];
      keys_H.push(res.key);
      if (res?.key === "LGORT") {
        newData = checkLgort(resRows, newData, self);
      } else if (res.key === "ZZMATNR") {
        newData = checkZZMATNRDtaRes(resRows, newData, codeList, self);
      } else {
        newData = checkRes(res.key, resRows, newData, self);
      }
    });

    const key_I = [...new Set(Object.keys(exKeys))];
    key_I.forEach((key) => {
      // console.log(key,keys_H,'keys_H')
      if (!keys_H.includes(key)) {
        if (key === "LGORT") {
          newData = checkLgort([], newData, self);
        } else if (key === "ZZMATNR") {
          newData = checkZZMATNRDtaRes([], newData, codeList, self);
        } else {
          newData = checkRes(key, [], newData, self);
        }
      }
    });

    const errorList = newData.filter(({ errMsg }) => errMsg);

    const backList = newData.filter(({ errMsg }) => !errMsg);

    // console.log(backList, errorList, "errorList");
    // downloadLoadingInstance.close();
    return [backList, errorList];
  } catch (error) {
    console.log(error, "error");
    // downloadLoadingInstance.close();
    return [];
  }
};

export const checkLineRequeck = (data, requiredKeys = [], lineColumn, self) => {
  try {
    let list = [...data];
    const arr = [];
    let _requiredKeys = [...new Set([...requiredKeys])];
    const dataInfo = self?.$parent?.$refs?.ComSearchInfo?.dataInfo;
    // console.log(dataInfo, "ddddd");
      _requiredKeys = _requiredKeys.filter((key) => key !== "LPEIN");
    if (dataInfo?.LGORT) {
      _requiredKeys = _requiredKeys.filter((key) => key !== "LGORT");
    }
    if (dataInfo?.EKGRP) {
      _requiredKeys = _requiredKeys.filter((key) => key !== "EKGRP");
    }
    if (list && list.length) {
      list = list.map((item) => {
        if (!arr.includes(item?.ZZMATNR) && item?.ZZMATNR) {
          arr.push(item.ZZMATNR);
        } else if (item?.ZZMATNR) {
          item.errMsg = `该专卖物料号已选择`;
        }
        _requiredKeys.forEach((key) => {
          if (!item[key]) {
            let label = lineColumn.filter(({ prop }) => key === prop)[0]?.label;
            item.errMsg = `${
              item.errMsg ? item.errMsg + "," : ""
            }${label}不能为空`;
          }
        });
        return item;
      });
    }

    // console.log(list,'list')

    return list;
  } catch (error) {
    console.log(error, "rrrr");
  }
};

export const c_list = [
  {
    prop: "xuhao",
    label: "序号",
  },
  {
    prop: "errMsg",
    label: "错误提示",
  },
];

export const exportExcel = (columns, tableData) => {
  const headers = [...columns, ...c_list].map((col) => col.label); // 获取表头
  const keys = [...columns, ...c_list].map((col) => col.prop); // 获取字段

  const data = tableData.map((row) => {
    let obj = {};
    keys.forEach((key, index) => {
      obj[headers[index]] = row[key];
    });
    return obj;
  });

  // console.log(data, "dadadada");

  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "数据");

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });

  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  saveAs(blob, `导入行信息_.xlsx`);
};
