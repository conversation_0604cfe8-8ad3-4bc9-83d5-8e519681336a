export const linemixin = {
  data() {
    return {};
  },
  methods: {
    initProjict() {
      this.delLineIdList = [];
      this.bottonValue = true
      this.delLineId = [];
      this.activeProps = {};
      this.errList = [];
      this.editId = "";
      this.editItem = {};
      this.multipleSelection = [];
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.$refs.LineTable.setCurrentRow([]);
      this.$refs.LineTable.clearSelection();
    },
    selectableLine(row) {
      if (this.selectable) {
        return this.selectable(row);
      } else {
        return row?.ABGRU !== "12";
      }
    },
    replaceValue(headerItem, type) {
      const value = this.searchHeaderTihuan[headerItem.prop];
      if (!value) {
        this.$modal.msgError("请选择数据");
        return;
      }
      let list = [...this.pageLineData];
      if (type === "2") {
        list = [...this.salesOrderLineList];
      }

      if (!list || list.length === 0) {
        this.$modal.msgError("暂无数据替换");
        return;
      }

      let text = type === "2" ? "全部" : "当前";
      this.$confirm(
        `是否要替换${text}页面的${headerItem?.label}的值?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          try {
            const id = this.keyId || "id";

            if (this.getParamsLoading) return;
            this.getParamsLoading = true;
            const data = list.map(({ [id]: d }) => d);
            const salesOrderLineList = [...this.salesOrderLineList].map(
              (item) => {
                // if(item.)
                Object.keys(item).forEach((key) => {
                  if (key === headerItem.prop && data.includes(item[id])) {
                    item[key] = value;
                  }
                });
                return item;
              }
            );
            this.salesOrderLineList = salesOrderLineList;
            this.searchHeaderTihuan = {};
            this.inputRefreshList();
            this.$modal.msgSuccess("替换成功");

            setTimeout(() => {
              this.getParamsLoading = false;
            }, 500);
          } catch (error) {
            this.getParamsLoading = false;
            console.log(error, "error");
          }
        })
        .catch((error) => {
          this.searchHeaderTihuan = {};

          console.log(error, "rrrrr");
        });
    },
  },
};
