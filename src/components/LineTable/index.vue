<template>
  <div class="addtable" v-loading="loading">
    <div
      style="margin-bottom: 20px; min-height: 40px; position: relative"
      v-if="pageType !== 'look'"
      class="headerLine"
    >
      <el-button
        v-if="btnsunits.add.isShow"
        v-hasPermi="btnsunits.add.hasPermi"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click.stop="addLineData"
        >添加</el-button
      >
      <el-button
        v-if="btnsunits.batchDelete.isShow"
        v-hasPermi="btnsunits.batchDelete.hasPermi"
        type="danger"
        icon="el-icon-delete"
        size="mini"
        :disabled="salesOrderLineList.length === 0"
        @click="delMore"
        >批量删除</el-button
      >
      <el-button
        type="danger"
        size="mini"
        v-if="isShowCancel"
        @click.stop="handFuntion('cancel', '')"
        >批量取消</el-button
      >
      <el-button
        v-if="downKey.key && btnsunits.add.isShow"
        type="primary"
        icon="el-icon-download"
        size="mini"
        @click.stop="downLine"
        >下载模板</el-button
      >
      <UploadExcel
        v-if="btnsunits.uploadExcel && btnsunits.uploadExcel.isShow"
        :parantdataInfo="parantdataInfo"
        :checkUpload="checkUpload"
        @getUploadExcel="getUploadExcel"
      />
      <slot name="lineTableHeader"></slot>

      <div class="lineBtnright" v-show="isShowBtnRight">
        <div class="box">
          <el-switch
            v-model="bottonValue"
            style="margin-top: 4px; margin-right: 10px"
          >
          </el-switch>
          <div
            :class="bottonValue ? 'search' : 'search search2'"
            @click="bottonValue = !bottonValue"
          >
            {{ bottonValue ? "搜索" : "重置" }}
          </div>
        </div>

        <el-tooltip class="item" effect="dark" content="刷新" placement="top">
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="refreshList"
          ></el-button>
        </el-tooltip>
        <el-tooltip
          class="item"
          effect="dark"
          content="配置表头"
          placement="top"
        >
          <el-button
            v-if="showHeaderConfig"
            icon="el-icon-s-unfold"
            size="mini"
            @click="getColumnCinfig()"
          ></el-button>
        </el-tooltip>
      </div>
    </div>

    <el-table
      border
      :data="isManualPage ? pageLineData : salesOrderLineList"
      highlight-current-row
      ref="LineTable"
      :row-key="getKeyId"
      :maxHeight="height"
      v-bind="tableProps"
      @selection-change="handleSelectionChange"
      @current-change="handleCurrentChange"
      @cell-dblclick="cellDblclick"
      v-loading="getParamsLoading"
    >
      <el-table-column
        v-if="showSelection"
        :reserve-selection="true"
        type="selection"
        width="50"
        align="center"
        fixed="left"
        :selectable="selectableLine"
      >
      </el-table-column>
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="50"
        fixed="left"
        v-if="indexXH"
      />
      <el-table-column
        v-for="(item, index) in salesOrderLineCloumn.filter(
          ({ visible = '' }) => visible !== '1'
        )"
        v-bind="item"
        :key="index"
      >
        <template #header>
          <div
            v-if="!bottonValue && item.prop !== 'caozuo'"
            style="display: flex"
          >
            <ComSearchItem
              :item="{ ...item, attr: { disabled: false }, append: false }"
              v-model="searchHerderObj[item.prop]"
              @keyup="inputRefreshList(item, searchHerderObj[item.prop])"
              @change="changeHeader($event, item)"
            />

            <el-popover
              :popperOptions="{
                boundariesElement: 'body',
                gpuAcceleration: false,
              }"
              placement="top"
              width="460"
              trigger="hover"
            >
              <div style="display: flex">
                <ComSearchItem
                  :item="{
                    ...item,
                    attr: { disabled: false, 'popper-append-to-body': false },
                    append: false,
                  }"
                  v-model="searchHeaderTihuan[item.prop]"
                  @change="changeHeaderSearch($event, item)"
                />
                <el-button
                  @click="replaceValue(item,'1')"
                  icon="el-icon-refresh"
                  style="margin-right: 10px;"
                  size="mini"
                  >替换当前页</el-button
                >
                <el-button
                  @click="replaceValue(item,'2')"
                  icon="el-icon-refresh"
                  size="mini"
                  >替换全部</el-button
                >
              </div>
              <el-button
                style="height: 100%"
                slot="reference"
                v-if="item.isShowPrebtn"
                icon="el-icon-refresh"
                size="mini"
              ></el-button>
            </el-popover>
          </div>
          <span v-else>{{ item.label }}</span>
        </template>

        <template slot-scope="scope">
          <slot
            v-if="item.slotName"
            :name="item.slotName"
            v-bind="scope.row"
          ></slot>
          <template v-else>
            <template
              v-if="
                editId === scope.row[keyId] &&
                !(item.attr && item.attr.disabled) &&
                !scope.row.disabled &&
                scope.row.ABGRU !== '12'
              "
            >
              <ComSearchItem
                :className="
                  requiredKeys.includes(item.prop) && !editItem[item.prop]
                    ? 'errorColor'
                    : ''
                "
                :tableList="salesOrderLineList"
                :item="item"
                :row="scope.row"
                v-model="scope.row[item.prop]"
                @keyup="keyup($event, item, item.prop)"
                @handClickSearch="handleMain(scope.row, item)"
                @blur="blur(item, scope.row)"
                @change="change($event, item, scope.row)"
                @closeProp="closeProp($event, scope.row)"
              />
            </template>
            <dict-tag
              v-else-if="item.optionsCode && dict.type[item.optionsCode]"
              :options="dict.type[item.optionsCode] || []"
              :value="scope.row[item.prop]"
            />
            <span v-else>{{ toLocaleString(item, scope.row) }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="160px"
        fixed="right"
        v-if="btnsunits.caozuoShow.isShow && pageType !== 'look'"
        v-bind="btnsunits.caozuoShow"
      >
        <template slot-scope="{ row }">
          <div class="lineBtn">
            <el-button
              v-if="btnsunits.deleteLine.isShow && row.id && row.ABGRU !== '12'"
              v-hasPermi="btnsunits.deleteLine.hasPermi"
              type="danger"
              size="mini"
              @click.stop="handFuntion('del', row)"
              >删除</el-button
            >
            <el-button
              v-if="isShowCancel"
              type="danger"
              size="mini"
              @click.stop="handFuntion('cancel', row)"
              >取消</el-button
            >
            <slot name="lineBtn" v-bind="row"></slot>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="isShowPage && pagination.total > 0"
      :current-page="pagination.pageNum"
      :page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="
        (val) => {
          changePageSize(pagination.pageNum, val);
        }
      "
      @current-change="
        (val) => {
          changePageSize(val, pagination.pageSize);
        }
      "
    >
    </el-pagination>
    <ComModalSearch
      v-if="
        salesOrderLineCloumn.findIndex(
          ({ prop, append }) =>
            (changeProp[prop] || prop) === item.key && append
        ) > -1
      "
      v-for="(item, index) in componentsModal"
      :key="index"
      :ref="'ComModalSearch' + item.key"
      v-bind="item"
      :searchData="SearchDataObj[item.key]"
      :column="ColumnsObj[item.key]"
      @handenOk="(val, val2) => handenOk(item.key, val, val2)"
      :getTableList="getTableList"
    />
    <ComModalTable
      ref="lineComModalSearch"
      :isPage="false"
      :column="modalColumn"
      :title="'导入信息提示'"
      :searchProps="{ showHeader: false }"
      @handenOk="errList = []"
    >
      <el-button type="primary" size="mini" @click.stop="excelDown"
        >导出excel</el-button
      >
    </ComModalTable>
    <DrawerColumn ref="DrawerColumn" @handOk="handOk" />
  </div>
</template>
<script>
import ComModalSearch from "@/components/ComModalSearch/index.vue";
import ComSearchItem from "@/components/ComSearch/ComSearchItem";
import UploadExcel from "@/components/UploadExcel/index.vue";
import { listOrgRelation } from "@/api/sales/orgRelation/orgRelation";
import { setColumnsOptions } from "@/utils/location";
import { Loading } from "element-ui";
import {
  activeHandok,
  changeNewLineList,
  checkZZMATNR,
  clearCheckZZMATNRProp,
  getColumnCinfig,
} from "./config";
import {
  getLookcolumns,
  getExcelId,
  trim,
  debounce,
  // setTableDictValue,
} from "@/utils/index.js";
import {
  getDownKey,
  checkExportList,
  checkLineRequeck,
  exportExcel,
  c_list,
  getLineParams,
  initbtn,
  propsObj,
} from "./units";
import {
  componentsModal,
  ColumnsObj,
  getRequestData,
  changeProp,
  SearchDataObj,
} from "../ComSearch/config";
import { getRequestList } from "../ComSearch/requestConf";
import { setColumns } from "../ComTable/config";
import { linemixin } from "./lineTable";
import { formatNowDate, nextFormatNowDate } from "@/utils";
export default {
  name: "LineTable",
  components: {
    ComModalSearch,
    ComSearchItem,
    UploadExcel,
    DrawerColumn: () => import("@/components/DrawerColumn/index.vue"),
    ComModalTable: () =>
      import("@/components/ComModalSearch/ComModalTable.vue"),
  },
  mixins: [linemixin],
  dicts: [
    "ekgrp",
    "meins",
    "bwart",
    "zzmlylx",
    "beskz",
    "table_fixed",
    "y_n",
    "fumigation",
    "abgru",
  ],
  props: propsObj,
  data() {
    return {
      changeProp,
      ColumnsObj,
      SearchDataObj,
      componentsModal,
      pageType: "",
      loading: false,
      // salesOrderLineCloumn: getLookcolumns(list, this),
      salesOrderLineList: this.$props.lineList,
      editId: "",
      editItem: {},
      multipleSelection: [],
      pagination: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      searchHerderObj: {},
      bottonValue: true,
      // 接口删除的id数据
      delLineId: [],
      // 删除的列表
      delLineIdList: [],
      activeProps: {},
      // 获取接口loading
      getParamsLoading: false,
      postponeEdit: {},
      downKey: {},
      errList: [],
      reshListData: undefined,
      tableColumn: [],
      height: 540,
      searchHeaderTihuan: {},
    };
  },
  watch: {
    lineList(val) {
      this.pageType = this.$route?.params?.type || "";
      this.salesOrderLineList = val;
    },
    salesOrderLineList(val) {
      this.$emit("changeLineList", val);
    },
    bottonValue(val) {
      this.reshListData = undefined;
      this.searchHerderObj = [];
      this.closeEdit2();
    },
  },
  computed: {
    salesOrderLineCloumn() {
      const list = this.lineCloumn.filter(({ prop }) => prop !== "caozuo");
      let data = getLookcolumns(list, this);
      data = setColumns(data, this.keyId, this.tableColumn);
      return data;
    },
    modalColumn() {
      const list = this.lineCloumn.filter(({ prop }) =>
        this.requiredKeys.includes(prop)
      );
      return [...list, ...c_list];
    },
    pageLineData() {
      const pageNum = this.pagination.pageNum;
      const pageSize = this.pagination.pageSize;
      const num1 = (pageNum - 1) * pageSize;
      const num2 = pageNum * pageSize;
      let list = [...JSON.parse(JSON.stringify(this.salesOrderLineList))];

      if (!this.bottonValue && this.reshListData) {
        list = this.reshListData;
      }

      const data = list.slice(num1, num2);

      if (!this.bottonValue && this.reshListData) {
        this.pagination.total = list?.length || 0;
      } else {
        this.pagination.total = this.salesOrderLineList.length || 0;
      }

      return data;
    },
    btnsunits() {
      const obj = {
        ...initbtn,
        ...this.$props.btnProps,
      };

      return obj;
    },
    dictDataOptions() {
      const data = this.$store.state.dictData.dictDataOptions;
      return data;
    },
    newlineList() {
      const data = changeNewLineList(this.salesOrderLineList);
      return data;
    },
    parantdataInfo() {
      const parantdataInfo = this?.$parent?.$refs?.ComSearchInfo?.dataInfo;
      return parantdataInfo || this?.$parent?.detail;
    },
  },
  created() {
    this.pageType = this.$route?.params?.type || "";
    let downKey = getDownKey(location.href);
    this.downKey = downKey;
  },
  methods: {
    getKeyId(row) {
      return `${row[this.keyId || "id"]}`;
    },
    getColumnCinfig() {
      getColumnCinfig(this, this.salesOrderLineCloumn);
    },
    async keyup(e, item, prop) {
      if (this.getParamsLoading) return;
      this.getParamsLoading = true;
      if (!this.editItem[prop]) return;
      this.activeProps = item;
      if (prop === "ZZMATNR" && checkZZMATNR(this)) {
        this.clearCheckZZMATNRProp(prop);
        return;
      }

      try {
        // console.log(this.editItem,'edeeedeee')
        const obj = this.getWeark();
        await getRequestList(this, prop, obj);
        if (prop === "ZZMATNR" && checkZZMATNR(this)) {
          this.clearCheckZZMATNRProp(prop);
          return;
        }
        this.salesOrderLineList = this.salesOrderLineList.map((item) => {
          if (item[this.keyId] === this.editItem[this.keyId]) {
            return {
              ...this.editItem,
            };
          }
          return item;
        });
        this.getParamsLoading = false;
      } catch (error) {
        this.getParamsLoading = false;
      }
    },
    // 删除物料信息
    clearCheckZZMATNRProp(prop) {
      setTimeout(() => {
        clearCheckZZMATNRProp(this);
        this.salesOrderLineList = this.salesOrderLineList.map((item) => {
          if (item[this.keyId] === this.editItem[this.keyId]) {
            return {
              ...this.editItem,
            };
          }
          return item;
        });
        this.getParamsLoading = false;
      }, 200);
    },
    // 双击提示

    cellDblclick(row) {
      // console.log(data, "dadada");
      if (this.editId && this.checkItem("")) return;
    },
    // 添加子列表
    addChildren(row) {
      // console.log(row, "row");
      if (this.checkList()) return;
      if (row[this.keyId]) {
        let list = [...this.salesOrderLineList];
        const obj = {};
        const time = new Date();
        const id = `${time.getTime()}`.slice(4);
        obj[this.parantId] = row[this.keyId];
        obj[this.keyId] = `child${id}`;
        obj.id = `child${id}`;
        list = list.map((item) => {
          if (item[this.keyId] === row[this.keyId]) {
            item.children.push(obj);
          }
          return item;
        });
        this.editId = obj[this.keyId];
        this.editItem = obj;
        this.salesOrderLineList = list;
      }
    },
    addLineData() {
      if (this.getParamsLoading) return;
      // this.getParamsLoading = true;
      if (this.checkList()) return;

      const list = [...this.salesOrderLineList];
      const oldObj = this.dataProps?.initLineData || {};
      const obj = {
        ...oldObj,
      };
      this.salesOrderLineCloumn.forEach((item, index) => {
        let time = new Date();
        const id = `${time.getTime()}${index}`.slice(4);
        obj[this.keyId] = `P${id}`;
        obj.id = `P${id}`;
      });

      // 树格式列表
      if (this.tableProps?.treeProps?.children) {
        obj.children = [];
      }

      this.salesOrderLineList = [...list, obj];

      this.$refs.LineTable.setCurrentRow(obj);
      this.$refs.LineTable.clearSelection();

      this.pagination.total = list.length;
      this.editId = obj[this.keyId];
      this.editItem = obj;
      // console.log(this.editId, this.editItem, "00000");
      this.activeProps = {};
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleCurrentChange(data) {
      if (this.pageType === "look" || !data) return;
      if (this.editId && this.checkItem("1")) return;

      if (this.getParamsLoading) {
        // 暂缓选择的数据
        this.postponeEdit = {
          editId: data[this.keyId],
          editItem: data,
          activeProps: {},
        };
        return;
      }

      this.editId = data[this.keyId];
      this.editItem = data;
      this.activeProps = {};
    },

    blur(item, editItem) {
      // console.log(item,this.checkNum,'ceheeeeee')
      if (this.checkNum) {
        this.checkNum(item, editItem);
        return;
      }
      if (item.blur) {
        item.blur(item, editItem, this);
      }
      if (item.append && editItem[item.prop] && !editItem.MEINS) {
        this.keyup("blur", item, item.prop);
      }
    },
    closeProp(item, editItem) {
      if (item.prop === "ZZMATNR") {
        editItem.ZZMATNR = undefined;
        editItem.MAKTX = undefined;
        editItem.MEINS = undefined;
        editItem["MEINS,MSEHT"] = undefined;

        if (item?.backProps && item.backProps.length) {
          item?.backProps.forEach((item) => {
            editItem[item.prop] = undefined;
            editItem[`${item.prop},${item.backLable}`] = undefined;
          });
        }
      } else if (item?.backProps && item.backProps.length) {
        const { prop, backLable } = item.backProps[0];
        const key = `${prop},${backLable}`;
        editItem[item.prop] = undefined;
        editItem[key] = undefined;
      } else {
        editItem[item.prop] = undefined;
      }
      this.editItem = editItem;
      // console.log(val,item,'0000')
    },
    // 校验导入
    checkUpload() {
      const parantdataInfo =
        this?.$parent?.$refs?.ComSearchInfo?.dataInfo || this?.$parent?.detail;
      const wrtWERKS =
        parantdataInfo?.WERKS ||
        parantdataInfo?.werks ||
        this?.$parent?.detail?.WERKS ||
        this.searchDetail?.werks;
      ("");
      // console.log(wrtWERKS, "wrtWERKS");
      if (!wrtWERKS) {
        this.$modal.msgError("请先选择组织或工厂");
        return true;
      }
      if (
        this.checkUploadOther &&
        this.checkUploadOther(parantdataInfo, this)
      ) {
        return true;
      }
      return false;
    },

    handleMain(data, item) {
      this.activeProps = item;
      this.editItem = data;
      const changePropName = changeProp[item.prop] || item.prop;
      const parantdataInfo = this?.$parent?.$refs?.ComSearchInfo?.dataInfo;
      const wrtWERKS =
        parantdataInfo?.WERKS ||
        parantdataInfo?.werks ||
        this?.$parent?.detail?.WERKS ||
        data?.WERKS ||
        this.searchDetail?.werks;
      ("");

      if (["LGORT", "ZZMATNR", "KUNNR"].includes(changePropName) && !wrtWERKS) {
        this.$modal.msgError("请先选择组织或工厂");
        return;
      }

      if (["KOSTL"].includes(changePropName) && !parantdataInfo?.companyCode) {
        this.$modal.msgError("请先公司");
        return;
      }

      const modalDatalist = [...this.componentsModal];
      const index = modalDatalist.findIndex(
        ({ key }) => key === changePropName
      );

      if (index > -1) {
        try {
          const { rowProps } = modalDatalist[index];
          const name = "ComModalSearch" + changePropName;
          const ref = this.$refs[name];
          if (ref) {
            this.$nextTick(() => {
              ref[0].dialogVisible = true;
              ref[0].$refs?.mainrList?.submitEmits();
              ref[0].currentRow = {};
            });
          }
        } catch (error) {
          console.log(error, "error");
        }
      } else {
        this.$emit("handClickSearch", item);
      }
    },

    closeEdit() {
      // console.log(this.editId,this.checkList("active"),'this.checkList("active")')
      if (!this.editId) return;
      if (this.checkList("active")) return;
      this.editId = "";
      this.editItem = {};
      this.activeProps = {};
      this.$refs.LineTable.setCurrentRow([]);
    },
    closeEdit2() {
      // console.log(this.editId,this.checkList("active"),'this.checkList("active")')
      this.editId = "";
      this.editItem = {};
      this.activeProps = {};
      this.$refs.LineTable.setCurrentRow([]);
    },
    change(val, item, editItem) {
      // if (val && editItem[item.prop] && (val === editItem[item.prop])) return;

      editItem[item.prop] = val;
      if (!val && item.prop !== "ZZMATNR") {
        if (item?.backProps && item.backProps.length) {
          const { backProp, backLable } = item.backProps[0];
          const key = `${backProp},${backLable}`;
          editItem[key] = undefined;
        }
      }

      // 修改行
      if (item?.changeLineItem) {
        item?.changeLineItem(val, item, editItem, this);
      }

      if (item?.setTableLine) {
        const PsalesOrderLineList = item.setTableLine(editItem, val, this);
        this.salesOrderLineList = PsalesOrderLineList;
      } else {
        this.salesOrderLineList = [...this.salesOrderLineList].map((item) => {
          if (item[this.keyId] === this.editItem[this.keyId]) {
            return {
              ...editItem,
            };
          }
          return item;
        });
      }

      this.editItem = editItem;
    },

    checkItem(active) {
      if (!this.editId) return false;
      try {
        const editItem = { ...this.editItem };
        const arr = [];
        // console.log(this.requiredKeys, "requiredKey11111s");
        this.requiredKeys.forEach((key) => {
          if (!editItem[key]) {
            arr.push(key);
          }
        });
        let texts = [];
        // console.log(arr, "0000000");

        if (arr && arr.length) {
          this.salesOrderLineCloumn.forEach((item) => {
            if (arr.includes(item.prop)) {
              texts.push(item.label);
            }
          });
          const text = texts.join(",");
          if (!active) {
            this.$message({
              type: "error",
              message: `${text}不能为空`,
            });
          }

          return true;
        }
      } catch (error) {
        console.log(error, "error");
      }

      return false;
    },
    // 校验数据是否为空
    checkList(active, checkListData) {
      const list = checkListData || [...this.salesOrderLineList];
      const arr = [];
      if (list && list.length) {
        list.forEach((item) => {
          if (item.children) {
            item.children.forEach((item2) => {
              this.requiredKeys.forEach((key) => {
                // console.log(item[key], key, "0000000");
                if (!item2[key]) {
                  arr.push(key);
                }
              });
            });
          }
          this.requiredKeys.forEach((key) => {
            // console.log(item[key], key, "0000000");
            if (!item[key]) {
              arr.push(key);
            }
          });
        });
      }
      let texts = [];
      // console.log(arr, "0000000");
      if (arr && arr.length) {
        this.salesOrderLineCloumn.forEach((item) => {
          if (arr.includes(item.prop)) {
            texts.push(item.label);
          }
        });
        const text = texts.join(",");
        if (!active) {
          this.$message({
            type: "error",
            message: `${text}不能为空`,
          });
        }

        return true;
      }

      return false;
    },
    toLocaleString(item, row) {
      return row[item.prop];
    },

    delMore() {
      let text = "勾选";
      if (!this.multipleSelection || this.multipleSelection.length === 0) {
        text = "全部";
      }
      this.$confirm(`是否删除${text}的数据?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const dataIds = [];
        const unDataIds = [];
        const unDataIdsList = [];
        let deleList =
          !this.multipleSelection || this.multipleSelection.length === 0
            ? this.salesOrderLineList
            : this.multipleSelection;

        deleList = deleList.filter((item) => !item[this.POSNRKey]);
        if (deleList?.length === 0) {
          this.$message({
            type: "error",
            message: "暂可删除的数据数据!",
          });

          return;
        }
        deleList.forEach((item) => {
          dataIds.push(item[this.keyId]);
          if (item[this.POSNRKey]) {
            // dataIds.push(item[this.keyId]);
            unDataIds.push(item[this.keyId]);
            unDataIdsList.push({
              ...item,
              delFlag: "2",
            });
          }
        });

        this.delLineId = [...this.delLineId, ...unDataIds];
        this.delLineIdList = [...this.delLineIdList, ...unDataIdsList];

        let list = this.salesOrderLineList.filter(
          (item) => !dataIds.includes(item[this.keyId])
        );

        // 删除子数据
        if (this.tableProps?.treeProps?.children) {
          list = list.map((item) => {
            item.children = item.children.filter(
              (item) => !dataIds.includes(item[this.keyId])
            );
            return item;
          });
        }
        this.salesOrderLineList = list;
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.$refs.LineTable.clearSelection();
        this.editItem = {};
        this.editId = "";
        this.activeProps = {};
      });
    },

    handFuntion(type, data) {
      if (["cancel"].includes(type)) {
        let text = data ? "取消该行" : "批量取消";
        let cancelList = [];
        if (data) {
          cancelList = [data];
        } else {
          cancelList =
            !this.multipleSelection || this.multipleSelection.length === 0
              ? this.salesOrderLineList
              : this.multipleSelection;
        }
        this.$confirm(`是否${text}数据?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.cancelFuntion("cancel", cancelList, this.salesOrderLineList);
        });
      } else if (type === "add") {
        this.addLineData();
      } else if (type === "del") {
        this.$confirm("是否删除该行数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          if (data[this.parantId]) {
            const list = [...this.salesOrderLineList].map((item) => {
              if (item[this.keyId] === data[this.parantId]) {
                item.children = item.children.filter(
                  (item2) => item2[this.keyId] !== data[this.keyId]
                );
              }
              return item;
            });

            this.salesOrderLineList = list;
          } else {
            this.salesOrderLineList = this.salesOrderLineList.filter(
              (item) => item[this.keyId] !== data[this.keyId]
            );
          }

          if (data[this.POSNRKey]) {
            this.delLineId = [...this.delLineId, data[this.keyId]];
            this.delLineIdList = [
              ...this.delLineIdList,
              { ...data, delFlag: "2" },
            ];
          }

          this.editItem = {};
          this.editId = "";
          this.activeProps = {};
          this.$refs.LineTable.clearSelection();
          this.$refs.LineTable.setCurrentRow([]);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        });
      }
    },
    changePageSize(pageNum, pageSize) {
      let num = pageNum;
      if (pageSize !== this.pagination.pageSize) {
        num = 1;
      }
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = num;
      this.$emit("changePageSize", { pageNum: num, pageSize });
    },

    getWeark() {
      let params = {};
      const info =
        this?.$parent?.$refs?.ComSearchInfo?.dataInfo ||
        this?.$parent?.detail ||
        this.searchDetail;
      const wrtWERKS =
        this?.$parent?.$refs?.ComSearchInfo?.dataInfo?.WERKS ||
        this?.$parent?.detail?.WERKS ||
        this.searchDetail?.werks;

      params = getLineParams(info, wrtWERKS, this);

      if (this.activeProps.setPropParams) {
        let ohterParams = this.activeProps.setPropParams(
          this.activeProps,
          info,
          params
        );
        params = {
          ...params,
          ...ohterParams,
        };
      }

      return params;
    },
    async getTableList(params) {
      // console.log(params,'pppppp')
      try {
        const changePropName =
          changeProp[this.activeProps.prop] || this.activeProps.prop;
        const obj = this.getWeark();
        // console.log(obj,'okkkkkk')
        const query = {
          ...params,
          ...obj,
        };
        const fun = getRequestData[changePropName];
        if (fun) {
          return fun(query);
        }
        const res = await listOrgRelation(query);
        return res;
      } catch (error) {
        console.log(error, "error");
      }
    },
    handenOk(type, data = {}, data2 = []) {
      if (type === "ZZMATNR" && !this.activeProps?.backProps) {
        this.editItem.ZZMATNR = data?.ZZMATNR;
        this.editItem.MAKTX = data.maktxZh;
        this.editItem.ZZMLYLX = data.ZZMLYLX;
        this.editItem.BESKZ = data.BESKZ;

        this.salesOrderLineList = this.salesOrderLineList.map((item) => {
          if (item[this.keyId] === this.editItem[this.keyId]) {
            return {
              // ...data,
              ...this.editItem,
              MEINS: data?.MSEHT || undefined,
              "MEINS,MSEHT": data?.MEINS
                ? [data?.MEINS, data?.MSEHT]
                : undefined,
            };
          }
          return item;
        });

        this.$forceUpdate();
        // console.log(this.salesOrderLineList,'salesOrderLineList')

        return;
      }
      if (type === "salesOrg") {
        if (!data?.salesOrg) {
          this.$modal.msgError("请选择选择销售组织");
          return;
        }
      }

      activeHandok(this, this.salesOrderLineCloumn, data || {}, data2);

      this.salesOrderLineList = [...this.salesOrderLineList].map((item) => {
        if (item[this.keyId] === this.editItem[this.keyId]) {
          return {
            ...this.editItem,
          };
        } else if (item.children && item.children.length) {
          item.children = item.children.map((item2) => {
            if (item2[this.keyId] === this.editItem[this.keyId]) {
              return {
                ...this.editItem,
              };
            }
            return item2;
          });
        }
        return item;
      });
    },
    refreshList() {
      this.$emit("refreshList");
      this.reshListData = undefined;
      this.searchHerderObj = {};
      this.closeEdit2();
    },
    async getUploadExcel(val) {
      const downloadLoadingInstance = Loading.service({
        text: "正在导入数据，请稍候",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let time = new Date();
      const id = `${time.getTime()}`.slice(4);
      // console.log(val, "val");
      const data = getExcelId(val, this.lineCloumn).map((item, index) => {
        item[[this.keyId]] = `P${id}${index}`;
        item.id = `P${id}${index}`;
        const oldObj = this.dataProps?.initLineData || {};
        const obj = {
          // ...oldObj,
          LPEIN: formatNowDate(),
          ...item,
        };

        return obj;
      });

      if (!data || data.length === 0) {
        this.$modal.msgError("暂无数据导入");
        downloadLoadingInstance.close();
        return;
      }
      try {
        let checkLineList = checkLineRequeck(
          data,
          this.requiredKeys,
          this.lineCloumn,
          this
        );

        if (
          this.checkUploadExcel &&
          this.checkUploadExcel(checkLineList, this)
        ) {
          checkLineList = this.checkUploadExcel(checkLineList, this) || data;
        }

        let checkLineList2 = checkLineList.filter(({ errMsg }) => errMsg);
        let dataList = checkLineList.filter(({ errMsg }) => !errMsg);

        const exData = JSON.parse(JSON.stringify(dataList));
        let errList2 = [...checkLineList2];

        if (exData && exData.length) {
          const [exEewList = [], errList = []] = await checkExportList(
            exData,
            this
          );
          const oldObj = this.dataProps?.initLineData || {};
          const _exEewList = exEewList.map((item) => {
            const obj = {
              ...oldObj,
              ...item,
            };

            return obj;
          });
          const list = [...this.salesOrderLineList];
          const newList = [...list, ..._exEewList];
          this.salesOrderLineList = newList;
          this.pagination.total = newList.length || 0;
          errList2 = [...checkLineList2, ...errList];
        }

        setTimeout(() => {
          downloadLoadingInstance.close();
        }, 200);

        this.errList = errList2;
        if (errList2 && errList2.length) {
          this.$refs.lineComModalSearch.open(errList2);
        }
      } catch (error) {
        downloadLoadingInstance.close();
      }
    },
    downLine() {
      this.download(
        `sales/common/export/${this.downKey.key}`,
        {},
        `${this.downKey.label}模板_.xlsx`
      );
    },
    excelDown() {
      exportExcel(this.modalColumn, this.errList);
    },
    changeHeaderSearch(val, item) {
      this.searchHeaderTihuan = {
        ...this.searchHeaderTihuan,
        [item.prop]: val,
      };
    },
    changeHeader(val, item) {
      this.searchHerderObj = {
        ...this.searchHerderObj,
        [item.prop]: val,
      };
      if (item.type === "select") {
        this.inputRefreshList();
      }
    },
    inputRefreshList(itemObj, text) {
      this.closeEdit2();
      let newData = [...this.salesOrderLineList].filter((item) => {
        const keys = Object.keys(this.searchHerderObj).map((key) => key);

        let flg = false;
        for (let i = 0; i < keys.length; i++) {
          let key = keys[i];
          let value1 = "";
          let value2 = "";

          try {
            value1 = trim(`${item[key]}`)?.toLowerCase();
          } catch (error) {
            value1 = trim(`${item[key]}`);
          }
          try {
            value2 = trim(`${this.searchHerderObj[key]}`)?.toLowerCase();
          } catch (error) {
            value2 = trim(`${this.searchHerderObj[key]}`);
          }

          flg = `${value1}`?.indexOf(`${value2}`) > -1;
        }
        return flg;
      });

      this.reshListData = newData;
    },
    handOk(type, columns) {
      const pathname = this?.columnKey || this?.keyId || location.pathname;
      const cKey = pathname;

      setColumnsOptions(cKey, JSON.parse(JSON.stringify(columns)));
      const data = [];
      this.tableColumn = data;
      //  this.$nextTick(()=>{
      //    this.tableColumn = [...data]
      //    this.$forceUpdate()
      //  })
    },
  },
};
</script>
<style lang="scss" scoped>
.ZZMATNRName {
  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }

  ::v-deep .el-dialog__body {
    padding: 0px;
  }
}

.headerLine {
  padding-right: 180px;

  ::v-deep .el-button {
    margin-bottom: 10px;
  }
}

.lineBtnright {
  position: absolute;
  right: 0px;
  top: 0px;
  display: flex;
  ::v-deep .el-switch__core {
    width: 56px !important;
  }
  .box {
    position: relative;
    display: inline;
  }
  .search {
    position: absolute;
    top: 6px;
    right: 30px;
    font-size: 12px;
    color: #fff;
    cursor: pointer;
  }
  .search2 {
    right: 20px;
  }
}

::v-deep .el-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

::v-deep .el-table__expand-icon--expanded,
::v-deep .el-table__expand-icon {
  .el-icon-arrow-right {
    font-size: 20px;
  }
}

::v-deep .errorColor .el-input__inner {
  border-color: red;
}

.lineBtn {
  display: flex;
}

::v-deep
  .el-table__fixed-body-wrapper
  .el-table__expand-icon
  .el-icon-arrow-right {
  color: #1890ff !important;
}

::v-deep .el-table__row--level-1 .el-icon-arrow-right {
  display: none;
}
</style>
