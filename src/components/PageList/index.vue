<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :showNum="indexShowNum"
      :searchProps="searchProps"
      :isSelection="isSelection"
      :tableProps="tableProps"
      ref="ComTableList"
      @selection-change="handleSelectionChange"
    >
      <template #searchButton v-if="headerBtn && headerBtn.length">
        <el-button
          v-for="(item, index) in headerBtn"
          size="mini"
          :key="index"
          :type="item.type || 'text'"
          :icon="item.icon"
          v-bind="item"
          v-hasPermi="item.hasPermi || []"
          @click="handleHeader(item)"
          >{{ item.name }}</el-button
        >
      </template>

      <template #caozuo="{ row }" v-if="tableRowBtn && tableRowBtn.length">
        <div>
          <el-button
            v-for="(item, index) in tableRowBtn"
            :key="index"
            size="mini"
            :type="item.type || 'text'"
            :icon="item.icon"
            v-bind="item"
            @click="item.click || handleTable(item, row)"
            >{{ item.name }}</el-button
          >
        </div>
      </template>
    </ComTableList>

    <el-dialog
      v-if="detailPageType === 'modal'"
      :title="title + '详情'"
      :visible.sync="open"
      :width="modalWidth"
      class="comdialog"
      append-to-body
      v-loading="loading"
    >
      <ComSearch
        :searchData="headerFormData"
        :searchInfo="headerSearchInfo"
        :labelWidth="labelWidth"
        :showNum="addShowNum"
        :showBtn="false"
        :searchProps="addSearchProps"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        @submitEmits="handDemand"
      ></ComSearch>
      <LineTable
        v-if="lineCloumn && lineCloumn.length"
        :dataProps="dataProps"
        :lineList="lineList"
        :lineCloumn="lineCloumn"
        :requiredKeys="[]"
        :POSNRKey="lineTableProps.rowKey"
        @refreshList="getDetail"
        ref="lineTable"
        :keyId="lineTableProps.rowKey"
        v-bind="lineTableProps"
      >
      </LineTable>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="submitForm"
          >确 定</el-button
        >
        <el-button @click="cancel" size="mini">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";

// import { pageConfig } from "./config";

const footerClounm = {
  name: "caozuo",
  slotName: "caozuo",
  label: "操作",
  fixed: "right",
  width: 120,
};

export default {
  name: "PageNAME",
  props: {
    indexPageConfig: {
      type: Object,
      default: {},
    },
    addPageConfig: {
      type: Object,
      default: {},
    },
  },
  dicts:['ship_method'],
  components: {
    ComTableList,
    ComSearch,
    LineTable,
  },
  data() {
    const {
      columns = [],
      searchData = [],
      searchProps = {},
      tableRowBtn = [],
      detailPageUrl = "",
      detailPageType = "",
      tableProps = {},
      title = "",
      headerBtn = [],
      modalWidth,
      isSelection,
      indexShowNum,
    } = this.$props?.indexPageConfig || {};

    const {
      labelWidth = "90px",
      headerFormData = [],
      headerSearchInfo = {},
      addSearchProps = {},
      addShowNum,
      lineCloumn = [],
      btnProps = [],
      lineTableProps = {},
    } = this.$props.addPageConfig || {};
    const columnsw = columns ? [...columns, footerClounm] : [];

    return {
      indexShowNum,
      headerBtn: headerBtn,
      columns: columnsw, // pageConfig.indexPageConfig.columns || [],
      searchData: searchData || [],
      searchProps: searchProps,
      addSearchProps: addSearchProps,
      tableProps: {
        ...tableProps,
      },
      detailPageType: detailPageType || "modal",
      detailPageUrl: "",
      tableRowBtn: tableRowBtn || [],
      title: title,
      detailPageUrl,
      headerFormData,
      headerSearchInfo,
      open: false,
      modalWidth: modalWidth || "90%",
      loading: false,
      labelWidth,
      isSelection: isSelection || true,
      ids: [],
      addShowNum,
      lineCloumn,
      btnProps,
      lineList: [],
      lineTableProps,
      dataProps: {
        initLineData: {},
      },
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    getPropsPageObj(obj) {},
    selectable() {
      return true;
    },
    handleSelectionChange(selection) {
      this.selectList = selection;
      this.ids = selection.map((item) => item[this.tableProps.rowKey]);
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return this.indexPageConfig.getList(params); // getList(params);
    },
    // 点击表格列表
    handleTable(item, row = {}) {
      this.clickType = item.clickType;
      if (item.clickType !== "del") {
        if (this.lineCloumn && this.lineCloumn.length) {
          this.getDetail(row);
        } else {
          this.open = true;
          this.headerSearchInfo = row;
        }
      }
      if (item.clickType === "del") {
        this.handleDelete(row);
      }
    },
    async getDetail(row) {



      try {
        if (this.loading) return;
        this.loading = true;
        this.$refs?.lineTable?.initProjict()
        const res = await this.addPageConfig.detailFunction(
          row[this.tableProps.rowKey]
        );
        if (res.code === 200 && res.data) {
          let list = res.data[this.lineTableProps.listKey];
          this.lineList = list.map((item, index) => {
            item.id = `${item[this.lineTableProps.rowKey]}-${index}`;
            item[this.lineTableProps.rowKey] =
              item[this.lineTableProps.rowKey] ||
              `${item[this.lineTableProps.rowKey]}-${index}`;
            return item;
          });
          this.headerSearchInfo = res.data;
          this.open = true;
        }
        this.loading = false;
      } catch (error) {
        console.log(error, "error");

        this.loading = false;
      }
    },
    // 删除
    handleDelete(row) {
      const werksIds = row[this.tableProps.rowKey] || this.ids;
      this.$modal
        .confirm(
          `是否确认删除${this.title}信息编号为"` + werksIds + '"的数据项？'
        )
        .then(async () => {
          let res = await this.addPageConfig.delFunction(werksIds);
          if (res.code === 200) {
            this.$modal.msgSuccess(res?.msg || "操作成功");
            this.$refs.ComTableList.submitEmits();
          }
        });
    },

    // 顶部按钮
    handleHeader(item) {
      // console.log(item,'0000')
      if(item.click){
        item.click(this)
        return
      }
      if (item.clickType === "add") {
        this.open = true;
        this.headerSearchInfo = {};
      }
      if (item.clickType === "del") {
        if(!this.ids || this.ids.length === 0){

          this.$modal.msgError('请选择需要删除的数据');

          return
        }
        this.handleDelete({});
      }
      if (item.clickType === "export") {
        this.handleExport(item);
      }
    },

    submitForm() {
      this.$refs.ComSearchInfo.onSubmit("");
    },
    // 弹窗确认按钮
    async handDemand(form) {
      let fun = this.addPageConfig.addFunction;

      if (form[this?.tableProps?.rowKey]) {
        fun = this.addPageConfig.updataFuntion;
      }

      const delLineIdList = this.$refs.lineTable?.delLineIdList.map((item)=>{
        item.delFlag = '2'
        return item
      })


      if (this.lineCloumn && this.lineCloumn.length) {
        let linelist = this.$refs.lineTable.newlineList;
        linelist = linelist.map((item) => {
          item[this.lineTableProps.rowKey] = item[this.tableProps.rowKey]
            ? item[this.lineTableProps.rowKey]
            : undefined;
          return item;
        });

        console.log(delLineIdList,'delLineIdList')
        form[this.lineTableProps.listKey] = [...linelist,...delLineIdList];
      }

      try {
        if (this.loading) return;
        this.loading = true;
        let res = await fun(form);
        this.loading = false;

        if (res.code === 200) {
          this.$modal.msgSuccess(res?.msg || "操作成功");
          this.$refs.ComTableList.submitEmits();
          this.cancel();
        }
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    cancel() {
      this.open = false;
    },
    /** 导出按钮操作 */
    handleExport(item) {
      if (!item?.requestUrl) return;
      let queryParams =
        this.$refs.ComTableList.$refs.searchComTableInfo.changeInfoData();
      this.download(
        item?.requestUrl,
        {
          ...queryParams,
        },
        `${this.title}_.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.comdialog {
  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }
  ::v-deep .el-dialog__body {
    padding: 0px 20px;
    min-height: 558px;
    height: calc(100vh - 120px);
    overflow: auto;
  }
}
</style>
