import Layout from "@/layout";
// 公共路由

export const Inventory = [
  {
    path: "/inventory/moving-detail",
    component: Layout,
    hidden: true,
    permissions: ["moving","Inventory:inventoryManage:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/moving/add.vue"),
        name: "MovingDetail",
        meta: { title: "移库管理详情", activeMenu: "/inventory/moving" },
      },
    ],
  },
    {
    path: "/inventory/store/orderNoteStore-detail",
    component: Layout,
    hidden: true,
    permissions: ["OrderNoteStore"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/store/orderNoteStore/add.vue"),
        name: "OrderNoteStoreDetail",
        meta: { title: "采购交货入库详情", activeMenu: "/inventory/store/orderNoteStore" },
      },
    ],
  },

  {
    path: "/inventory/store/purOrderStore-detail",
    component: Layout,
    hidden: true,
    permissions: ["PurOrderStore",],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/store/purOrderStore/add.vue"),
        name: "PurOrderStoreDetail",
        meta: { title: "采购订单入库详情", activeMenu: "/inventory/store/purOrderStore" },
      },
    ],
  },

    {
    path: "/inventory/store/movingStore-detail",
    component: Layout,
    hidden: true,
    permissions: ["MovingStore",],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/store/movingStore/add.vue"),
        name: "MovingStoreDetail",
        meta: { title: "移库入库详情", activeMenu: "/inventory/store/movingStore" },
      },
    ],
  },

     {
    path: "/inventory/store/allotStore-detail",
    component: Layout,
    hidden: true,
    permissions: ["AllotStore",],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/store/allotStore/add.vue"),
        name: "AllotStoreDetail",
        meta: { title: "调拨入库详情", activeMenu: "/inventory/store/allotStore" },
      },
    ],
  },

   {
    path: "/inventory/outbound/salesOut-detail",
    component: Layout,
    hidden: true,
    permissions: ["sales/salesDelivery:salesDelivery:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/outbound/salesOut/add.vue"),
        name: "OutboundDetail",
        meta: { title: "销售出库详情", activeMenu: "/inventory/outbound/salesOut" },
      },
    ],
  },
    {
    path: "/inventory/outbound/movingOut-detail",
    component: Layout,
    hidden: true,
    permissions: ["MovingOut"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/outbound/movingOut/add.vue"),
        name: "MovingOutDetail",
        meta: { title: "移库出库详情", activeMenu: "/inventory/outbound/movingOut" },
      },
    ],
  },

      {
    path: "/inventory/outbound/allotOut-detail",
    component: Layout,
    hidden: true,
    permissions: ["AllotOut"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/outbound/allotOut/add.vue"),
        name: "AllotOutDetail",
        meta: { title: "调拨出库详情", activeMenu: "/inventory/outbound/allotOut" },
      },
    ],
  },

  {
    path: "/inventory/allot/allotApply-detail",
    component: Layout,
    hidden: true,
    permissions: ["Allot"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/allot/add.vue"),
        name: "AllotDetail",
        meta: { title: "调拨申请详情", activeMenu: "/inventory/allot/allotApply" },
      },
    ],
  },

  
     {
    path: "/inventory/allot/allotApproved-detail",
    component: Layout,
    hidden: true,
    permissions: ["AllotApproved",],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/allot/allotApproved/add.vue"),
        name: "AllotStoreDetail",
        meta: { title: "调拨审批详情", activeMenu: "/inventory/allot/allotApproved" },
      },
    ],
  },


   {
    path: "/inventory/picking-detail",
    component: Layout,
    hidden: true,
    permissions: ["Picking"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/picking/add.vue"),
        name: "PickingDetail",
        meta: { title: "领料管理详情", activeMenu: "/inventory/picking" },
      },
    ],
  },

     {
    path: "/inventory/check-detail",
    component: Layout,
    hidden: true,
    permissions: ["Check"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/check/add.vue"),
        name: "CheckDetail",
        meta: { title: "盘点管理详情", activeMenu: "/inventory/check" },
      },
    ],
  },


  {
    path: "/inventory/assembly-detail",
    component: Layout,
    hidden: true,
    permissions: ["Assembly"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/Inventory/assembly/add.vue"),
        name: "AssemblyDetail",
        meta: { title: "组装单详情", activeMenu: "/inventory/assembly" },
      },
    ],
  },

    {
    path: "/masterdata/assemblyPart-detail",
    component: Layout,
    hidden: true,
    permissions: ["master:AssemblySheet:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/masterdata/assemblyPart/add.vue"),
        name: "AssemblyPartDetail",
        meta: { title: "组装单详情", activeMenu: "/masterdata/assemblyPart" },
      },
    ],
  },
   {
    path: "/masterdata/material-detail",
    component: Layout,
    hidden: true,
    permissions: ["master:assemblyPart:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/masterdata/material/add.vue"),
        name: "MaterialDetail",
        meta: { title: "物料主数据详情", activeMenu: "/masterdata/material" },
      },
    ],
  },

  {
    path: "/inventory/scan/scanck-detail",
    component: Layout,
    hidden: true,
    permissions: ["Scanck"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views//Inventory/scan/scanck/add.vue"),
        name: "ScanckDetail",
        meta: { title: "扫码出库详情", activeMenu: "/inventory/scan/scanck" },
      },
    ],
  },

];
