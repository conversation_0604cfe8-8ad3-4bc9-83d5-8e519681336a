import Layout from "@/layout";
// 公共路由

export const salesRoutes = [
  {
    path: "/sales/demand-detail",
    component: Layout,
    hidden: true,
    permissions: ["sales/demand:demand:list"],
    children: [
      {
        path: "index/:type/:demandHeadId",
        component: () => import("@/views/sales/demand/add.vue"),
        name: "DemandDetail",
        meta: { title: "需求单详情", activeMenu: "/sales/demand" },
      },
    ],
  },

   {
    path: "/sales/SalesOrder-detail",
    component: Layout,
    hidden: true,
    permissions: ["sales/salesOrder:head:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/sales/salesOrder/add.vue"),
        name: "SalesOrderDetail",
        meta: { title: "销售订单详情", activeMenu: "/sales/salesOrder" },
      },
    ],
  },

   {
    path: "/sales/purDelivery-detail",
    component: Layout,
    hidden: true,
    permissions: ["sales/purDelivery:purDelivery:list","sales/salesDelivery:salesDelivery:list","sales/salesDelivery:salesDelivery:query"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/sales/purDelivery/add.vue"),
        name: "PurDeliveryDetail",
        meta: { title: "销售交货单详情", activeMenu: "/sales/purDelivery" },
      },
    ],
  },

   {
    path: "/sales/newproduct/launch-detail",
    component: Layout,
    hidden: true,
    permissions: ["system:newConfig:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/sales/newproduct/launch/add.vue"),
        name: "LaunchDetail",
        meta: { title: "新品投放详情", activeMenu: "/sales/newproduct/launch" },
      },
    ],
  },
];
