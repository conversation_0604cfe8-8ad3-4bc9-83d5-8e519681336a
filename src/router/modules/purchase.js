import Layout from "@/layout";
// 公共路由

export default [
  {
    path: "/purchase/purApply-detail",
    component: Layout,
    hidden: true,
    permissions: ["purdelivery:list",'purchase/purApply:purApply:list'],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/purchase/purApply/add.vue"),
        name: "PurApplyAdd",
        meta: { title: "采购申请详情", activeMenu: "/purchase/purApply" },
      },
    ],
  },

   {
    path: "/purchase/purOrder-detail",
    component: Layout,
    hidden: true,
    permissions: ["purchase/purOrder:purOrder:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/purchase/purOrder/add.vue"),
        name: "PurOrderAdd",
        meta: { title: "采购订单详情", activeMenu: "/purchase/purOrder" },
      },
    ],
  },

  {
    path: "/purchase/orderNote-detail",
    component: Layout,
    hidden: true,
    permissions: ["purchaseDelivery:purDelivery:list"],
    children: [
      {
        path: "index/:type/:id",
        component: () => import("@/views/purchase/orderNote/detail.vue"),
        name: "OrderNoteDetail",
        meta: { title: "采购交货单详情", activeMenu: "/purchase/orderNote" },
      },
    ],
  },
];
