export default function (Vue) {
  Vue.mixin({
    data() {
      if (
        this.$options === undefined ||
        this.$options.dictsKey === undefined ||
        this.$options.dictsKey === null
      ) {
        return {};
      }
      return {}
    },
    created() {
      const dictsKey = this.$options?.dictsKey || [];
      // console.log(dictsKey,'dictsKey')
      const keys = [...new Set(dictsKey)]
    
      if (keys && keys.length) {
        keys.forEach((element) => {
          this.$store.dispatch("dictData/getFeatch", {
            dataType: [element],
            code: [element],
            params: {},
          });
        });
      }
    },
  });
}
