// 设置 rem 函数
export  function setRem() {
  // 1920 默认大小16px; 1920px = 120rem ;每个元素px基础上/16
  const screenWidth = 1920;
  let htmlWidth =
    document.documentElement.clientWidth || document.body.clientWidth;
  // 得到html的Dom元素
  const htmlDom = document.getElementsByTagName("html")[0];
  // if(htmlWidth < 1600){
  //     htmlWidth = 1600
  // }

   if (htmlWidth < 400) {
    htmlDom.style.fontSize = 10 + "px";
    return;
  }

  if (htmlWidth < 600) {
    htmlDom.style.fontSize = 12 + "px";
    return;
  }

  if (htmlWidth < 1000) {
    htmlDom.style.fontSize = 14 + "px";
    return;
  }

 
  if (htmlWidth < 1400) {
    htmlDom.style.fontSize = 16 + "px";
    return;
  }

  htmlWidth = 1920;
  const scale = screenWidth / 18;

  // 设置根元素字体大小
  // console.log(htmlWidth / scale,'htmlWidth / scale')

  htmlDom.style.fontSize = htmlWidth / scale + "px";
}
// 初始化
setRem();
// 改变窗口大小时重新设置 rem
window.onresize = function () {
  setRem();
};
