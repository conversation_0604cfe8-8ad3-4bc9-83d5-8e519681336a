// 字典类型
const options = "dictDataOptions";

// 表格头部数据
const columnsOptions = "columnsOptions";

export function getDictOptions() {
  const val = localStorage.getItem(options);
  const data = JSON.parse(val) || {};
  return data;
}

export function setDictOptions(val) {
  const data = JSON.stringify(val);
  //  localStorage.removeItem(options);
  try {
     localStorage.setItem(options, data);
  } catch (error) {
    
  }
  
}

export function removeDictOptions() {
  return localStorage.removeItem(options);
}





export function getColumnsOptions(type) {
  const val = localStorage.getItem(columnsOptions) || '';
  const data = val ? JSON.parse(val) : {};

  if (type) {
    return data[type] || undefined;
  }
  return data;
}

export function setColumnsOptions(type,val) {
  let dictData = {};
  if (type) {
     let data2 = localStorage.getItem(columnsOptions) || '';
     data2 = data2 ? JSON.parse(data2) : {};
    dictData = {
      ...data2,
      [type]: val,
    };
  } else {
    dictData = val || {};
  }
  const data = JSON.stringify(dictData);

  return localStorage.setItem(columnsOptions, data);
}

export function removeColumnsOptions(type) {
  if (type) {
    let data2 = localStorage.getItem(columnsOptions) || {};
     data2 = JSON.parse(data2) || {};
    const params = {
      ...data2,
    };
    delete params[type];

    const data = JSON.stringify(params);
    localStorage.setItem(columnsOptions, data);
    return
  }
  return localStorage.removeItem(columnsOptions);
}



