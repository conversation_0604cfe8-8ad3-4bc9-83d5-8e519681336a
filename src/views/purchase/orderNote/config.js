export const searchData = [
 {
    prop: "VBELN",
    label: "内向交货单号",
    // type:'select'
  },
   {
    prop: "LIFNR",
    label: "供应商",
    dictOptionCode: "LIFNR",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "LIFNR",
        backLable: "NAME1",
        prop: "LIFNR",
      },
    ],
    // type:'select'
  },
  {
    prop: "XBLNR",
    label: "发货单号",
    // type:'select'
  },
  {
    prop: "VGBEL",
    label: "采购订单编号",
    // type:'select'
  },
  
   {
    label: "工厂",
    type: "input",
    prop: "WERKS",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },
   {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "demand_send_status",
    otherBind :(item2) => {
      let dis = !["rkSuccess", "rkError", "S"].includes(item2.value);
      return {
        disabled: dis,
      };
    },
    attr: {
    },
  },
];

export const columns = [
  // {
  //   prop: "purchaseNo",
  //   label: "采购交货单单号",
  //   width: 100,
  // },

  
  {
    prop: "VBELN",
    label: "内向交货单号",
    // type:'select'
  },

  {
    prop: "XBLNR",
    label: "发货单号",
    // type:'select'
  },
    {
    prop: "VGBEL",
    label: "采购订单编号",
    // type:'select'
  },
     {
    prop: "LIFNR",
    label: "供应商",
  },
    {
    prop: "NAME1",
    label: "供应商名称",
    // type:'select'
  },

  // {
  //   prop: "vbelnSo",
  //   label: "销售订单号",
  // },
  // {
  //   prop: "vbelnDr",
  //   label: "销售交货单号",
  // },
  
  // {
  //   prop: "VSTEL",
  //   label: "装运点",
  //   dictOptionCode: "VKORG",
  //   // slotName: "WERKS",
  //   backProps: [
  //     {
  //       backProp: "werks",
  //       backLable: "werksName",
  //       prop: "VSTEL",
  //     },
  //   ],
  // },

  {
    prop: "LFDAT",
    label: "计划发货日期",
    type: "date",
  },





    {
    prop: "ERDAT",
    label: "创建日期",
    type: "date",
  },
  {
    prop: "AEDAT",
    label: "更改日期",
    type: "date",
  },
    {
    prop: "hMemo",
    label: "抬头备注",
    width: 140,
    searchOut:true
    // type:'select'
  },
 {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    fixed: "right",
    optionsCode: "demand_send_status",
    slotName: "docStatus",
    attr: {
      disabled: true,
    },
  },
   {
    prop: "caozuo",
    label: "操作",
    slotName:'caozuo',
    hiddenSearch:true,
    fixed:'right',
    width:200,
    // type:'select'
  },
].map((item)=>{
  return {
    ...item,
    width:item?.width || '140px'
  }
});

export const keyList = columns.filter(({ optionsCode }) => optionsCode);


export const detailSearch = columns.map((item)=>{
  if(item.prop === 'caozuo'){
    item.hiddenSearch = true
  }
  return {
    ...item,
    attr: {
      disabled: true,
    },
  }

})

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lineCloumn = [
  // {
  //   prop: "POSNR",
  //   label: "行号",
  //   width: 140,
  // },
  
   {
    prop: "VBELN",
    label: "内向交货单号",
    width: 140,
  },
  {
    prop: "VGBEL",
    label: "采购订单号",
    width: 140,
  },
  // {
  //   prop: "MATNR",
  //   label: "物料号",
  //   width: 140,
  // },
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
  },
  {
    prop: "KWMENG",
    label: "数量",
    width: 140,
  },
  {
    prop: "WERKS",
    label: "工厂",
    width: 140,
    dictOptionCode: "VKORG",
    // slotName: "WERKS",
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },
  {
    prop: "LGORT",
    label: "库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },



     {
    prop: "zzmatnrName",
    label: "物料描述",
    width: 140,
  },

  {
    prop: "iMemo",
    label: "行备注",
     width: 300,
  },
];


export const notesearchData = JSON.parse(JSON.stringify(searchData))
export const notecolumns = JSON.parse(JSON.stringify(columns))
export const notelineCloumn = JSON.parse(JSON.stringify(lineCloumn))