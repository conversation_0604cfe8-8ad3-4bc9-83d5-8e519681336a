<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="采购内向交货单单号" prop="purchaseNo">
        <el-input
          v-model="queryParams.purchaseNo"
          placeholder="请输入采购内向交货单单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售订单号" prop="vbelnSo">
        <el-input
          v-model="queryParams.vbelnSo"
          placeholder="请输入销售订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售交货单号" prop="vbelnDr">
        <el-input
          v-model="queryParams.vbelnDr"
          placeholder="请输入销售交货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="内向交货单号" prop="VBELN">
        <el-input
          v-model="queryParams.VBELN"
          placeholder="请输入内向交货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="LIFNR">
        <el-input
          v-model="queryParams.LIFNR"
          placeholder="请输入供应商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商名称" prop="NAME1">
        <el-input
          v-model="queryParams.NAME1"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['purchaseDelivery:purDelivery:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['purchaseDelivery:purDelivery:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['purchaseDelivery:purDelivery:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['purchaseDelivery:purDelivery:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purDeliveryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="purchaseHeadId" />
      <el-table-column label="采购内向交货单单号" align="center" prop="purchaseNo" />
      <el-table-column label="单据状态" align="center" prop="docStatus" />
      <el-table-column label="销售订单号" align="center" prop="vbelnSo" />
      <el-table-column label="销售交货单号" align="center" prop="vbelnDr" />
      <el-table-column label="装运点" align="center" prop="VSTEL" />
      <el-table-column label="计划发货日期" align="center" prop="LFDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.LFDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="抬头备注" align="center" prop="hMemo" />
      <el-table-column label="内向交货单号" align="center" prop="VBELN" />
      <el-table-column label="供应商" align="center" prop="LIFNR" />
      <el-table-column label="供应商名称" align="center" prop="NAME1" />
      <el-table-column label="创建日期" align="center" prop="ERDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ERDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更改日期" align="center" prop="AEDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.AEDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['purchaseDelivery:purDelivery:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['purchaseDelivery:purDelivery:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购内向交货单头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采购内向交货单单号" prop="purchaseNo">
          <el-input v-model="form.purchaseNo" placeholder="请输入采购内向交货单单号" />
        </el-form-item>
        <el-form-item label="系统编码" prop="ivSysno">
          <el-input v-model="form.ivSysno" placeholder="请输入系统编码" />
        </el-form-item>
        <el-form-item label="接口编码" prop="ivItfno">
          <el-input v-model="form.ivItfno" placeholder="请输入接口编码" />
        </el-form-item>
        <el-form-item label="销售订单号" prop="vbelnSo">
          <el-input v-model="form.vbelnSo" placeholder="请输入销售订单号" />
        </el-form-item>
        <el-form-item label="销售交货单号" prop="vbelnDr">
          <el-input v-model="form.vbelnDr" placeholder="请输入销售交货单号" />
        </el-form-item>
        <el-form-item label="装运点" prop="VSTEL">
          <el-input v-model="form.VSTEL" placeholder="请输入装运点" />
        </el-form-item>
        <el-form-item label="计划发货日期" prop="LFDAT">
          <el-date-picker clearable
            v-model="form.LFDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划发货日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="抬头备注" prop="hMemo">
          <el-input v-model="form.hMemo" placeholder="请输入抬头备注" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="${comment}" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="内向交货单号" prop="VBELN">
          <el-input v-model="form.VBELN" placeholder="请输入内向交货单号" />
        </el-form-item>
        <el-form-item label="供应商" prop="LIFNR">
          <el-input v-model="form.LIFNR" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="供应商名称" prop="NAME1">
          <el-input v-model="form.NAME1" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item label="创建日期" prop="ERDAT">
          <el-date-picker clearable
            v-model="form.ERDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更改日期" prop="AEDAT">
          <el-date-picker clearable
            v-model="form.AEDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更改日期">
          </el-date-picker>
        </el-form-item>
        <el-divider content-position="center">采购内向交货单行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddPurchaseDeliveryLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeletePurchaseDeliveryLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="purchaseDeliveryLineList" :row-class-name="rowPurchaseDeliveryLineIndex" @selection-change="handlePurchaseDeliveryLineSelectionChange" ref="purchaseDeliveryLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="SAS单号" prop="purchaseNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.purchaseNo" placeholder="请输入SAS单号" />
            </template>
          </el-table-column>
          <el-table-column label="行号" prop="POSNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.POSNR" placeholder="请输入行号" />
            </template>
          </el-table-column>
          <el-table-column label="物料号" prop="MATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MATNR" placeholder="请输入物料号" />
            </template>
          </el-table-column>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="KWMENG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KWMENG" placeholder="请输入数量" />
            </template>
          </el-table-column>
          <el-table-column label="工厂" prop="WERKS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.WERKS" placeholder="请输入工厂" />
            </template>
          </el-table-column>
          <el-table-column label="库存地点" prop="LGORT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.LGORT" placeholder="请输入库存地点" />
            </template>
          </el-table-column>
          <el-table-column label="行备注" prop="iMemo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.iMemo" placeholder="请输入行备注" />
            </template>
          </el-table-column>
          <el-table-column label="$comment" prop="userId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.userId" placeholder="请输入$comment" />
            </template>
          </el-table-column>
          <el-table-column label="$comment" prop="deptId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deptId" placeholder="请输入$comment" />
            </template>
          </el-table-column>
          <el-table-column label="内向交货单号" prop="VBELN" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.VBELN" placeholder="请输入内向交货单号" />
            </template>
          </el-table-column>
          <el-table-column label="采购订单号" prop="VGBEL" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.VGBEL" placeholder="请输入采购订单号" />
            </template>
          </el-table-column>
          <el-table-column label="采购订单行项目" prop="VGPOS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.VGPOS" placeholder="请输入采购订单行项目" />
            </template>
          </el-table-column>
          <el-table-column label="物料描述" prop="ARKTX" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ARKTX" placeholder="请输入物料描述" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurDelivery, getPurDelivery, delPurDelivery, addPurDelivery, updatePurDelivery } from "@/api/purchase/purchaseDelivery/purDelivery";

export default {
  name: "PurDelivery",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPurchaseDeliveryLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购内向交货单头信息表格数据
      purDeliveryList: [],
      // 采购内向交货单行信息表格数据
      purchaseDeliveryLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseNo: null,
        docStatus: null,
        vbelnSo: null,
        vbelnDr: null,
        VBELN: null,
        LIFNR: null,
        NAME1: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购内向交货单头信息列表 */
    getList() {
      this.loading = true;
      listPurDelivery(this.queryParams).then(response => {
        this.purDeliveryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purchaseHeadId: null,
        purchaseNo: null,
        docStatus: null,
        ivSysno: null,
        ivItfno: null,
        TYPE: null,
        vbelnSo: null,
        vbelnDr: null,
        VSTEL: null,
        LFDAT: null,
        hMemo: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        VBELN: null,
        LIFNR: null,
        NAME1: null,
        ERDAT: null,
        AEDAT: null
      };
      this.purchaseDeliveryLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.purchaseHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购内向交货单头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const purchaseHeadId = row.purchaseHeadId || this.ids
      getPurDelivery(purchaseHeadId).then(response => {
        this.form = response.data;
        this.purchaseDeliveryLineList = response.data.purchaseDeliveryLineList;
        this.open = true;
        this.title = "修改采购内向交货单头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.purchaseDeliveryLineList = this.purchaseDeliveryLineList;
          if (this.form.purchaseHeadId != null) {
            updatePurDelivery(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurDelivery(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const purchaseHeadIds = row.purchaseHeadId || this.ids;
      this.$modal.confirm('是否确认删除采购内向交货单头信息编号为"' + purchaseHeadIds + '"的数据项？').then(function() {
        return delPurDelivery(purchaseHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 采购内向交货单行信息序号 */
    rowPurchaseDeliveryLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 采购内向交货单行信息添加按钮操作 */
    handleAddPurchaseDeliveryLine() {
      let obj = {};
      obj.purchaseNo = "";
      obj.POSNR = "";
      obj.MATNR = "";
      obj.ZZMATNR = "";
      obj.KWMENG = "";
      obj.WERKS = "";
      obj.LGORT = "";
      obj.iMemo = "";
      obj.userId = "";
      obj.deptId = "";
      obj.VBELN = "";
      obj.VGBEL = "";
      obj.VGPOS = "";
      obj.ARKTX = "";
      this.purchaseDeliveryLineList.push(obj);
    },
    /** 采购内向交货单行信息删除按钮操作 */
    handleDeletePurchaseDeliveryLine() {
      if (this.checkedPurchaseDeliveryLine.length == 0) {
        this.$modal.msgError("请先选择要删除的采购内向交货单行信息数据");
      } else {
        const purchaseDeliveryLineList = this.purchaseDeliveryLineList;
        const checkedPurchaseDeliveryLine = this.checkedPurchaseDeliveryLine;
        this.purchaseDeliveryLineList = purchaseDeliveryLineList.filter(function(item) {
          return checkedPurchaseDeliveryLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handlePurchaseDeliveryLineSelectionChange(selection) {
      this.checkedPurchaseDeliveryLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('purchaseDelivery/purDelivery/export', {
        ...this.queryParams
      }, `purDelivery_.xlsx`)
    }
  }
};
</script>
