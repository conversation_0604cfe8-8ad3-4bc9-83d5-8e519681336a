<template>
  <div class="add" v-loading="loading">
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="16"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      @submitEmits="submitEmits"
    />
    <ComTable :tableData="linelist" :column="lineCloumn" />
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import ComTable from "@/components/ComTable/index.vue";
import { getPurDelivery, } from "@/api/purchase/purchaseDelivery/purDelivery";
import { setTableDictValue } from "@/utils/index.js";
import { detailSearch, lineCloumn } from "./config";


export default {
  name: "OrderNoteDetail",
  components: {
    ComSearch,
    ComTable,
  },
  dictsKey:['LGORT','VKORG'],
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      searchInfo: {},
      rules: {},
      searchData: detailSearch,
      pageType,
      lineCloumn,
      detail:{},
      linelist:[]
    };
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  created() {
    this.getDetail();
  },
  watch: {},
  methods: {
    getDetail() {
      const id= this.$route.params.id
      if (!id || id === 'undefined' || this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getPurDelivery(this.$route.params.id)
        .then((res) => {
          // console.log(res, "res");
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            this.searchInfo = this.detail;
             const _rows2 = setTableDictValue(
              this.lineCloumn,
              [...res.data?.purchaseDeliveryLineList],
              this.dictDataOptions
            );
            this.linelist = _rows2

          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
    submitEmits() {},
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}
.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
