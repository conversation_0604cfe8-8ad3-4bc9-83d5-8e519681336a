<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
    >
      <!-- <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd"
          >导出</el-button
        >
      </template> -->
       <template v-for="(item, index) in keyList" #[item.slotName]="{ row }">
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
        />
      </template>
      <template #caozuo="{ row }">
        <el-button 
        size="mini" 
        type="primary"
         @click="gotoAdd('look', row)"
         v-hasPermi="['purchaseDelivery:purDelivery:detail']"
          >详情</el-button
        >
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";

import { searchData, columns ,keyList} from "./config";
// import {
//   listPurDelivery,
//   getPurDelivery,
//   delPurDelivery,
//   addPurDelivery,
//   updatePurDelivery,
// } from "@/api/sales/purDelivery/purDelivery";
import { listPurDelivery  } from "@/api/purchase/purchaseDelivery/purDelivery";

export default {
  name: "OrderNote",
  components: {
    ComTableList,
  },
  dicts: ["demand_send_status", "doct_status",'demand_send_status'],
  dictsKey:['LGORT','VKORG'],
  data() {
    return {
      keyList,
      activeName: "all",
      columns,
      searchData: searchData,
      innerVisible: false,
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return listPurDelivery(params);
    },

    gotoAdd(type, data) {
      this.$store
        .dispatch("tagsView/delPage", { name: "OrderNoteDetail" })
        .then(() => {
          this.$router.push(
            `/purchase/orderNote-detail/index/${type}/${data.purchaseHeadId || undefined}`
          );
        });
    },
  },
};
</script>
