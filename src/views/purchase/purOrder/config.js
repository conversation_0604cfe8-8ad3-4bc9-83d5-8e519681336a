export const cloumns = [

  {
    label: "SAS采购订单",
    type: "input",
    prop: "purOrderNo",
    attr: {
      disabled: true,
    },
  },

  {
    label: "SAP采购订单",
    type: "input",
    prop: "EBELN",
    attr: {
      disabled: true,
    },
  },
  {
    label: "SAS采购申请",
    type: "input",
    prop: "purApplyNo",
    attr: {
      disabled: true,
    },
  },

  {
    label: "SAS销售订单号",
    type: "input",
    prop: "salesNo",
    attr: {
      disabled: true,
    },
  },
  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    fixed: "right",
    optionsCode: "demand_send_status",
    slotName: "docStatus",
    otherBind: (item2) => {
      let dis = !["commit", "draft", "rkError", "ckSuccess", "S"].includes(
        item2.value
      );
      return {
        disabled: dis,
      };
    },
    attr: {
      disabled: true,
    },
  },

  {
    label: "公司",
    type: "input",
    prop: "BUKRS",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "BUKRS",
      },
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
       {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },

  {
    label: "采购组织",
    type: "input",
    prop: "EKORG",
    dictOptionCode: "EKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "BUKRS",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },

  {
    prop: "WERKS",
    label: "工厂",
    width: 140,
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "BUKRS",
      },
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
    ],
  },

  {
    label: "采购组",
    type: "select",
    prop: "EKGRP",
    optionsCode: "ekgrp",
    slotName: "EKGRP",
  },
  {
    label: "供应商",
    type: "input",
    prop: "LIFNR",
    dictOptionCode: "LIFNR",
    append: true,
    attr: {
      readonly: true,
    },
    backProps: [
      {
        backProp: "LIFNR",
        backLable: "NAME1",
        prop: "LIFNR",
      },
    ],
  },

  {
    label: "凭证类型",
    type: "input",
    prop: "BSART",
    optionsCode: "basrt",
    type: "select",
    slotName: "basrt",
  },
  {
    prop: "LGORT",
    label: "子库库存地点",
    width: 240,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },
  {
    label: "备注",
    prop: "remark",
    searchOut: true,
    // hiddenSearch: true,
    attr: {},
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item)=>{
  item.width = item.width || 140
  return item
});


// export const cloumns2 = [
//     {
//     label: "PO单号",
//     type: "input",
//     prop: "purOrderNo",
//     attr: {
//       disabled: true,
//     },
//   },

//   {
//     label: "验收入库单号",
//     type: "input",
//     prop: "EBELN",
//     attr: {
//       disabled: true,
//     },
//   },
//   {
//     label: "收货单位",
//     type: "input",
//     prop: "purApplyNo",
//     attr: {
//       disabled: true,
//     },
//   },

//   {
//     label: "收货地址",
//     type: "input",
//     prop: "salesNo",
//     attr: {
//       disabled: true,
//     },
//   },
//   {
//     label: "供应商名称",
//     type: "input",
//     prop: "salesNo",
//     attr: {
//       disabled: true,
//     },
//   },

  
//   {
//     label: "单据状态",
//     type: "select",
//     prop: "docStatus",
//     fixed: "right",
//     optionsCode: "demand_send_status",
//     slotName: "docStatus",
//     otherBind: (item2) => {
//       let dis = !["commit", "draft", "rkError", "ckSuccess", "S"].includes(
//         item2.value
//       );
//       return {
//         disabled: dis,
//       };
//     },
//     attr: {
//       disabled: true,
//     },
//   },

// ]


// 映射code
export const optionsCodeList = cloumns.filter(({ optionsCode }) => optionsCode);

export const rulesData = {};
cloumns.forEach((item) => {
  if (
    ["BSART", "BUKRS", "EKORG", "EKGRP", "LIFNR", "WERKS"].includes(item.prop)
  ) {
    let text = item?.type === "select" ? "请选择" : "请输入";
    let trigger = "blur";
    let arr = [
      { required: true, message: `${text}${item.label}`, trigger: trigger },
    ];
    rulesData[item.prop] = arr;
  }
});

export const rules = rulesData;

export const searchData = JSON.parse(JSON.stringify(cloumns)).map((item) => {
  item.attr = {};

  if (item.prop === "docStatus") {
    item.otherBind = (item) => {
      let dis = !["rkSuccess", "rkError", "S"].includes(item.value);
      let dis2 = !["commit", "draft", "rkError", "rkSuccess", "S"].includes(
        item.value
      );

      return {
        disabled:
          location.href.indexOf("purOrderStore") > -1 ? dis : dis2 || false,
      };
    };
  }
  return item;
});

export const getsearchData = (docStatus, ruku, WERKS) => {
  let data = JSON.parse(JSON.stringify(cloumns)).map((item) => {
    if (item.prop === "docStatus") {
      item.otherBind = (item) => {
        let dis = !["rkSuccess", "rkError", "S"].includes(item.value);
        return {
          disabled: location.href.indexOf("purOrderStore") > -1 ? dis : false,
        };
      };
    } else if (["LGORT", "remark"].includes(item.prop)) {
      if (item.prop === "remark" && ruku) {
        item.label = "出库/入备注";
      }


      // if()
      if ((!["E", "draft"].includes(docStatus) && !ruku) || ['rkSuccess'].includes(docStatus)) {
        item.attr = {
          disabled: true,
        };
      } else {
        item.hiddenSearch = false;
        item.attr = {
          disabled: false,
        };
      }

      if (WERKS === "6001" && ruku) {
        item.attr = {
          disabled: true,
        };
      }
    } else {
      item.attr = {
        disabled: true,
      };
    }

    return item;
  });

  return data;
};

export const dicts = JSON.parse(JSON.stringify(searchData))
  .filter(({ optionsCode }) => optionsCode)
  .map(({ optionsCode }) => optionsCode);

export const dictsKey = JSON.parse(JSON.stringify(searchData))
  .filter(({ dictOptionCode }) => dictOptionCode)
  .map(({ dictOptionCode }) => dictOptionCode);

export const lineCloumn = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
    append: true,
    attr: {},
  },

  {
    prop: "KWMENG",
    label: "数量",
    width: 120,
  },

  {
    prop: "LGORT",
    label: "库存地点",
    width: 240,
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  {
    prop: "iMemo",
    label: "行备注",
    // width:140
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
];

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["purchase/purOrder:purOrder:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["purchase/purOrder:purOrder:remove"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["purchase/purOrder:purOrder:remove"],
  },
  uploadExcel: {
    isShow: true,
    hasPermi: ["purchase/purOrder:purOrder:remove"],
  },
};

export const getLineCloums = (docStatus, isRuku) => {
  const columns2 = JSON.parse(JSON.stringify(lineCloumn)).map((item) => {
    if (isRuku) {
      // 成功、入库失败状态允许修改行数量、库存地点和备注信息，但不允许新增和删除，物料号不允许修改；
      if (
        ["S", "rkError"].includes(docStatus) &&
        ["iMemo", "LGORT", "KWMENG"].includes(item.prop)
      ) {
        item.attr = {
          disabled: false,
        };
      } else {
        item.attr = {
          disabled: true,
        };
      }
    }
    return item;
  });

  return columns2;
};
export const columns2 = JSON.parse(JSON.stringify(cloumns)).map((item) => {
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

export const lineCloumn2 = JSON.parse(JSON.stringify(lineCloumn))
  .filter(({ prop }) => prop !== "caozuo")
  .map((item) => {
    return {
      ...item,
      attr: {
        disabled: true,
      },
    };
  });

export const btnProps2 = {
  add: {
    isShow: false,
    hasPermi: ["purchase/purOrder:purOrder:add"],
  },
  batchDelete: {
    isShow: false,
    hasPermi: ["purchase/purOrder:purOrder:remove"],
  },
  deleteLine: {
    isShow: false,
    hasPermi: ["purchase/purOrder:purOrder:remove"],
  },
  uploadExcel: {
    isShow: false,
    hasPermi: ["purchase/purOrder:purOrder:remove"],
  },
};

export const changeResLine = (list, docStatus) => {
  if (Array.isArray(list)) {
    const data = [...list].map((item) => {
      return {
        ...item,
        id: ["draft", "E"].includes(docStatus)
          ? `${item.purOrderLineId}`
          : undefined,
      };
    });

    return data;
  }

  return list;
};

export const changeAddLine = (list, detail) => {
  if (Array.isArray(list)) {
    let posnr = 0;
    const data = [...list].map((item) => {
      posnr = item.posnr * 1 || (posnr + 10) * 1;

      return {
        ...item,
        EBELP: posnr,
        WERKS: detail.WERKS,
      };
    });

    return data;
  }

  return list;
};
