<template>
  <div class="add" v-loading="loading">
    <div @click="closeEdit">
      <ComSearch
        :searchData="cloumns"
        :searchInfo="searchInfo"
        :showNum="16"
        labelWidth="140px"
        :showBtn="false"
        :rules="rules"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        :cancelShow="['draft', 'E', 'rkError', 'S'].includes(detail.docStatus)"
        :handFuntion="handFuntion"
        :cancelLoading="loading"
        :getSearchcallBack="getSearchcallBack"
        :closesearch="closesearch"
        @submitEmits="submitEmits"
      >
        <template #searchButton v-if="pageType !== 'look'">
          <el-button
            v-if="[''].includes(detail.docStatus) && !ruku"
            type="primary"
            size="mini"
            @click="handeFuntion('save')"
            v-hasPermi="['purchase/purOrder:purOrder:add']"
            >保存</el-button
          >
          <el-button
            type="primary"
            size="mini"
            v-if="['draft'].includes(detail.docStatus)"
            @click="handeFuntion('upd')"
            v-hasPermi="['purchase/purOrder:purOrder:edit']"
            >修改</el-button
          >

          <el-button
            v-if="['draft', 'E'].includes(detail.docStatus) && !ruku"
            type="primary"
            size="mini"
            @click="handeFuntion('submit')"
            v-hasPermi="['purchase/purOrder:purOrder:submit']"
            >提交</el-button
          >
          <el-button
          type="primary"
          size="mini"
          @click="getPrint"
          v-hasPermi="['Inventory:inventoryManage:rkApproved']"
          v-if="
            ['S', 'rkError', 'rkSuccess'].includes(detail.docStatus) &&
            detail.WERKS !== '6001' && ruku
          "
          >打印</el-button
        >

          <el-button
            v-if="
              ruku &&
              ['rkError', 'S'].includes(detail.docStatus) &&
              detail.WERKS !== '6001'
            "
            type="primary"
            size="mini"
            @click="handeFuntion('ruku')"
            v-hasPermi="['PurOrderStore:in']"
            >入库</el-button
          >
        </template>
      </ComSearch>
    </div>
    <LineTable
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      ref="lineTable"
      keyId="purOrderLineId"
      POSNRKey="EBELP"
      :requiredKeys="['ZZMATNR', 'KWMENG', 'LGORT']"
      :dataProps="dataProps"
      :isShowCancel="['draft', 'E', 'rkError', 'S'].includes(detail.docStatus)"
      :cancelFuntion="handFuntion"
    >
      <!-- <template #lineTableHeader>
        <el-button type="primary" size="mini">创建采购订单退货</el-button>
      </template> -->
    </LineTable>
     <Inventoryprint ref="Inventoryprint" />
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import {
  inventoryPurOrderEntry,
  canceHead,
  canceLine,
} from "@/api/purchase/purOrder/purOrder";
import {
  rules,
  searchData,
  cloumns,
  lineCloumn,
  dictsKey,
  dicts,
  btnProps,
  changeResLine,
  changeAddLine,
  columns2,
  lineCloumn2,
  btnProps2,
  getLineCloums,
  getsearchData,
} from "./config";
import {
  getPurOrder,
  addPurOrder,
  updatePurOrder,
} from "@/api/purchase/purOrder/purOrder";
import { setTableDictValue } from "@/utils/index.js";
import Inventoryprint from '../../Inventory/components/Inventoryprint.vue'

export default {
  name: "PurOrderAdd",
  components: {
    ComSearch,
    LineTable,
    Inventoryprint
  },
  dictsKey,
  dicts,
  props: {},
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
    lineCloumn() {
      let isruku = location.href.indexOf("purOrderStore-detail") > -1;
      if (isruku) {
        let data = getLineCloums(this.detail.docStatus, isruku);
        return data;
      }

      if (
        ["S", "commit", "rkSuccess", "rkError"].includes(
          this.detail.docStatus
        ) ||
        location.href.indexOf("purOrderStore-detail") > -1
      ) {
        return lineCloumn2;
      } else {
        return lineCloumn;
      }
    },
    isRuku() {
      return location.href.indexOf("purOrderStore-detail") > -1;
    },
  },
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      btnProps,
      cloumns,
      loading: false,
      searchInfo: {},
      searchData: searchData,
      pageType,
      // lineCloumn,
      lineList: [],
      rules,
      detail: {
        docStatus: "",
      },
      dataProps: {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
        },
      },
    };
  },

  created() {
    this.ruku = location.href.indexOf("purOrderStore-detail") > -1;
    this.getPurOrder();
  },
  watch: {},
  methods: {
      getPrint() {
      this.$refs.Inventoryprint.printInfo({
        ids: "278",
        type: "purOrder",
        title: "采购订单入库单",
      });
    },
    closeEdit() {
      // console.log(this.$refs.lineTable,'11111')
      this.$refs.lineTable.closeEdit();
    },
    getPurOrder() {
      if (this.$route.params.type === "add") return;
      if (!this.$route.params.id) return;
      if (this.loading) return;
      this.loading = true;
      getPurOrder(this.$route.params.id)
        .then((res) => {
          if (res.code === 200) {
            let WERKS = res?.data?.purchaseOrderLineList[0]?.WERKS || "";
            const resData = {
              ...res.data,
              LGORT: res?.data?.lgort,
              WERKS,
            };
            this.detail = JSON.parse(JSON.stringify(resData));
            this.searchInfo = resData;

            if (
              ["S", "commit", "rkSuccess",, "rkError"].includes(
                resData.docStatus
              ) ||
              this.isRuku
            ) {
              this.btnProps = btnProps2;
              this.cloumns = getsearchData(
                this.detail.docStatus,
                this.ruku,
                WERKS
              );
            }

            let rows2 = setTableDictValue(
              this.lineCloumn,
              changeResLine(res.data.purchaseOrderLineList, resData.docStatus),
              this.dictDataOptions
            );
            this.lineList = rows2;
          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error, "rrrrr");
          this.loading = false;
        });
    },

    handeFuntion(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },

    submitEmits(searchInfoData, type) {
      let list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }

      let action = "add";
      const fun = addPurOrder;
      if (this.detail?.purOrderHeadId) {
        action = "upd";
        // fun = updatePurOrder
      }

      if (type === "submit") {
        action = "submit";
      }
      if (this.loading) return;
      this.loading = true;

      if (this.$refs.lineTable.checkList()) return;

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];

      const newList = changeAddLine(list, searchInfoData);

      const addList = newList
        .filter(({ purOrderHeadId }) => !purOrderHeadId)
        .map((item) => {
          item.purOrderLineId = undefined;
          item.purOrderHeadId = undefined;
          return item;
        });
      const _list = newList.filter(({ purOrderHeadId }) => purOrderHeadId);
      const list2 = [..._list, ...delLineIdList];

      fun({
        action,
        purchaseOrderHead: {
          ...this.detail,
          ...searchInfoData,
          lgort: searchInfoData?.LGORT,
          delFlag: "0",
          purchaseOrderLineList: list2,
          addPurchaseOrderLineList: addList,
        },
      })
        .then((res) => {
          this.loading = false;
          if (type === "ruku") {
            this.setRuku();
            return;
          }
          this.$modal.msgSuccess(res.data.msg || "修改成功");
          if (res?.data && !this?.detail?.purOrderHeadId) {
            this.$store
              .dispatch("tagsView/delPage", { name: "PurOrderAdd" })
              .then(() => {
                this.$router.replace(
                  `/purchase/purOrder-detail/index/edit/${res?.data?.purOrderHeadId}`
                );
              });
          } else if (res.code === 200) {
            this.getPurOrder();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    closesearch(backProps, prop) {
      if (prop === "LGORT") {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            LGORT: "",
            "LGORT,lgortName": [],
          },
        };
      }
    },
    getSearchcallBack(params, activeProps) {
      if (this.$route.query.ruku) {
        if (activeProps.prop === "LGORT" && params[activeProps.prop]) {
          const lineList = [...this.$refs.lineTable.salesOrderLineList];

          const list = lineList.map((item) => {
            return {
              ...item,
              LGORT: params.LGORT,
              "LGORT,lgortName": params["LGORT,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
        return;
      }
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "LGORT" && params[activeProps.prop]) {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            LGORT: params.LGORT,
            "LGORT,lgortName": params["LGORT,lgortName"],
          },
        };
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const lineList2 = [...this.$refs.lineTable.salesOrderLineList].filter(
          ({ LGORT }) => !LGORT
        );

        if (lineList && lineList.length && lineList2.length) {
          const list = lineList.map((item) => {
            return {
              ...item,
              LGORT: item.LGORT ? item.LGORT : params.LGORT,
              "LGORT,lgortName": item.LGORT
                ? item["LGORT,lgortName"]
                : params["LGORT,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
    async setRuku() {
      try {
        if (!this.$route.params.id || this.loading) return;
        this.loading = true;
        const res = await inventoryPurOrderEntry(this.detail.EBELN);
        this.$modal.msgSuccess(res?.msg || res?.data?.msg || "操作成功");
        this.loading = false;
        this.getPurOrder();
      } catch (error) {
        this.loading = false;
        this.getPurOrder();
        console.log(error);
      }
    },
    async handFuntion(type, rows) {
      try {
        if (type && rows) {
          const data = rows.map((item) => {
            return {
              purOrderHeadId: item.purOrderHeadId,
              purOrderLineId: item.purOrderLineId,
            };
          });

          const res = await canceLine(data);

          if (res.code === 200) {
            this.$modal.msgSuccess(res.data.msg || "修改成功");
          }
          this.getPurOrder();
        } else {

          const res = await canceHead([{
            purOrderHeadId:this.detail?.purOrderHeadId
          }])
          if(res.code === 200){
              this.$modal.msgSuccess(res.data.msg || "修改成功");
          }
            this.getPurOrder();
        }
      } catch (error) {

        console.log(error,'rrrr')

          this.getPurOrder();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}
.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
