<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="cloumns"
      :getTableList="getList"
      :isSelection="pageRuku ? true : false"
      :tableProps="tableProps"
      :selectable="selectable"
      :searchInfo="searchInfo"
      ref="ComTableList"
      @selection-change="handleSelectionChange"
    >
      <template #searchButton>
        <el-button
          v-if="!pageRuku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          v-hasPermi="['purchase/purOrder:purOrder:add']"
          @click="gotoAdd('add', {})"
          >新增</el-button
        >

        <el-button
          v-if="pageRuku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          v-hasPermi="['PurOrderStore:in']"
          :disabled="selectList.length === 0"
          @click="inventoryPurOrderEntry"
          >批量入库</el-button
        >
         <el-button
         v-if="pageRuku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="getPrint" 
          :disabled="selectList.length === 0"
          >批量打印</el-button
        >
      </template>

      <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>

      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
            v-hasPermi="[
              'purchase/purOrder:purOrder:edit',
              'PurOrderStore:edit',
            ]"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
            v-hasPermi="[
              'purchase/purOrder:purOrder:detail',
              'PurOrderStore:detail',
            ]"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
     <Inventoryprint ref="Inventoryprint"/>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import {
  listPurOrder,
  inventoryPurOrderEntry,
} from "@/api/purchase/purOrder/purOrder";
import Inventoryprint from '../../Inventory/components/Inventoryprint.vue'
import {
  searchData,
  dictsKey,
  dicts,
  cloumns,
  optionsCodeList,
} from "./config";
import { changeInfoData } from "@/components/LineTable/units";
export default {
  name: "PurOrderList",
  components: {
    ComTableList,
    Inventoryprint
  },
  dictsKey,
  dicts,
  data() {
    const ispurOrderStore = location.href.indexOf("purOrderStore") > -1;
    return {
      searchData: searchData.filter(({ prop }) =>(!['LGORT','remark'].includes(prop))),
      cloumns: cloumns.filter(({ prop }) => !["LGORT", "WERKS"].includes(prop)),
      optionsCodeList,
      tableProps: {
        rowKey: "purOrderHeadId",
      },
      selectList: [],
      searchInfo: {
        // docStatus: ispurOrderStore ? "S" : "",
      },
      pageRuku:false
    };
  },
  created() {
     const ispurOrderStore = location.href.indexOf("purOrderStore") > -1;
     this.pageRuku = ispurOrderStore
  },
  computed: {
    
  },
  methods: {
    getPrint(){
      this.$refs.Inventoryprint.printInfo({
        ids:'278',
        type:'purOrder',
        title:'采购订单入库单'
      })
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {

      if (!params?.docStatusList || params.docStatusList.length === 0) {
        if (location.href.indexOf("purOrderStore") > -1) {
          params.docStatusList = ["rkSuccess", "rkError", "S"];
        }else{
          // params.docStatusList = ["commit", "draft", "rkError", "rkSuccess", "S"]
        }
      }

      return listPurOrder(params);
    },
    gotoAdd(type, data) {
      if (this.pageRuku) {
        this.$store
          .dispatch("tagsView/delPage", { name: "PurOrderStoreDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/store/purOrderStore-detail/index/${type}/${
                data.purOrderHeadId
              }?ruku=${true}`
            );
          });
      } else {
        this.$store
          .dispatch("tagsView/delPage", { name: "PurOrderAdd" })
          .then(() => {
            this.$router.push(
              `/purchase/purOrder-detail/index/${type}/${data.purOrderHeadId}`
            );
          });
      }
    },

    handleSelectionChange(val) {
      this.selectList = val;
    },
    selectable(row) {
      const params = changeInfoData(row)
      return this.pageRuku && (params.BUKRS !== '6000');
    },
    inventoryPurOrderEntry() {
      
      const list = this.selectList.filter((item)=>{
       const params =  changeInfoData(item)
        return ['rkError','S'].includes(item.docStatus) &&  (params.WERKS !== '6001');
      }) 
      if (!list || list.length === 0) {
        this.$message({
          type: "error",
          message: `暂无入库的数据`,
        });
        return;
      }
      const getFun = list.map(({ EBELN }) => {
        return inventoryPurOrderEntry(EBELN);
      });
      Promise.all(getFun)
        .then((res) => {
          // console.log(res);
          this.$modal.msgSuccess("入库成功");
          let data = this.$refs.ComTableList.$refs.searchComTableInfo.dataInfo;
          this.$refs.ComTableList.submitEmits(data);
        })
        .catch((error) => {
          console.log(error);
          let data = this.$refs.ComTableList.$refs.searchComTableInfo.dataInfo;
          this.$refs.ComTableList.submitEmits(data);
        });
    },
  },
};
</script>
