import { getaddSearch } from "@/components/ComSearch/utils";
export const searchData = [
  {
    label: "SAS采购申请",
    type: "input",
    prop: "purApplyNo",
  },
  {
    label: "供方SAS需求单号",
    type: "input",
    prop: "demandNo",
  },
  {
    label: "SAP采购申请",
    type: "input",
    prop: "BANFN",
  },

  {
    label: "来源单号",
    type: "input",
    prop: "salesNo",
  },

  {
    label: "采购组织",
    type: "input",
    prop: "EKORG",
    dictOptionCode: "EKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },

  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
    dictOptionCode: "EKORG",
    append: true,
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
    ],
    attr: {
      // disabled: true,
    },
  },
  {
    label: "采购组",
    type: "select",
    prop: "EKGRP",
    optionsCode: "ekgrp",
  },
  {
    label: "单据状态",
    type: "input",
    prop: "docStatus",
    optionsCode: "purappay_docstatus",
    slotName: "docStatus",
    fixed: "right",
    attr: {
      // disabled: true,
    },
    type: "select",
  },
  // {
  //   label: "创建日期",
  //   type: "daterange",
  //   prop: "createTimeS,createTimeE",
  //   hiddenTable: true,
  // },
];

const aProps = [
      {
        prop: "KUNNR",
        backProp: "company",
        backLable: "companyName",
      },

      {
        prop: "IHREZ",
        propLabels: ["IHREZ", "IHREZ"],
      },

      {
        prop: "TELF1",
        propLabels: ["TELF1", "TELF1"],
      },
      {
        prop: "harvestAddress",
        propLabels: ["harvestAddress", "sasAddress"],
      },
      {
        prop: "shipMethod",
        propLabels: ["shipMethod", "shipMethod"],
      },
    ]

export const columns = [
  {
    label: "SAS采购申请",
    type: "input",
    prop: "purApplyNo",
    attr: {
      disabled: true,
    },
  },
  {
    label: "SAP采购申请",
    type: "input",
    prop: "BANFN",
    attr: {
      disabled: true,
    },
  },
  {
    label: "供方SAS需求单号",
    type: "input",
    prop: "demandNo",
    attr: {
      disabled: true,
    },
  },

  {
    label: "来源单号",
    type: "input",
    prop: "salesNo",
    attr: {
      disabled: true,
    },
  },

  {
    label: "采购申请凭证类型",
    type: "input",
    prop: "BSART",
    optionsCode: "bstyp",
    type: "select",
    slotName: "BSART",
    attr: {
      disabled: true,
    },
  },

  {
    label: "来源类型",
    type: "input",
    prop: "sourceType",
    attr: {
      disabled: true,
    },
  },

  {
    label: "采购组织",
    type: "input",
    prop: "EKORG",
    dictOptionCode: "EKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
     ...aProps
    ],
    setBackParams: (prop, data, self) => {
      // let datainfo = self.dataInfo;
      // let type = self.$route?.params?.type;
      let params = getaddSearch(prop, data, self.activeProps.backProps);
      params["KUNNR,NAME1"] = params.KUNNR ? params["KUNNR,companyName"] : [];

      delete params["KUNNR,companyName"];
      return params;
    },
  },

  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
    dictOptionCode: "EKORG",
    append: true,
    backProps: [
       {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
       {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "EKORG",
      },
     
      ...aProps
    ],
    setBackParams: (prop, data, self) => {
      // let datainfo = self.dataInfo;
      // let type = self.$route?.params?.type;
      let params = getaddSearch(prop, data, self.activeProps.backProps);
      params["KUNNR,NAME1"] = params.KUNNR ? params["KUNNR,companyName"] : [];

      delete params["KUNNR,companyName"];
      return params;
    },

    attr: {
      // disabled: true,
    },
  },

  {
    label: "采购方销售组织",
    type: "input",
    prop: "VKORG",
    dictOptionCode: "VKORG",
    hiddenSearch: true,
    append: true,
    attr: {
      readonly: true,
    },
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
        name: "purOrgName",
      },
    ],
  },

  {
    label: "需求者",
    type: "input",
    prop: "AFNAM",
    attr: {
      disabled: true,
    },
  },
  {
    prop: "LGORT",
    label: "收货地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    hiddenTable: true,
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },
  {
    label: "采购组",
    type: "select",
    prop: "EKGRP",
    hiddenTable: true,
    optionsCode: "ekgrp",
    // hiddenSearch: true,
  },

  {
    prop: "KUNNR",
    label: "收货单位",
    width: 200,
    dictOptionCode: "KUNNR",
    append: true,
    attr: {},
    otherBind: (item, row, self) => {
      if (!row.EKORG) {
        return {
          disabled: true,
        };
      } else {
        return {
          disabled: false,
        };
      }
    },
    setPropParams: (activeProps, dataInfo, params) => {
      // console.log(params,'params')
      return {
        ...params,
        VKORG: dataInfo["EKORG,salesOrgName"][0],
      };
    },
    backProps: [
      {
        prop: "KUNNR",
        backProp: "KUNNR",
        backLable: "NAME1",
        name: "NAME1",
      },
      {
        prop: "IHREZ",
        propLabels: ["IHREZ", "NAME1"],
      },

      {
        prop: "TELF1",
        propLabels: ["TELF1", "telNumber"],
      },
      {
        prop: "harvestAddress",
        propLabels: ["harvestAddress", "STREET"],
      },
      {
        prop: "shipMethod",
        propLabels: ["shipMethod", "KVGR2"],
      },
    ],
  },
  {
    label: "收货地址",
    prop: "harvestAddress",
    searchOut: true,
  },
  {
    label: "联系人",
    prop: "IHREZ",
  },
  {
    label: "电话",
    prop: "TELF1",
  },
  {
    label: "发运方式",
    type: "select",
    prop: "shipMethod",
    slotName:'shipMethod',
    optionsCode: "ship_method",
  },

  // {
  //   label: "创建日期",
  //   type: "date",
  //   prop: "createTime",
  //   hiddenSearch: true,
  // },

  {
    label: "备注",
    searchOut: true,
    prop: "remark",
    attr: {
      disabled: false,
    },
  },
  {
    label: "单据状态",
    type: "input",
    prop: "docStatus",
    optionsCode: "purappay_docstatus",
    slotName: "docStatus",
    fixed: "right",
    attr: {
      disabled: true,
    },
    type: "select",
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.width = item.width || "140px";

  return item;
});

export const getColumns = (docStatus) => {
  return JSON.parse(JSON.stringify(columns)).map((item) => {
    if (["WERKS", "EKORG", "remark", "LGORT", "EKGRP",'harvestAddress','KUNNR','IHREZ','TELF1','shipMethod'].includes(item.prop)) {
      item.attr = {
        disabled: !["E", "", "draft"].includes(docStatus) ? true : false,
      };
    }

    if (["WERKS", "EKORG"].includes(item.prop)) {
      item.setBackParams = (prop, data, self) => {
        // let datainfo = self.dataInfo;
        // let type = self.$route?.params?.type;
        let params = getaddSearch(prop, data, self.activeProps.backProps);
        params["KUNNR,NAME1"] = params.KUNNR ? params["KUNNR,companyName"] : [];

        delete params["KUNNR,companyName"];
        return params;
      };
    }

    if (["KUNNR"].includes(item.prop)) {
      item.setPropParams = (activeProps, dataInfo, params) => {
        return {
          ...params,
          VKORG: dataInfo["EKORG,salesOrgName"][0],
        };
      };
    }

    return item;
  });
};

export const columns2 = columns.slice(0, -1);

export const lineColumns = [
  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
    width: 300,
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "ZZMATNR",
        backLable: "ZZMATNR",
        prop: "ZZMATNR",
      },
      {
        backProp: "MEINS",
        backLable: "MSEHT",
        prop: "MEINS",
      },
    ],
  },

  {
    label: "数量",
    type: "number",
    prop: "KWMENG",
    width: 100,
  },
  {
    prop: "LGORT",
    label: "收货地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },
  {
    prop: "MEINS",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
    optionsCode: "meins",
    // width:140
  },

  {
    label: "交货日期",
    type: "input",
    prop: "LPEIN",
    type: "date",
  },
  {
    label: "采购组",
    type: "select",
    prop: "EKGRP",
    optionsCode: "ekgrp",
    width: 200,
  },

  {
    label: "备注",
    type: "input",
    prop: "iMemo",
  },
].map((item) => {
  item.width = item.width || 200;
  if (item.prop === "iMemo") {
    item.width = undefined;
  }
  return item;
});

export const getLineColumns = (docStatus) => {
  return JSON.parse(JSON.stringify(lineColumns)).map((item) => {
    if (!item?.attr?.disabled) {
      item.attr = {
        disabled: !["E", "", "draft"].includes(docStatus) ? true : false,
      };
    }

    return item;
  });
};

export const dicts = JSON.parse(JSON.stringify(columns))
  .filter(({ optionsCode }) => optionsCode)
  .map(({ optionsCode }) => optionsCode);

export const dictsList = JSON.parse(JSON.stringify(columns)).filter(
  ({ optionsCode }) => optionsCode
);

export const dictsKey = JSON.parse(JSON.stringify(columns))
  .filter(({ dictOptionCode }) => dictOptionCode)
  .map(({ dictOptionCode }) => dictOptionCode);

const rulesObj = {};
columns.forEach((item) => {
  if (["EKORG", "KUNNR"].includes(item.prop)) {
    const text = item.type ? "选择" : "输入";
    const trigger = item.type ? "change" : "blur";
    rulesObj[item.prop] = [
      { required: true, message: `请${text}${item.label}`, trigger: trigger },
    ];
    // if (item.prop === "KWMENG") {
    //   rulesObj.KWMENG = [
    //     { required: true, message: "请输入数量", trigger: trigger },
    //     // { type: 'number', message: '数量必须为数字值'}
    //   ];
    // }
  }
});

export const rules = rulesObj;

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["purchase/purApply:line:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["purchase/purApply:line:del"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["purchase/purApply:line:del"],
  },
  uploadExcel: {
    isShow: true,
    hasPermi: ["purchase/purApply:line:UploadExcel"],
  },
};

export const getExcelId = (excelData) => {
  const data = JSON.parse(JSON.stringify(excelData));
  // console.log(data, excelData, "dadad");
  const obj = {};
  lineColumns.forEach((item) => {
    obj[item.label] = item.prop;
  });
  const _data = data.map((item) => {
    const obj2 = {};
    Object.keys(item).forEach((key) => {
      const prop = obj[key];
      obj2[prop] = item[key];
    });
    return obj2;
  });
  return _data;
};
