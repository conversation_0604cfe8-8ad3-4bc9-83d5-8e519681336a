export default {
  data(){
   return {}
  },
  methods: {
    getSearchcallBack(params, activeProps) {
      // 根据库存地点回显行库存地点
      if (
        this.backList.includes(activeProps.prop) &&
        params[activeProps.prop] &&
        activeProps?.backProps
      ) {
        const { backLable, prop } = activeProps.backProps[0];
        const label1 = `${prop},${backLable}`;
        this.dataProps = {
          initLineData: {
            ...this.dataProps.initLineData,
            [prop]: params[prop],
            [label1]:params[label1],
          },
        };
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        if (lineList && lineList.length) {
          const list = lineList.map((item) => {
            let [key = "", value = ""] = params[label1];
            if (!key) {
              return item;
            }
            return {
              ...item,
              [prop]: item[prop] || params[prop],
              [label1]: item[prop] ? item[label1] : params[label1],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      } else if(this.backList.includes(activeProps.prop)) {

        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const prop = activeProps.prop;
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            [prop]: params[prop],
          },
        };

        if (lineList && lineList.length) {
          const list = lineList.map((item) => {
            return {
              ...item,
              [prop]: item[prop] || params[prop],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
  },
};
