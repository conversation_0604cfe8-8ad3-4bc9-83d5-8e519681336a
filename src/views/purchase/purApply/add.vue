<template>
  <div class="add" v-loading="loading">
    <div @click="closeEdit">
      <ComSearch
        :searchData="columns"
        :searchInfo="searchInfo"
        :showNum="columns.length"
        labelWidth="140px"
        :showBtn="false"
        :cancelShow="['draft', 'E'].includes(detail.docStatus)"
        :handFuntion="handFuntion"
        :cancelLoading="loading"
        :rules="rules"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        :getSearchcallBack="getSearchcallBack"
        @submitEmits="submitEmits"
      >
        <template
          #searchButton
          v-if="['draft', 'E', ''].includes(detail.docStatus)"
        >
          <el-button
            v-hasPermi="[
              'purchase/purApply:purApply:add',
              'purchase/purApply:purApply:edit',
            ]"
            type="primary"
            v-loading="loading"
            size="mini"
            @click="submitTip('add')"
          >
            {{ pageType !== "add" ? "修改" : "保存" }}
          </el-button>
        </template>
      </ComSearch>
    </div>
    <LineTable
      :btnProps="btnProps"
      :isShowRightBtn="pageType !== 'add'"
      :lineList="lineList"
      :lineCloumn="lineColumns"
      keyId="purApplyId"
      POSNRKey="BNFPO"
      :requiredKeys="['KWMENG', 'ZZMATNR', 'EKGRP', 'LPEIN', 'LGORT']"
      :dataProps="dataProps"
      :showSelection="true"
      :isShowCancel="['draft', 'E'].includes(detail.docStatus)"
      :cancelFuntion="handFuntion"
      :checkSelectMsg="checkSelectMsg"
      @changeLineList="(val) => (lineList = val)"
      @refreshList="getDetail"
      ref="lineTable"
    >
      <template #lineTableHeader>
        <el-button
          v-if="['draft'].includes(detail.docStatus) && pageType !== 'add'"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          :disabled="$refs.lineTable.newlineList.length === 0"
          @click.stop="addDemand"
          v-hasPermi="['purchase/purApply:changeDemand']"
          >采购申请转需求单</el-button
        >

      </template>
      <template #lineStatus="row">
        <dict-tag
          :options="dict.type.demand_line_status"
          :value="row.lineStatus"
        />
      </template>
    </LineTable>

    <ComModalSearch
      v-if="['VKORG'].findIndex((prop) => prop === item.key) > -1"
      v-for="(item, index) in componentsModal"
      :key="index"
      :ref="'ComModalSearch' + item.key"
      v-bind="item"
      :searchData="ColumnsObj[item.key]"
      :column="ColumnsObj[item.key]"
      @handenOk="handenOk(item.key, $event)"
      :checkSelectMsg="checkSelectMsg"
      :getTableList="getTableList"
    />
  </div>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import ComModalSearch from "@/components/ComModalSearch/index.vue";
import { setTableDictValue } from "@/utils/index.js";
import {
  columns,
  dictsKey,
  dicts,
  rules,
  lineColumns,
  btnProps,
  getColumns,
  getLineColumns,
  getExcelId,
} from "./config";
import {
  listPurApply,
  getPurApply,
  delPurApply,
  addPurApply,
  updatePurApply,
} from "@/api/purchase/purApply/purApply";
import { formatNowDate, nextFormatNowDate } from "@/utils";
import {
  componentsModal,
  ColumnsObj,
  getRequestData,
} from "@/components/ComSearch/config";
import lineLgort from "./lineLgort";
export default {
  name: "PurApplyAdd",
  dictsKey,
  dicts: [...dicts,'pltyp','zterm'],
  components: {
    ComSearch,
    LineTable,
    ComModalSearch,
  },
  mixins: [lineLgort],
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      backList: ["LGORT", "EKGRP"],
      VKORGnewActionData: {},
      btnProps,
      loading: false,
      searchInfo: {
        BSTYP: "NB",
        BSART: "NB",
        AFNAM: this.$store.state.user.name,
      },
      rules: rules,
      columns: columns,
      pageType,
      lineList: [],
      lineColumns,
      componentsModal,
      ColumnsObj,
      getRequestData,
      detail: {
        docStatus: "",
      },
      dataProps: {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
          LPEIN: formatNowDate(),
        },
      },
    };
  },

  created() {
    this.getDetail();
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  watch: {},
  methods: {
    closeEdit() {
      // console.log(this.$refs.lineTable,'11111')
      this.$refs.lineTable.closeEdit();
    },
    getDetail() {
      if (!this.$route.params.id || this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getPurApply(this.$route.params.id)
        .then((res) => {
          // console.log(res, "res");
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            if (this.detail.docStatus) {
              if (!["E", "", "draft"].includes(this.detail.docStatus)) {
                const obj = { ...btnProps };
                Object.keys(btnProps).forEach((item) => {
                  obj[item] = {
                    ...obj[item],
                    isShow: false,
                  };
                });
                this.btnProps = obj;
              }

              this.columns = getColumns(this.detail.docStatus);
              this.lineColumns = getLineColumns(this.detail.docStatus);
            }
            let list =
              res?.data?.purApplyLineInfoList ||
              res.data.purApplyLineList ||
              [];
            let lineData = list?.map((item, index) => {
              item.id = ["E", "", "draft"].includes(res.data?.docStatus)
                ? `${item.purApplyId}${index}`
                : undefined;
              return {
                ...item,
              };
            });
            const _rows2 = setTableDictValue(
              this.lineColumns,
              lineData,
              this.dictDataOptions
            );
            this.searchInfo = { ...this.detail };

            this.lineList = _rows2 || [];
          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
    submitTip(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    checkSelectMsg(row) {
      const dataInfo = this.$refs.ComSearchInfo.changeInfoData();
      if (row?.salesOrg === dataInfo.EKORG) {
        this.$modal.msgError("选择的销售组织不能与采购方销售组织一样");
        return true;
      }
      return false;
    },
    submitEmits(data, type) {
      const newlineList = this.$refs.lineTable.newlineList || [];
      const delLineIdList = this.$refs.lineTable.delLineIdList?.map((item) => {
        item.delFlag = "2";
        return item;
      });
      if (this.$refs.lineTable.checkList()) return;
      let action = type;
      let fun = addPurApply;
      if (this.detail?.purApplyNo && type !== "submit") {
        // fun = updatePurApply;
        action = "upd";
      }

      if (this.loading) return;
      this.loading = true;
      let BNFPO = 0;
      const list = newlineList.map((item, index) => {
        BNFPO = item.BNFPO || `${BNFPO * 1 + 10}`;
        return {
          ...item,
          BNFPO,
          posnr: BNFPO,
          // LIFNR: "",
          // FLIEF: "",
          // VRTYP: "",
          // LGORT: "",
          purApplyId: item.purApplyNo ? item.purApplyId : undefined,
        };
      });

      const params = {
        type: "",
        action: type == "submit" ? "submit" : action,
        purchaseApplyInfos: {
          ...this.detail,
          ...data,
          hMemo: data?.remark,
          delFlag: "0",
          VKORG:
            type == "submit" ? this.VKORGnewActionData?.salesOrg : undefined,
          purApplyLineInfoList: [...list, ...delLineIdList],
          purApplyLineList: [...list, ...delLineIdList],
          addPurApplyLineList: list.filter(
            ({ purApplyId = "" }) => !purApplyId
          ),
        },
      };

      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$refs.lineTable.delLineIdList = [];
            if (this?.detail?.purApplyNo) {
              this.getDetail();
            } else if (res?.data?.purApplyNo) {
              this.$store
                .dispatch("tagsView/delPage", { name: "PurApplyAdd" })
                .then(() => {
                  this.$router.replace(
                    `/purchase/purApply-detail/index/edit/${res.data?.purApplyNo}`
                  );
                });
            }

            this.$modal.msgSuccess("操作成功");
          }
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    addDemand() {
      const newlineList = this.$refs.lineTable.newlineList || [];

      if(!newlineList || newlineList.length === 0){
        this.$modal.msgError("请添加行数据");
        return
      }
      
      if (this.$refs.lineTable.checkList()) return;
      this.$refs.ComModalSearchVKORG[0].dialogVisible = true;
    },
    getTableList(params) {
      return getRequestData.VKORG(params);
    },
    handenOk(key, event) {
      this.VKORGnewActionData = event;
      this.submitTip("submit");
    },
    handFuntion(type,list){

      if(list){

      }

    }
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}
.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
