<template>
  <div :class="isSelfPage ? 'app-container': 'app-container app-container2'">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchShow="isSelfPage"
      :tableProps="{lineBtnrightClass:'lineBtnright2'}"
      :isShowRightBtn="!isSelfPage"
      :keyId="isSelfPage ? 'purApply':'purApply2'"
      ref="ComTableList"
    >
    <template
        v-for="(item, index) in dictsList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>

     <template #searchButton>

        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          v-hasPermi="['purchase/purApply:purApply:add']"
          @click="gotoAdd('add',{})"
          >新增</el-button
        >
      </template>
       <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
            v-hasPermi="['purchase/purApply:purApply:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
            v-hasPermi="['purchase/purApply:purApply:detail']"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";

import { listPurApply } from "@/api/purchase/purApply/purApply";

import { searchData, columns,dictsKey,dicts,dictsList,columns2 } from "./config";

export default {
  name: "PurDeliveryList",
  components: {
    ComTableList,
  },
  dicts:[...dicts,'pltyp','zterm'],
  dictsKey,
  data() {
     const as = location.href.indexOf("/purchase/purApply") > -1
    return {
      columns:as ? columns : columns2,
      searchData: searchData,
      dictsList
    };
  },
  created() {
  },
   computed: {
    isSelfPage() {
      return location.href.indexOf("/purchase/purApply") > -1;
    },
  },
  methods: {
    comitList(params){
       this.$refs.ComTableList.searchComTableInfo = params
       this.$refs.ComTableList.submitEmits(params)
    },
    async getList(params) {
      return listPurApply(params);
     
    },
    gotoAdd(type,row){
      this.$store.dispatch("tagsView/delPage", { name: "PurApplyAdd" }).then(()=>{
        this.$router.push(`/purchase/purApply-detail/index/${type}/${row?.purApplyNo}`);
      });
      
    }
   
  },
};
</script>

<style lang="scss">
  .app-container2{
    margin-top: 20px;
     padding-left:0px ;
    padding-right: 0px;
  }
 .app-container2 {
  .lineBtnright2 {
      top: -42px ;
  }
 }

</style>

