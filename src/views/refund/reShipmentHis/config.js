import { listReShipmentHis, getReShipmentHis, delReShipmentHis, addReShipmentHis, updateReShipmentHis } from "@/api/sales/reShipmentHis";

export const cloumn = [
  {
    label: "销售提货单",
    type: "input",
    prop: "deliveryNo",
  },

  {
    label: "提货单行号",
    type: "input",
    prop: "posnr",
  },

  {
    label: "销售组织",
    type: "input",
    prop: "salesOrg",
  },

  {
    label: "客户号",
    type: "input",
    prop: "kunnr",
  },


    {
    label: "提货单日期",
    type: "date",
    prop: "deliveryDate",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "zzmatnr",
  },

  {
    label: "数量",
    type: "input",
    prop: "qty",
  },

  {
    label: "单位",
    type: "input",
    prop: "meins",
  },


  
  {
    label: "单价",
    type: "input",
    prop: "netpr",
  },
  
].map((item)=>{
  item.width = item.width || 140
  return item
});


export const pageConfig = {
  indexPageConfig: {
    title: "期数历史发货数据",
    columns: cloumn,
    searchData: cloumn.slice(0,6),
    tableProps: {
      rowKey: "reShipmentId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['sales:reShipmentHis:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['sales:reShipmentHis:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "sales/reShipmentHis/export",
        clickType:'export',
        hasPermi:['sales:reShipmentHis:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['sales:reShipmentHis:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['sales:reShipmentHis:remove']


      },
    ],
    // 列表
    getList: listReShipmentHis,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    addShowNum:cloumn.length,
    addSearchProps: {
    },
    headerSearchInfo:{},
    // 新增
    addFunction: addReShipmentHis,
    // 更新
    updataFuntion: updateReShipmentHis,
    // 删除
    delFunction: delReShipmentHis,
    // 详情
    detailFunction: getReShipmentHis,
  },
};
