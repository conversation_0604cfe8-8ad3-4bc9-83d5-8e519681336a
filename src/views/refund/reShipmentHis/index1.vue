<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="销售提货单" prop="deliveryNo">
        <el-input
          v-model="queryParams.deliveryNo"
          placeholder="请输入销售提货单"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售组织" prop="salesOrg">
        <el-input
          v-model="queryParams.salesOrg"
          placeholder="请输入销售组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="提货单日期">
        <el-date-picker
          v-model="daterangeDeliveryDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales:reShipmentHis:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales:reShipmentHis:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales:reShipmentHis:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales:reShipmentHis:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reShipmentHisList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="reShipmentId" />
      <el-table-column label="销售提货单" align="center" prop="deliveryNo" />
      <el-table-column label="提货单行号" align="center" prop="posnr" />
      <el-table-column label="销售组织" align="center" prop="salesOrg" />
      <el-table-column label="客户号" align="center" prop="kunnr" />
      <el-table-column label="提货单日期" align="center" prop="deliveryDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="专卖物料号" align="center" prop="zzmatnr" />
      <el-table-column label="数量" align="center" prop="qty" />
      <el-table-column label="单位" align="center" prop="meins" />
      <el-table-column label="单价" align="center" prop="netpr" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales:reShipmentHis:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales:reShipmentHis:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改期初历史发货数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="销售提货单" prop="deliveryNo">
          <el-input v-model="form.deliveryNo" placeholder="请输入销售提货单" />
        </el-form-item>
        <el-form-item label="提货单行号" prop="posnr">
          <el-input v-model="form.posnr" placeholder="请输入提货单行号" />
        </el-form-item>
        <el-form-item label="销售组织" prop="salesOrg">
          <el-input v-model="form.salesOrg" placeholder="请输入销售组织" />
        </el-form-item>
        <el-form-item label="客户号" prop="kunnr">
          <el-input v-model="form.kunnr" placeholder="请输入客户号" />
        </el-form-item>
        <el-form-item label="提货单日期" prop="deliveryDate">
          <el-date-picker clearable
            v-model="form.deliveryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择提货单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="专卖物料号" prop="zzmatnr">
          <el-input v-model="form.zzmatnr" placeholder="请输入专卖物料号" />
        </el-form-item>
        <el-form-item label="数量" prop="qty">
          <el-input v-model="form.qty" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="单位" prop="meins">
          <el-input v-model="form.meins" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="单价" prop="netpr">
          <el-input v-model="form.netpr" placeholder="请输入单价" />
        </el-form-item>
        <el-form-item label="${comment}" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入${comment}" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listReShipmentHis, getReShipmentHis, delReShipmentHis, addReShipmentHis, updateReShipmentHis } from "@/api/sales/reShipmentHis";

export default {
  name: "ReShipmentHis",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 期初历史发货数据表格数据
      reShipmentHisList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // $comment时间范围
      daterangeDeliveryDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deliveryNo: null,
        salesOrg: null,
        deliveryDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询期初历史发货数据列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDeliveryDate && '' != this.daterangeDeliveryDate) {
        this.queryParams.params["beginDeliveryDate"] = this.daterangeDeliveryDate[0];
        this.queryParams.params["endDeliveryDate"] = this.daterangeDeliveryDate[1];
      }
      listReShipmentHis(this.queryParams).then(response => {
        this.reShipmentHisList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        reShipmentId: null,
        deliveryNo: null,
        posnr: null,
        salesOrg: null,
        kunnr: null,
        deliveryDate: null,
        zzmatnr: null,
        qty: null,
        meins: null,
        netpr: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        userId: null,
        deptId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDeliveryDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.reShipmentId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加期初历史发货数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const reShipmentId = row.reShipmentId || this.ids
      getReShipmentHis(reShipmentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改期初历史发货数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.reShipmentId != null) {
            updateReShipmentHis(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReShipmentHis(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reShipmentIds = row.reShipmentId || this.ids;
      this.$modal.confirm('是否确认删除期初历史发货数据编号为"' + reShipmentIds + '"的数据项？').then(function() {
        return delReShipmentHis(reShipmentIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/reShipmentHis/export', {
        ...this.queryParams
      }, `reShipmentHis_.xlsx`)
    }
  }
};
</script>
