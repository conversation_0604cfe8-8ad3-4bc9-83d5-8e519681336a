<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="SAS退货申请单号" prop="torderSqno">
        <el-input
          v-model="queryParams.torderSqno"
          placeholder="请输入SAS退货申请单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单据状态" prop="docStatus">
        <el-select v-model="queryParams.docStatus" placeholder="请选择单据状态" clearable>
          <el-option
            v-for="dict in dict.type.demand_doc_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售组织" prop="vkorg">
        <el-input
          v-model="queryParams.vkorg"
          placeholder="请输入销售组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分销渠道" prop="vtweg">
        <el-input
          v-model="queryParams.vtweg"
          placeholder="请输入分销渠道"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品组" prop="spart">
        <el-input
          v-model="queryParams.spart"
          placeholder="请输入产品组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户代码" prop="kunnr">
        <el-input
          v-model="queryParams.kunnr"
          placeholder="请输入客户代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="name1">
        <el-input
          v-model="queryParams.name1"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售办公室" prop="vkbur">
        <el-input
          v-model="queryParams.vkbur"
          placeholder="请输入销售办公室"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales:reOrder:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales:reOrder:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales:reOrder:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales:reOrder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="reOrderHeadId" />
      <el-table-column label="SAS退货申请单号" align="center" prop="torderSqno" />
      <el-table-column label="单据状态" align="center" prop="docStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.demand_doc_status" :value="scope.row.docStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="销售组织" align="center" prop="vkorg" />
      <el-table-column label="分销渠道" align="center" prop="vtweg" />
      <el-table-column label="产品组" align="center" prop="spart" />
      <el-table-column label="客户代码" align="center" prop="kunnr" />
      <el-table-column label="客户名称" align="center" prop="name1" />
      <el-table-column label="客户类型" align="center" prop="customerType" />
      <el-table-column label="退货原因" align="center" prop="returnReason" />
      <el-table-column label="销售办公室" align="center" prop="vkbur" />
      <el-table-column label="审批状态" align="center" prop="releaseStatus" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="部门id" align="center" prop="deptId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales:reOrder:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales:reOrder:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改销售退货申请单头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="SAS退货申请单号" prop="torderSqno">
          <el-input v-model="form.torderSqno" placeholder="请输入SAS退货申请单号" />
        </el-form-item>
        <el-form-item label="单据状态" prop="docStatus">
          <el-select v-model="form.docStatus" placeholder="请选择单据状态">
            <el-option
              v-for="dict in dict.type.demand_doc_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="销售组织" prop="vkorg">
          <el-input v-model="form.vkorg" placeholder="请输入销售组织" />
        </el-form-item>
        <el-form-item label="分销渠道" prop="vtweg">
          <el-input v-model="form.vtweg" placeholder="请输入分销渠道" />
        </el-form-item>
        <el-form-item label="产品组" prop="spart">
          <el-input v-model="form.spart" placeholder="请输入产品组" />
        </el-form-item>
        <el-form-item label="客户代码" prop="kunnr">
          <el-input v-model="form.kunnr" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="客户名称" prop="name1">
          <el-input v-model="form.name1" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="退货原因" prop="returnReason">
          <el-input v-model="form.returnReason" placeholder="请输入退货原因" />
        </el-form-item>
        <el-form-item label="销售办公室" prop="vkbur">
          <el-input v-model="form.vkbur" placeholder="请输入销售办公室" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="部门id" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门id" />
        </el-form-item>
        <el-divider content-position="center">销售退货申请单行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddSalesReOrderLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteSalesReOrderLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="salesReOrderLineList" :row-class-name="rowSalesReOrderLineIndex" @selection-change="handleSalesReOrderLineSelectionChange" ref="salesReOrderLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="SAS退货申请单号" prop="torderSqno" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.torderSqno" placeholder="请输入SAS退货申请单号" />
            </template>
          </el-table-column>
          <el-table-column label="序号" prop="itemNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.itemNo" placeholder="请输入序号" />
            </template>
          </el-table-column>
          <el-table-column label="图号" prop="zparh" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.zparh" placeholder="请输入图号" />
            </template>
          </el-table-column>
          <el-table-column label="名称" prop="name" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.name" placeholder="请输入名称" />
            </template>
          </el-table-column>
          <el-table-column label="可退数量" prop="allowQty" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.allowQty" placeholder="请输入可退数量" />
            </template>
          </el-table-column>
          <el-table-column label="申请退货数量" prop="applyQty" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.applyQty" placeholder="请输入申请退货数量" />
            </template>
          </el-table-column>
          <el-table-column label="物料单位" prop="kein" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.kein" placeholder="请输入物料单位" />
            </template>
          </el-table-column>
          <el-table-column label="单价" prop="netpr" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.netpr" placeholder="请输入单价" />
            </template>
          </el-table-column>
          <el-table-column label="折扣" prop="discount" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.discount" placeholder="请输入折扣" />
            </template>
          </el-table-column>
          <el-table-column label="退货金额" prop="amount" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.amount" placeholder="请输入退货金额" />
            </template>
          </el-table-column>
          <el-table-column label="子公司备注" prop="remark" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" placeholder="请输入子公司备注" />
            </template>
          </el-table-column>
          <el-table-column label="来源提货单号" prop="originalDn" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.originalDn" placeholder="请输入来源提货单号" />
            </template>
          </el-table-column>
          <el-table-column label="来源提货单行号" prop="originalItem" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.originalItem" placeholder="请输入来源提货单行号" />
            </template>
          </el-table-column>
          <el-table-column label="提货日期" prop="originalDate" width="240">
            <template slot-scope="scope">
              <el-date-picker clearable v-model="scope.row.originalDate" type="date" value-format="yyyy-MM-dd" placeholder="请选择提货日期" />
            </template>
          </el-table-column>
          <el-table-column label="超期范围" prop="overdueDate" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.overdueDate" placeholder="请输入超期范围" />
            </template>
          </el-table-column>
          <el-table-column label="整改文件号" prop="documentNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.documentNo" placeholder="请输入整改文件号" />
            </template>
          </el-table-column>
          <el-table-column label="退货方式" prop="returnMode" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.returnMode" placeholder="请输入退货方式" />
            </template>
          </el-table-column>
          <el-table-column label="前90天已退货金额" prop="returnAmount90days" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.returnAmount90days" placeholder="请输入前90天已退货金额" />
            </template>
          </el-table-column>
          <el-table-column label="同意退货数量" prop="approveQty" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.approveQty" placeholder="请输入同意退货数量" />
            </template>
          </el-table-column>
          <el-table-column label="上年度销售额" prop="lastYearSalesAmount" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.lastYearSalesAmount" placeholder="请输入上年度销售额" />
            </template>
          </el-table-column>
          <el-table-column label="本年累计销售额" prop="yearSalesAmount" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.yearSalesAmount" placeholder="请输入本年累计销售额" />
            </template>
          </el-table-column>
          <el-table-column label="本年累计退货金额" prop="yearReturnAmount" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.yearReturnAmount" placeholder="请输入本年累计退货金额" />
            </template>
          </el-table-column>
          <el-table-column label="制度可退货比例标准" prop="allowReturnRate" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.allowReturnRate" placeholder="请输入制度可退货比例标准" />
            </template>
          </el-table-column>
          <el-table-column label="现退货比例" prop="returnRete" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.returnRete" placeholder="请输入现退货比例" />
            </template>
          </el-table-column>
          <el-table-column label="用户id" prop="userId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.userId" placeholder="请输入用户id" />
            </template>
          </el-table-column>
          <el-table-column label="部门id" prop="deptId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deptId" placeholder="请输入部门id" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listReOrder, getReOrder, delReOrder, addReOrder, updateReOrder } from "@/api/refund/reOrder.js";

export default {
  name: "ReOrder",
  dicts: ['demand_doc_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedSalesReOrderLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销售退货申请单头信息表格数据
      reOrderList: [],
      // 销售退货申请单行信息表格数据
      salesReOrderLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        torderSqno: null,
        docStatus: null,
        vkorg: null,
        vtweg: null,
        spart: null,
        kunnr: null,
        name1: null,
        customerType: null,
        vkbur: null,
        releaseStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询销售退货申请单头信息列表 */
    getList() {
      this.loading = true;
      listReOrder(this.queryParams).then(response => {
        this.reOrderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        reOrderHeadId: null,
        torderSqno: null,
        docStatus: null,
        vkorg: null,
        vtweg: null,
        spart: null,
        kunnr: null,
        name1: null,
        customerType: null,
        returnReason: null,
        vkbur: null,
        releaseStatus: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null
      };
      this.salesReOrderLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.reOrderHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加销售退货申请单头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const reOrderHeadId = row.reOrderHeadId || this.ids
      getReOrder(reOrderHeadId).then(response => {
        this.form = response.data;
        this.salesReOrderLineList = response.data.salesReOrderLineList;
        this.open = true;
        this.title = "修改销售退货申请单头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.salesReOrderLineList = this.salesReOrderLineList;
          if (this.form.reOrderHeadId != null) {
            updateReOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reOrderHeadIds = row.reOrderHeadId || this.ids;
      this.$modal.confirm('是否确认删除销售退货申请单头信息编号为"' + reOrderHeadIds + '"的数据项？').then(function() {
        return delReOrder(reOrderHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 销售退货申请单行信息序号 */
    rowSalesReOrderLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 销售退货申请单行信息添加按钮操作 */
    handleAddSalesReOrderLine() {
      let obj = {};
      obj.torderSqno = "";
      obj.itemNo = "";
      obj.zparh = "";
      obj.name = "";
      obj.allowQty = "";
      obj.applyQty = "";
      obj.kein = "";
      obj.netpr = "";
      obj.discount = "";
      obj.amount = "";
      obj.remark = "";
      obj.originalDn = "";
      obj.originalItem = "";
      obj.originalDate = "";
      obj.overdueDate = "";
      obj.documentNo = "";
      obj.returnMode = "";
      obj.returnAmount90days = "";
      obj.approveQty = "";
      obj.lastYearSalesAmount = "";
      obj.yearSalesAmount = "";
      obj.yearReturnAmount = "";
      obj.allowReturnRate = "";
      obj.returnRete = "";
      obj.userId = "";
      obj.deptId = "";
      this.salesReOrderLineList.push(obj);
    },
    /** 销售退货申请单行信息删除按钮操作 */
    handleDeleteSalesReOrderLine() {
      if (this.checkedSalesReOrderLine.length == 0) {
        this.$modal.msgError("请先选择要删除的销售退货申请单行信息数据");
      } else {
        const salesReOrderLineList = this.salesReOrderLineList;
        const checkedSalesReOrderLine = this.checkedSalesReOrderLine;
        this.salesReOrderLineList = salesReOrderLineList.filter(function(item) {
          return checkedSalesReOrderLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleSalesReOrderLineSelectionChange(selection) {
      this.checkedSalesReOrderLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/reOrder/export', {
        ...this.queryParams
      }, `reOrder_.xlsx`)
    }
  }
};
</script>
