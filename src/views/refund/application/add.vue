<template>
  <div class="add" v-loading="loading">
    <div class="movingContent" @click="closeEdit">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="6"
        labelWidth="140px"
        :showBtn="false"
        :rules="rules"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        @submitEmits="submitEmits"
      >
     <template #searchButton v-if="pageType !== 'look'">
        <el-button
            v-if="[''].includes(detail.docStatus)"
            type="primary"
            size="mini"
            @click="save('add')"
            v-hasPermi="['Application:save']"
            >保存</el-button
          >
          <el-button
            type="primary"
            size="mini"
            v-if="['draft'].includes(detail.docStatus)"
            @click="save('upd')"
            v-hasPermi="['Application:header:edit']"
            >修改</el-button
          >
          <el-button
            v-if="['draft'].includes(detail.docStatus)"
            type="primary"
            size="mini"
            @click="save('submit')"
            v-hasPermi="['sales:reOrder:submit']"
            >提交</el-button
          >

     </template>

    </ComSearch>
    </div>
    <LineTable
      :showSelection="['', 'draft'].includes(detail.docStatus)"
      ref="lineTable"
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      keyId="salesReOrderLineList"
      POSNRKey="posnr"
      :requiredKeys="['originalDn','zzmatnr','applyQty']"
    >
    </LineTable>

  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import {
  btnProps,
  lookBtnProps,
  rules,
  getLineCloumn,
  getDetailSearchData,
} from "./config";
import { setTableDictValue } from "@/utils/index.js";

import {  getReOrder,  addReOrder, updateReOrder } from "@/api/refund/reOrder.js";


export default {
  name: "ApplicationDetail",
  dicts: ['spart','vtweg','vkbur','customer_type','overdue_date','return_mode','ship_method','source_type','zterm'],
  dictsKey: ["VKORG",],
  components: {
    ComSearch,
    LineTable,
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      searchInfo: {},
      rules: rules,
      pageType,
      lineList: [],
      detail: {
        docStatus: "",
      },
      tableProps: {

      },
    };
  },

  created() {
    this.getDetail();
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
    lineCloumn(){
      return getLineCloumn()
    },
    searchData(){
      return getDetailSearchData()
    },
    btnProps(){
      if(['','draft'].includes(this.detail.docStatus)){
        return btnProps
      }
      return lookBtnProps
    }
  },
  watch: {},
  methods: {
    closeEdit() {
      this.$refs.lineTable.closeEdit();
    },
    getDetail() {
      if (this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getReOrder(this.$route.params.id)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            this.searchInfo = res.data;
            let _rows2 = setTableDictValue(
              this.lineCloumn,
              res.data.zsasOrderLineList,
              this.dictDataOptions
            );

            this.lineList = _rows2;
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },


    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    submitEmits(data, type = "add") {
      let list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }

      if (this.loading) return;
      this.loading = true;

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];

      let fun = addReOrder;
      let posnr = 0;
      const _list = [...list].map((item) => {
        item.salesReOrderLineList = item.reOrderHeadId
          ? item.salesReOrderLineList
          : undefined;
        posnr = item.posnr * 1 || (posnr + 10) * 1;
        return {
          ...item,
          posnr: posnr,
          KWMENG_SQ: ["upd", "add"].includes(type)
            ? item.KWMENG
            : item?.KWMENG_SQ,
        };
      });

      const params = {
        action: type === "add" ? "add" : "submit",
        type: type === "add" ? "" : type,
        zsasOrderHeader: {
          ...this.detail,
          ...data,
          docStatus: undefined,
          zsasOrderLineList:[],
          addZsasOrderLineList:[],
        },
      };

      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
              this.$modal.msgSuccess(res?.msg || res?.data?.msg || "修改成功");
            if (!this.detail.reOrderHeadId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "ApplicationDetail" })
                .then(() => {
                  this.$router.replace(
                    `/refund/application-detail/index/edit/${res.data.reOrderHeadId}`
                  );
                });
            } else {

              this.getDetail();
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },



  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
