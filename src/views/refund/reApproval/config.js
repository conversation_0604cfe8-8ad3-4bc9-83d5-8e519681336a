import { listReOrderHis, getReOrderHis, delReOrderHis, addReOrderHis, updateReOrderHis } from "@/api/sales/reOrderHis";

export const cloumn = [
  {
    label: "销售退货单",
    type: "input",
    prop: "deliveryNo",
  },

  {
    label: "销售组织",
    type: "input",
    prop: "salesOrg",
  },

  {
    label: "客户号",
    type: "input",
    prop: "kunnr",
  },

    {
    label: "退货日期",
    type: "date",
    prop: "returnDate",
  },

  {
    label: "特殊退货原因",
    type: "input",
    prop: "returnReason",
    searchOut:true
  },

  {
    label: "金额",
    type: "input",
    prop: "amount",
  },

  {
    label: "币别",
    type: "input",
    prop: "currency",
  },

  
].map((item)=>{
  item.width = item.width || 140
  return item
});


export const pageConfig = {
  indexPageConfig: {
    title: "期数历史退货",
    name: "ReShipmentHis",
    columns: cloumn,
    searchData: cloumn.slice(0,4),
    tableProps: {
      rowKey: "reOrderId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['sales:reOrderHis:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['sales:reOrderHis:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "sales/reOrderHis/export",
        clickType:'export',
        hasPermi:['sales:reOrderHis:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['sales:reOrderHis:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['sales:reOrderHis:remove']


      },
    ],
    // 列表
    getList: listReOrderHis,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    addShowNum:cloumn.length,
    addSearchProps: {
    },
    headerSearchInfo:{},
    // 新增
    addFunction: addReOrderHis,
    // 更新
    updataFuntion: updateReOrderHis,
    // 删除
    delFunction: delReOrderHis,
    // 详情
    detailFunction: getReOrderHis,
  },
};
