
export const searchData = [
  {
    label: "申请人",
    type: "input",
    prop: "sysUsers",
  },
  {
    label: "订单号",
    type: "input",
    prop: "orderNo",
  },
  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },
  {
    label: "发货工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },

  {
    label: "收货工厂",
    type: "input",
    prop: "unwrk",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "unwrk",
      },
    ],
  },

  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "sas_spzt",
    slotName: "docStatus",
    fixed: "right",
  },
];

export const getSearchData = (ruku) => {

  let list = JSON.parse(JSON.stringify(searchData)).map((item) => {
    if (item.prop === "docStatus" && ruku ==='ruku') {
      item.otherBind = (item) => {
        let dis = !["E", "rkApproved", "approved"].includes(item.value);
        return {
          disabled: dis,
        };
      };
    } else if (item.prop === "docStatus" && ruku==='chuku') {
      item.otherBind = (item) => {
        let dis = !["E", "ckApproved", "approved"].includes(item.value);
        return {
          disabled: dis,
        };
      };
    } else if (item.prop === "docStatus") {
      item.otherBind = (item) => {
        let dis = !["E", "draft", "cancel", "approving"].includes(item.value);
        return {
          disabled: false,
        };
      };
    }

    return item;
  });

  return list;
};

export const columns = [
  {
    label: "申请人",
    type: "input",
    prop: "createBy",
    append: true,
    attr: {
      disabled: true,
    },
  },
  {
    label: "单据类型",
    type: "input",
    prop: "orderType",
    type: "select",
    optionsCode: "inventory_ordertype",
    slotName: "orderType",
    attr: {
      disabled: true,
    },
  },
  {
    label: "订单号",
    type: "input",
    prop: "orderNo",
    attr: {
      disabled: true,
    },
  },
  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "unwrk",
      },
    ],
  },
  {
    label: "发货工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "unwrk",
      },
    ],
  },
  {
    label: "入库子库",
    prop: "lgort",
    hiddenSearch: true,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },

  {
    label: "收货工厂",
    type: "input",
    prop: "unwrk",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      readonly: true,
      disabled: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "unwrk",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },

  {
    label: "收货地址",
    type: "input",
    prop: "address",
  },

  {
    label: "运费",
    type: "input",
    prop: "yf",
  },

  {
    label: "是否补单",
    type: "select",
    prop: "bdFlag",
    optionsCode: "bd_flag",
    slotName: "bdFlag",
  },

  {
    label: "备注",
    type: "input",
    prop: "fytxt",
    searchOut: true,
  },

  {
    label: "出库完成标识",
    slotName: "ckFlag",
    optionsCode: "ck_flag",
    type: "select",
    prop: "ckFlag",
    attr: {
      disabled: true,
    },
  },

  {
    label: "入库完成标识",
    type: "select",
    prop: "rkFlag",
    optionsCode: "ck_flag",
    slotName: "rkFlag",
    attr: {
      disabled: true,
    },
  },

  {
    label: "出/入库备注",
    prop: "remark",
    searchOut: true,
    hiddenSearch: true,
    attr: {},
  },

  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "sas_spzt",
    slotName: "docStatus",
    fixed: "right",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.width = item.width || "140px";
  return item;
});

export const getmovingData = (docStatus, ruku) => {
  console.log(ruku,'ruku')
  let data = JSON.parse(JSON.stringify(columns)).map((item) => {
    if (
      ["lgort"].includes(item.prop) &&
      ["rkApproved",].includes(docStatus) &&
      ruku === "ruku"
    ) {
      item.hiddenSearch = false;
      item.attr = {
        disabled: false,
      };
    } else if (
      ["lgort"].includes(item.prop) &&
      ["approved",'rkApproved',].includes(docStatus) &&
      ruku === "ruku"
    ) {
      item.hiddenSearch = false;
      item.attr = {
        disabled: true,
      };
    } else if (["remark", "lgort"].includes(item.prop)) {
      item.hiddenSearch = true;
      item.attr = {
        disabled: true,
      };
    } else {
      item.attr = {
        disabled: true,
      };
    }


    if (
      ["remark"].includes(item.prop) &&
      ["rkApproved",'ckApproved','E'].includes(docStatus) &&
      ruku
    ) {
      item.hiddenSearch = false;
      item.attr = {
        disabled: false,
      };
    }else if(["remark"].includes(item.prop) && ruku){
      item.hiddenSearch = false;
      item.attr = {
        disabled: true,
      };

    } 

    return item;
  });

  return data;
};

export const searchData2 = JSON.parse(JSON.stringify(columns)).map((item) => {
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

// 映射code
export const optionsCodeList = columns.filter(({ optionsCode }) => optionsCode);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lineCloumn = [
  // {
  //   prop: "posnr",
  //   label: "行项目",
  //   width: 140,
  //   attr: {
  //     disabled: true,
  //   },
  // },

  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
    append: true,
    attr: {
      // readonly: true,
    },
  },
  {
    prop: "MAKTX",
    label: "物料描述",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "KWMENG",
    label: "数量",
    width: 140,
    type: "number",
  },

  {
    prop: "LGORT",
    label: "发出库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  {
    prop: "UMLGO",
    label: "接收库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "UMLGO",
      },
    ],
  },

  {
    prop: "MEINS",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
    optionsCode: "meins",
    // width:140
  },
  {
    prop: "KWMENG_SQ3",
    label: "可用库存数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },
  {
    prop: "KWMENG_SQ",
    label: "申请数量",
    width: 140,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_SP",
    label: "审批数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  // {
  //   prop: "KWMENG_SD",
  //   label: "锁定数量",
  //   width: 200,
  //   attr: {
  //     disabled: true,
  //   },
  //   // width: 140,
  // },

  {
    prop: "KWMENG_CK",
    label: "出库数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_RK",
    label: "入库数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },
  {
    prop: "ABGRU",
    label: "拒绝原因",
    width: 140,
    attr: {
      disabled: true,
    },
    optionsCode:'abgru',
    fixed: "right",
    // width:140
  },
  {
    prop: "SGTXT",
    label: "备注",
    width: 200,
    // width:140
  },
];

export const lineCloumn2 = JSON.parse(JSON.stringify(lineCloumn)).map(
  (item) => {
    if (item.prop !== "KWMENG") {
      item.attr = {
        disabled: true,
      };
    } else {
      item.inputChange = (val, oldVal, self) => {
        //  数量小于申请数量
        const editItem = self?.row || {};
        let KWMENG_SQ = Number(editItem.KWMENG_SQ) || 0;

        // 出库小于申请数量
        if (editItem.docStatus === "ckApproved") {
          KWMENG_SQ = editItem.KWMENG_SP * 1;
        }
        if (editItem.docStatus === "ckApproved") {
          KWMENG_SQ = editItem.KWMENG_CK * 1;
        }

        if (val * 1 < 0) {
          return 1;
        } else if (val * 1 > KWMENG_SQ * 1) {
          return KWMENG_SQ;
        }
        return val;
      };
    }
    return {
      ...item,
    };
  }
);

export const getmovingLine = (docStatus, ruku) => {
  const lineCloumn = lineCloumn2.map((item) => {
    if (["UMLGO"].includes(item.prop) && docStatus === "rkApproved" && ruku) {
      item.attr = {
        disabled: false,
      };
    } else if (
      ["UMLGO"].includes(item.prop) &&
      docStatus === "rkApproved" &&
      !ruku
    ) {
      item.attr = {
        disabled: true,
      };
    } else {
      item.attr = {
        disabled: true,
      };
    }
    return {
      ...item,
    };
  });

  return lineCloumn;
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["Inventory:moving:line:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["Inventory:moving:line:del"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["Inventory:moving:line:del"],
  },
  uploadExcel: {
    isShow: true,
    hasPermi: ["Inventory:inventoryManage:uploadExcel"],
  },
};

const _btnProps = {};
Object.keys(btnProps).forEach((key) => {
  _btnProps[key] = {
    ...btnProps[key],
    isShow: false,
  };
});

export const lookBtnProps = _btnProps;

export const shenPiNumber = (zsasOrderLineList, childList, docStatus) => {
  const listT = [...zsasOrderLineList].map((item, index) => {
    // const list = index === 0 ? childList1 : childList2;
    // let num = item.KWMENG * 1 ? item.KWMENG * 1 : 0;
    const total = childList
      .filter(
        ({ ZZMATNR = "", LGORT }) =>
          item.ZZMATNR === ZZMATNR && item.LGORT === LGORT
      )
      .map(({ LABST }) => LABST)
      .reduce(
        (accumulator, currentValue) => accumulator * 1 + currentValue * 1,
        0
      );

    const obj = {
      ...item,
      KWMENG_SQ3: total,
      id: docStatus === "draft" ? `${item.zsasOrderLineId}${index}` : undefined,
      docStatus,
    };

    return {
      ...obj,
    };
  });

  return listT;
};

export const checkzmzbFlag = (row, text = "", self) => {
  if (row?.zmzbFlag === "Y") {
    self.$modal.msgError(`工厂为专卖总部，不可${text}`);
    return true;
  }
  return false;
};
