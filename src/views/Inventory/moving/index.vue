<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :isSelection="true"
      :selectable="selectable"
      @selection-change="handleSelectionChange"
    >
      <template #searchButton>
         <el-button
          v-if="ruku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="getPrint" 
          :disabled="selectList.length === 0"
          >打印</el-button
        >
        <el-button
          v-if="!ruku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd('add', {})"
          v-hasPermi="['Inventory:inventoryManage:add']"
          >新增</el-button
        >
      </template>

      <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>

      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            v-hasPermi="['Inventory:inventoryManage:edit']"
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['Inventory:inventoryManage:detail']"
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
    <Inventoryprint ref="Inventoryprint"/>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import { listInventoryManage } from "@/api/inventory/inventoryManage";
import Inventoryprint from '../components/Inventoryprint.vue'

import { searchData, getSearchData, columns, optionsCodeList } from "./config";

export default {
  name: "Moving",
  dicts: ["inventory_ordertype", "sas_spzt", "ck_flag"],
  dictsKey: ["VKORG", "LGORT"],
  components: {
    ComTableList,
    Inventoryprint
  },
  data() {
    return {
      searchData: [],
      columns,
      optionsCodeList,
      ruku: "",
      selectList:[]
    };
  },
  created() {
    let ruku = "";
    if (location.href.indexOf("movingStore") > -1) {
      ruku = "ruku";
    } else if (location.href.indexOf("movingOut") > -1) {
      ruku = "chuku";
    }
    this.ruku = ruku;
    this.searchData = getSearchData(ruku);

  },
  methods: {
     selectable(row) {
      return ["rkApproved", "approved"].includes(row.docStatus);
    },
    handleSelectionChange(val) {
      this.selectList = val;
    },
    getPrint(){
      this.$refs.Inventoryprint.printInfo({
        ids:'278',
        type:'moveing',
        title:'移仓单入库'
      })
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {
      if (!params?.docStatusList || params.docStatusList.length === 0) {
        if (this.ruku === "ruku") {
          params.docStatusList = ["E", "rkApproved", "approved"];
        } else if (this.ruku === "chuku") {
          params.docStatusList = ["E", "ckApproved", "approved"];
        } else {
          // params.docStatusList = ["E", "draft", "cancel", "approving"];
        }
      }

      return listInventoryManage({
        ...params,
        orderType: "YK",
      });
    },
    gotoAdd(type, data) {
      if (location.href.indexOf("movingStore") > -1) {
        this.$store
          .dispatch("tagsView/delPage", { name: "MovingStoreDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/store/movingStore-detail/index/${type}/${data.zsasOrderHeaderId}?ruku=ruku`
            );
          });
      } else if (location.href.indexOf("movingOut") > -1) {
        this.$store
          .dispatch("tagsView/delPage", { name: "AllotOutDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/outbound/movingOut-detail/index/${type}/${data.zsasOrderHeaderId}?ruku=chuku`
            );
          });
      } else {
        this.$store
          .dispatch("tagsView/delPage", { name: "MovingDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/moving-detail/index/${type}/${data.zsasOrderHeaderId}`
            );
          });
      }
    },
  },
};
</script>
