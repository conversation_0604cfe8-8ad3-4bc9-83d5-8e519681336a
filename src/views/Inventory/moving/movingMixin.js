import {
  addInventoryManage,
} from "@/api/inventory/inventoryManage";
export default {
  data() {
    return {};
  },
  methods: {
    goBackData() {
      this.$confirm("是否回退该单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {});
    },
    // 取消
    async cancelFuntion(data, rows) {
      try {
        const ids = rows
          .filter(({ ABGRU }) => ABGRU !== "12")
          .map(({ zsasOrderLineId }) => zsasOrderLineId);

        if (!ids || ids.length === 0) {
          this.$message({
            type: "error",
            message: `暂无取消的数据`,
          });
          return;
        }

        let list = this.$refs.lineTable.newlineList;
        list = [...list].map((item) => {
          item.ABGRU = ids.includes(item.zsasOrderLineId) ? "12" : undefined;
          return item;
        });

        const params = {
          action: "submit",
          type: "upd",
          zsasOrderHeader: {
            ...this.detail,
            zsasOrderLineList: list,
          },
        };

        if (this.loading) return;
        this.loading = true;

        await addInventoryManage({
          ...params,
        });
        this.loading = false;
        this.getDetail();
      } catch (error) {
        this.loading = false;

        console.log(error, "error");
      }
    },
  },
};
