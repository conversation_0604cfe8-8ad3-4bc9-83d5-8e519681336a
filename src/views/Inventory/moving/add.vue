<template>
  <div class="add" v-loading="loading">
    <div class="movingContent" @click="closeEdit">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="6"
        labelWidth="140px"
        :showBtn="false"
        :rules="rules"
        :closesearch="this.closesearch"
        :checkSelectMsg="this.checkSelectMsg"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        :addSearchKey="['werks', 'WERKS']"
        :addSearch="addSearch"
        :getSearchcallBack="getSearchcallBack"
        @submitEmits="submitEmits"
      >
        <template #searchButton v-if="pageType !== 'look'">
          <el-button
            v-if="['E', 'approving', 'ckApproved'].includes(detail.docStatus)"
            type="danger"
            size="mini"
            v-hasPermi="['sales/purDelivery:purDelivery:submit']"
            :disabled="loading"
            @click="goBackData('huiTui')"
            >回退</el-button
          >
          <template v-if="!ruku">
            <el-button
              v-if="[''].includes(detail.docStatus)"
              type="primary"
              size="mini"
              @click="save('add')"
              v-hasPermi="['Inventory:inventoryManage:add']"
              >保存</el-button
            >
            <el-button
              type="primary"
              size="mini"
              v-if="['draft'].includes(detail.docStatus)"
              v-hasPermi="['Inventory:inventoryManage:edit']"
              @click="save('upd')"
              >保存</el-button
            >
            <el-button
              v-if="['draft'].includes(detail.docStatus)"
              v-hasPermi="['Inventory:inventoryManage:approved']"
              type="primary"
              size="mini"
              @click="save('approved')"
              >提交</el-button
            >
          </template>
          <el-button
            type="primary"
            size="mini"
            @click="save('ckApproved')"
            v-hasPermi="['Inventory:inventoryManage:ckApproved']"
            v-if="ruku === 'chuku' && ['ckApproved'].includes(detail.docStatus)"
            >出库</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="getPrint"
            v-hasPermi="['Inventory:inventoryManage:rkApproved']"
            v-if="ruku === 'ruku' && ['rkApproved','approved','ckApproved'].includes(detail.docStatus)"
            >打印</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="save('rkApproved')"
            v-hasPermi="['Inventory:inventoryManage:rkApproved']"
            v-if="ruku === 'ruku' && ['rkApproved'].includes(detail.docStatus)"
            >入库</el-button
          >
        </template>
      </ComSearch>
    </div>
    <LineTable
      ref="lineTable"
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      :checkUploadExcel="checkUploadExcel"
      :isShowCancel="
        ['draft', 'ckApproved', 'approving'].includes(detail.docStatus)
      "
      :cancelFuntion="cancelFuntion"
      @refreshList="getDetail"
      keyId="zsasOrderLineId"
      POSNRKey="posnr"
      :requiredKeys="['ZZMATNR', 'KWMENG', 'LGORT', 'UMLGO']"
    >
    </LineTable>
    <Inventoryprint ref="Inventoryprint" />
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import { setTableDictValue } from "@/utils/index.js";
import Inventoryprint from "../components/Inventoryprint.vue";

import {
  columns,
  lineCloumn,
  btnProps,
  searchData2,
  lookBtnProps,
  lineCloumn2,
  shenPiNumber,
  checkzmzbFlag,
  getmovingData,
  getmovingLine,
} from "./config";
import {
  getInventoryManage,
  addInventoryManage,
} from "@/api/inventory/inventoryManage";
import movingMixin from "./movingMixin";
import { queryScanBarInfo } from "@/api/inventory/inventoryManage";
import { queryInventory } from "@/api/inventory/stock";
export default {
  name: "MovingDetail",
  dicts: ["inventory_ordertype", "sas_spzt", "bd_flag", "MEINS"],
  dictsKey: ["VKORG", "LGORT", "ZZMATNR"],
  components: {
    ComSearch,
    LineTable,
    Inventoryprint,
  },
  mixins: [movingMixin],
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      btnProps,
      ruku: "",
      loading: false,
      searchInfo: {
        orderType: "YK",
        sysUsers: "", // this.$store.state.user.id,
        bdFlag: "N",
      },
      rules: {
        companyCode: [
          { required: true, message: "请选择选择公司", trigger: "change" },
        ],
        werks: [
          { required: true, message: "请选择发货工厂", trigger: "change" },
        ],
        bdFlag: [
          { required: true, message: "请选择补单标识", trigger: "change" },
        ],
      },
      searchData: columns,
      pageType,
      lineCloumn,
      lineList: [],
      detail: {
        docStatus: "",
      },
    };
  },

  created() {
    this.ruku = this.$route.query.ruku;
    this.getDetail();
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
    getUrl() {},
  },
  watch: {},
  methods: {
    getPrint() {
      this.$refs.Inventoryprint.printInfo({
        ids: "278",
        type: "moveing",
        title: "移仓单入库",
      });
    },
    closeEdit() {
      // console.log(this.$refs.lineTable,'11111')
      this.$refs.lineTable.closeEdit();
    },
    getDetail() {
      if (this.$route.params.type === "add" || !this.$route.params.id) return;
      if (this.loading) return;
      this.loading = true;
      getInventoryManage(this.$route.params.id)
        .then((res) => {
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            this.searchInfo = res.data;
            // this.lineList = res.data.zsasOrderLineList;
            this.ruku = this.$route.query.ruku;
            if (
              (this.detail.docStatus !== "draft" && this.detail.docStatus) ||
              this.ruku
            ) {
              this.searchData = getmovingData(this.detail.docStatus, this.ruku);
              this.btnProps = lookBtnProps;
              this.lineCloumn = getmovingLine(this.detail.docStatus, this.ruku);
            }
            if (
              !["approved", "ckApproved", "rkApproved"].includes(
                this.detail.docStatus
              )
            ) {
              this.queryInventory(this.detail.werks);
            } else {
              const _rows2 = setTableDictValue(
                this.lineCloumn,
                res.data.zsasOrderLineList,
                this.dictDataOptions
              );
              this.lineList = _rows2;
              this.loading = false;
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    async queryInventory(WERKS) {
      try {
        const params = this.detail.zsasOrderLineList.map((item) => {
          return {
            WERKS,
            ZZMATNR: item.ZZMATNR,
            LGORT: item.LGORT,
          };
        });
        const res = await queryInventory({
          action: "SDI035",
          inventoryInfos: params,
        });

        if (res.code === 200 && res.rows) {
          const _rows = shenPiNumber(
            [...this.detail.zsasOrderLineList],
            [...res.rows],
            this.detail.docStatus
          );

          const _rows2 = setTableDictValue(
            this.lineCloumn,
            _rows,
            this.dictDataOptions
          );
          this.lineList = _rows2;
          this.loading = false;
        }
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },

    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    async submitEmits(data, type = "add") {
      const list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }
      if (this.$refs.lineTable.checkList()) return;
      if (this.loading) return;
      this.loading = true;

      try {
        // 出库校验
        if (type === "ckApproved" && this.detail?.bdFlag === "N") {
          const resData = await queryScanBarInfo({
            docNo: this.detail?.orderNo,
          });
          let obj = {};
          if (resData.code === 200 && resData.rows && resData.rows.length) {
            obj = resData.rows[0];
          }

          if (obj?.checkFlag === "Y" && obj.allScan === "N") {
            this.$modal.msgError("该订单号未全部扫码，不可出库");
            this.loading = false;
            return;
          }
        }
      } catch (error) {
        console.log(error, "error");
      }

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];
      // console.log(delLineIdList, "delLineIdList");

      let fun = addInventoryManage;
      let posnr = 0;
      const _list = [...list].map((item) => {
        item.zsasOrderLineId = item.zsasOrderHeaderId
          ? item.zsasOrderLineId
          : undefined;
        posnr = item.posnr * 1 || (posnr + 10) * 1;
        return {
          ...item,
          posnr: posnr,
          KWMENG_SQ: ["upd", "add"].includes(type)
            ? item.KWMENG
            : item?.KWMENG_SQ,
          KWMENG_SP:
            type === "approved" ? item.KWMENG : item.KWMENG_SP || undefined,
          KWMENG_RK:
            type === "rkApproved" ? item.KWMENG : item.KWMENG_RK || undefined,
          KWMENG_CK:
            type === "ckApproved" ? item.KWMENG : item.KWMENG_CK || undefined,
        };
      });

      // console.log(type, "type");

      const params = {
        action: type === "add" ? "add" : "submit",
        type: type === "add" ? "" : type,
        zsasOrderHeader: {
          ...this.detail,
          ...data,
          docStatus: undefined,
          zsasOrderLineList:
            type === "add"
              ? _list
              : [..._list, ...delLineIdList].filter(
                  ({ zsasOrderHeaderId = "" }) => zsasOrderHeaderId
                ),
          addZsasOrderLineList:
            type === "add"
              ? []
              : _list.filter(
                  ({ zsasOrderHeaderId = "" }) => !zsasOrderHeaderId
                ),
        },
      };

      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$modal.msgSuccess(res?.msg || res?.data?.msg || "修改成功");
            if (!this.detail.zsasOrderHeaderId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "MovingDetail" })
                .then(() => {
                  this.$router.replace(
                    `/inventory/moving-detail/index/edit/${res.data.zsasOrderHeaderId}`
                  );
                });
            } else {
              this.getDetail();
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    addSearch(type, item) {
      if (!["werks", "WERKS"].includes(type)) return {};
      const params = {
        werks: item.werksName,
        "werks,werksName": [item.werks, item.werksName],
        unwrk: item.werksName,
        "unwrk,werksName": [item.werks, item.werksName],
        companyCode: item.companyName,
        "companyCode,companyName": [item.company, item.companyName],
      };
      return params;
    },
    closesearch(item, prop) {
      if (!["werks"].includes(prop)) return {};
      return {
        werks: undefined,
        "werks,werksName": undefined,
        unwrk: undefined,
        "unwrk,werksName": undefined,
      };
    },
    checkSelectMsg(row, self) {
      return checkzmzbFlag(row, "移库", this);
    },
    checkUploadExcel(data, active = "") {
      const lgorntData = data.filter(({ LGORT, UMLGO }) => LGORT === UMLGO);
      if (lgorntData && lgorntData.length) {
        let mgs = lgorntData.map(({ ZZMATNR }) => ZZMATNR);
        mgs = mgs.join(",");
        if (!active) {
          this.$modal.msgError(
            `导入的行数据，发出库存地点与接受库存地点不能相同，请重新导入。`
          );
        }
        let textData = data.map((item) => {
          if (mgs.includes(item.ZZMATNR)) {
            if (!item.LGORT) {
              item.errMsg = "发出库存地点不能为空";
            } else if (!item.UMLGO) {
              item.errMsg = "接受库存地点不能为空";
            } else if (item.LGORT === item.UMLGO) {
              item.errMsg = "发出库存地点与接受库存地点不能相同";
            }
          }
          return item;
        });
        return textData;
      } else {
        return false;
      }
    },
    getSearchcallBack(params, activeProps) {
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "lgort" && params[activeProps.prop]) {
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        if (lineList && lineList.length) {
          const list = lineList.map((item) => {
            let [key = "", value = ""] = params["lgort,lgortName"];
            let label = `${value}`;

            if (item.LGORT === label) {
              return item;
            }
            return {
              ...item,
              UMLGO: params.lgort,
              "UMLGO,lgortName": params["lgort,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
