<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司代码" prop="companyCode">
        <el-input
          v-model="queryParams.companyCode"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单据编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入单据编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发货工厂" prop="werks">
        <el-input
          v-model="queryParams.werks"
          placeholder="请输入发货工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收货工厂" prop="unwrk">
        <el-input
          v-model="queryParams.unwrk"
          placeholder="请输入收货工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['Inventory:inventoryManage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['Inventory:inventoryManage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['Inventory:inventoryManage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['Inventory:inventoryManage:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inventoryManageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="zsasOrderHeaderId" />
      <el-table-column label="申请人" align="center" prop="sysUsers" />
      <el-table-column label="审批人" align="center" prop="sysUsersSp" />
      <el-table-column label="出库人" align="center" prop="sysUsersCk" />
      <el-table-column label="入库人" align="center" prop="sysUsersRk" />
      <el-table-column label="单据类型" align="center" prop="orderType" />
      <el-table-column label="公司代码" align="center" prop="companyCode" />
      <el-table-column label="单据编号" align="center" prop="orderNo" />
      <el-table-column label="发货工厂" align="center" prop="werks" />
      <el-table-column label="收货工厂" align="center" prop="unwrk" />
      <el-table-column label="出库完成标识" align="center" prop="ckFlag" />
      <el-table-column label="入库完成标识" align="center" prop="rkFlag" />
      <el-table-column label="审批状态" align="center" prop="docStatus" />
      <el-table-column label="收货地址" align="center" prop="address" />
      <el-table-column label="运费" align="center" prop="yf" />
      <el-table-column label="补单标识" align="center" prop="bdFlag" />
      <el-table-column label="备注" align="center" prop="fytxt" />
      <el-table-column label="订单日期" align="center" prop="ddTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ddTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批日期" align="center" prop="spTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.spTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存锁定日期" align="center" prop="kcsdTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.kcsdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出库日期" align="center" prop="ckTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ckTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库日期" align="center" prop="rkTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.rkTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['Inventory:inventoryManage:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['Inventory:inventoryManage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改移库调拨盘点领用头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请人" prop="sysUsers">
          <el-input v-model="form.sysUsers" placeholder="请输入申请人" />
        </el-form-item>
        <el-form-item label="审批人" prop="sysUsersSp">
          <el-input v-model="form.sysUsersSp" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="出库人" prop="sysUsersCk">
          <el-input v-model="form.sysUsersCk" placeholder="请输入出库人" />
        </el-form-item>
        <el-form-item label="入库人" prop="sysUsersRk">
          <el-input v-model="form.sysUsersRk" placeholder="请输入入库人" />
        </el-form-item>
        <el-form-item label="公司代码" prop="companyCode">
          <el-input v-model="form.companyCode" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="单据编号" prop="orderNo">
          <el-input v-model="form.orderNo" placeholder="请输入单据编号" />
        </el-form-item>
        <el-form-item label="发货工厂" prop="werks">
          <el-input v-model="form.werks" placeholder="请输入发货工厂" />
        </el-form-item>
        <el-form-item label="收货工厂" prop="unwrk">
          <el-input v-model="form.unwrk" placeholder="请输入收货工厂" />
        </el-form-item>
        <el-form-item label="出库完成标识" prop="ckFlag">
          <el-input v-model="form.ckFlag" placeholder="请输入出库完成标识" />
        </el-form-item>
        <el-form-item label="入库完成标识" prop="rkFlag">
          <el-input v-model="form.rkFlag" placeholder="请输入入库完成标识" />
        </el-form-item>
        <el-form-item label="收货地址" prop="address">
          <el-input v-model="form.address" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="运费" prop="yf">
          <el-input v-model="form.yf" placeholder="请输入运费" />
        </el-form-item>
        <el-form-item label="补单标识" prop="bdFlag">
          <el-input v-model="form.bdFlag" placeholder="请输入补单标识" />
        </el-form-item>
        <el-form-item label="备注" prop="fytxt">
          <el-input v-model="form.fytxt" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="订单日期" prop="ddTime">
          <el-date-picker clearable
            v-model="form.ddTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择订单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批日期" prop="spTime">
          <el-date-picker clearable
            v-model="form.spTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审批日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="库存锁定日期" prop="kcsdTime">
          <el-date-picker clearable
            v-model="form.kcsdTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择库存锁定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="出库日期" prop="ckTime">
          <el-date-picker clearable
            v-model="form.ckTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出库日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="入库日期" prop="rkTime">
          <el-date-picker clearable
            v-model="form.rkTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择入库日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="部门编码" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门编码" />
        </el-form-item>
        <el-form-item label="用户编码" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户编码" />
        </el-form-item>
        <el-divider content-position="center">移库调拨盘点领用行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddZsasOrderLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteZsasOrderLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="zsasOrderLineList" :row-class-name="rowZsasOrderLineIndex" @selection-change="handleZsasOrderLineSelectionChange" ref="zsasOrderLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="行项目" prop="posnr" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.posnr" placeholder="请输入行项目" />
            </template>
          </el-table-column>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="KWMENG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KWMENG" placeholder="请输入数量" />
            </template>
          </el-table-column>
          <el-table-column label="发出库存地点" prop="LGORT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.LGORT" placeholder="请输入发出库存地点" />
            </template>
          </el-table-column>
          <el-table-column label="接收库存地点" prop="UMLGO" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.UMLGO" placeholder="请输入接收库存地点" />
            </template>
          </el-table-column>
          <el-table-column label="物料描述" prop="MAKTX" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MAKTX" placeholder="请输入物料描述" />
            </template>
          </el-table-column>
          <el-table-column label="基本单位" prop="MEINS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MEINS" placeholder="请输入基本单位" />
            </template>
          </el-table-column>
          <el-table-column label="申请数量" prop="kwmengSq" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.kwmengSq" placeholder="请输入申请数量" />
            </template>
          </el-table-column>
          <el-table-column label="审批数量" prop="kwmengSp" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.kwmengSp" placeholder="请输入审批数量" />
            </template>
          </el-table-column>
          <el-table-column label="锁定数量" prop="kwmengSd" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.kwmengSd" placeholder="请输入锁定数量" />
            </template>
          </el-table-column>
          <el-table-column label="出库数量" prop="kwmengCk" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.kwmengCk" placeholder="请输入出库数量" />
            </template>
          </el-table-column>
          <el-table-column label="入库数量" prop="kwmengRk" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.kwmengRk" placeholder="请输入入库数量" />
            </template>
          </el-table-column>
          <el-table-column label="是否扫描" prop="barcodeFlag" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.barcodeFlag" placeholder="请输入是否扫描" />
            </template>
          </el-table-column>
          <el-table-column label="成本中心" prop="KOSTL" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KOSTL" placeholder="请输入成本中心" />
            </template>
          </el-table-column>
          <el-table-column label="客户" prop="KUNNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KUNNR" placeholder="请输入客户" />
            </template>
          </el-table-column>
          <el-table-column label="销售订单" prop="KDAUF" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KDAUF" placeholder="请输入销售订单" />
            </template>
          </el-table-column>
          <el-table-column label="销售订单行项目" prop="KDPOS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KDPOS" placeholder="请输入销售订单行项目" />
            </template>
          </el-table-column>
          <el-table-column label="部门编码" prop="deptId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.deptId" placeholder="请输入部门编码" />
            </template>
          </el-table-column>
          <el-table-column label="用户编码" prop="userId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.userId" placeholder="请输入用户编码" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInventoryManage, getInventoryManage, delInventoryManage, addInventoryManage, updateInventoryManage } from "@/api/inventory/inventoryManage";

export default {
  name: "InventoryManage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedZsasOrderLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 移库调拨盘点领用头信息表格数据
      inventoryManageList: [],
      // 移库调拨盘点领用行信息表格数据
      zsasOrderLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderType: null,
        companyCode: null,
        orderNo: null,
        werks: null,
        unwrk: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询移库调拨盘点领用头信息列表 */
    getList() {
      this.loading = true;
      listInventoryManage(this.queryParams).then(response => {
        this.inventoryManageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        zsasOrderHeaderId: null,
        sysUsers: null,
        sysUsersSp: null,
        sysUsersCk: null,
        sysUsersRk: null,
        orderType: null,
        companyCode: null,
        orderNo: null,
        werks: null,
        unwrk: null,
        ckFlag: null,
        rkFlag: null,
        docStatus: null,
        address: null,
        yf: null,
        bdFlag: null,
        fytxt: null,
        ddTime: null,
        spTime: null,
        kcsdTime: null,
        ckTime: null,
        rkTime: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deptId: null,
        userId: null
      };
      this.zsasOrderLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.zsasOrderHeaderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加移库调拨盘点领用头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const zsasOrderHeaderId = row.zsasOrderHeaderId || this.ids
      getInventoryManage(zsasOrderHeaderId).then(response => {
        this.form = response.data;
        this.zsasOrderLineList = response.data.zsasOrderLineList;
        this.open = true;
        this.title = "修改移库调拨盘点领用头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.zsasOrderLineList = this.zsasOrderLineList;
          if (this.form.zsasOrderHeaderId != null) {
            updateInventoryManage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventoryManage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const zsasOrderHeaderIds = row.zsasOrderHeaderId || this.ids;
      this.$modal.confirm('是否确认删除移库调拨盘点领用头信息编号为"' + zsasOrderHeaderIds + '"的数据项？').then(function() {
        return delInventoryManage(zsasOrderHeaderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 移库调拨盘点领用行信息序号 */
    rowZsasOrderLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 移库调拨盘点领用行信息添加按钮操作 */
    handleAddZsasOrderLine() {
      let obj = {};
      obj.posnr = "";
      obj.ZZMATNR = "";
      obj.KWMENG = "";
      obj.LGORT = "";
      obj.UMLGO = "";
      obj.SGTXT = "";
      obj.MAKTX = "";
      obj.MEINS = "";
      obj.kwmengSq = "";
      obj.kwmengSp = "";
      obj.kwmengSd = "";
      obj.kwmengCk = "";
      obj.kwmengRk = "";
      obj.barcodeFlag = "";
      obj.KOSTL = "";
      obj.KUNNR = "";
      obj.KDAUF = "";
      obj.KDPOS = "";
      obj.deptId = "";
      obj.userId = "";
      this.zsasOrderLineList.push(obj);
    },
    /** 移库调拨盘点领用行信息删除按钮操作 */
    handleDeleteZsasOrderLine() {
      if (this.checkedZsasOrderLine.length == 0) {
        this.$modal.msgError("请先选择要删除的移库调拨盘点领用行信息数据");
      } else {
        const zsasOrderLineList = this.zsasOrderLineList;
        const checkedZsasOrderLine = this.checkedZsasOrderLine;
        this.zsasOrderLineList = zsasOrderLineList.filter(function(item) {
          return checkedZsasOrderLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleZsasOrderLineSelectionChange(selection) {
      this.checkedZsasOrderLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('Inventory/inventoryManage/export', {
        ...this.queryParams
      }, `inventoryManage_.xlsx`)
    }
  }
};
</script>
