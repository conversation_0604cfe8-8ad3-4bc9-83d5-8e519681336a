export const searchData = [
  {
    label: "申请人",
    type: "input",
    prop: "sysUsers",
  },
  {
    label: "订单号",
    type: "input",
    prop: "orderNo",
  },
  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },
  {
    label: "发货工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },

  {
    label: "收货工厂",
    type: "input",
    prop: "unwrk",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "unwrk",
      },
    ],
  },

  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "sas_spzt",
    attr: {},
  },
];

export const getSearchData = (ruku) => {
  // let ruku = location.href.indexOf("allotStore") > -1;
  // let cuku = location.href.indexOf("allotOut") > -1;

  let list = JSON.parse(JSON.stringify(searchData)).map((item) => {
    if (item.prop === "docStatus" && ruku === 'ruku') {
      item.otherBind = (item) => {
        let dis = !["E", "rkApproved", "approved"].includes(item.value);
        return {
          disabled: dis,
        };
      };
    } else if (item.prop === "docStatus" && ruku === 'chuku') {
      item.otherBind = (item) => {
        let dis = !["E", "ckApproved", "approved"].includes(item.value);
        return {
          disabled: dis,
        };
      };
    } else if (item.prop === "docStatus" && ruku === 'shenpi') {
      item.otherBind = (item) => {
        let dis = !["approving"].includes(item.value);
        return {
          disabled: dis,
        };
      };
    }
     else if (item.prop === "docStatus") {
      item.otherBind = (item) => {
        let dis = !["E", "draft", "cancel","approving","ckApproved","rkApproved"].includes(item.value);
        return {
          disabled: dis,
        };
      };
    }

    return item;
  });

  return list;
};

export const columns = [
  {
    label: "申请人",
    type: "input",
    prop: "sysUsers",
    append: true,
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    label: "单据类型",
    type: "input",
    prop: "orderType",
    type: "select",
    optionsCode: "inventory_ordertype",
    slotName: "orderType",
    width: 200,
    attr: {
      disabled: true,
    },
  },
  {
    label: "订单号",
    type: "input",
    prop: "orderNo",
    width: 200,
    attr: {
      disabled: true,
    },
  },
  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    width: 200,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },

  {
    label: "收货工厂",
    type: "input",
    prop: "unwrk",
    width: 200,
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "unwrk",
      },
    ],
  },

  {
    label: "入库子库",
    prop: "UMLGO",
    hiddenSearch: true,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "UMLGO",
      },
    ],
  },

  {
    label: "发货工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    width: 200,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },

  {
    label: "收货地址",
    type: "input",
    width: 200,
    prop: "address",
  },

  {
    label: "运费",
    type: "input",
    prop: "yf",
    width: 200,
  },

  {
    label: "是否补单",
    type: "select",
    prop: "bdFlag",
    optionsCode: "bd_flag",
    slotName: "bdFlag",
  },

  {
    label: "发运说明",
    type: "input",
    prop: "fytxt",
    searchOut: true,
  },

  {
    label: "出库完成标识",
    type: "select",
    prop: "ckFlag",
    optionsCode: "ck_flag",
    slotName: "ckFlag",
    attr: {
      disabled: true,
    },
  },

  {
    label: "入库完成标识",
    type: "select",
    prop: "rkFlag",
    optionsCode: "ck_flag",
    slotName: "rkFlag",
    attr: {
      disabled: true,
    },
  },

  {
    label: "出/入库备注",
    prop: "remark",
    searchOut: true,
    hiddenSearch: true,
    attr: {},
  },

  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "sas_spzt",
    slotName: "docStatus",
    fixed: "right",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
];

export const searchData2 = JSON.parse(JSON.stringify(columns)).map((item) => {
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

// 映射code
export const optionsCodeList = columns.filter(({ optionsCode }) => optionsCode);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lineCloumn = [
  // {
  //   prop: "posnr",
  //   label: "行项目",
  //   width: 140,
  //   attr: {
  //     disabled: true,
  //   },
  // },

  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "ZZMATNR",
        backLable: "ZZMATNR",
        prop: "ZZMATNR",
      },
      {
        backProp: "maktxZh",
        backLable: "maktxZh",
        prop: "MAKTX",
      },
      {
        backProp: "MEINS",
        backLable: "MSEHT",
        prop: "MEINS",
      },
    ],
  },
  {
    prop: "MAKTX",
    label: "物料描述",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "KWMENG",
    label: "数量",
    width: 200,
    type: "number",
    // width: 140,
  },

  {
    prop: "LGORT",
    label: "发出库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
      disabled: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  {
    prop: "UMLGO",
    label: "接收库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "UMLGO",
      },
    ],
  },

  {
    prop: "KWMENG_SQ",
    label: "申请数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_SP",
    label: "审批数量",
    width: 140,
    type: "number",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "KWMENG_SD",
    label: "锁定数量",
    width: 140,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_CK",
    label: "出库数量",
    width: 140,
    type: "number",
    attr: {
      disabled: true,
    },
    inputChange: (val, oldVal, self) => {
      const { KWMENG_SP = 0 } = self?.row;
      const num = Number(KWMENG_SP) || 1;
      if (val > num) {
        return num;
      } else if (val <= 0) {
        return 1;
      }
      return val;
    },
    // width: 140,
  },

  {
    prop: "KWMENG_RK",
    label: "入库数量",
    width: 140,
    attr: {
      disabled: true,
    },
    type: "number",
    attr: {
      disabled: true,
    },
    inputChange: (val, oldVal, self) => {
      const { KWMENG_CK = 0 } = self?.row;
      const num = Number(KWMENG_CK) || 1;
      if (val > num) {
        return num;
      } else if (val <= 0) {
        return 1;
      }
      return val;
    },
    // width: 140,
  },

  {
    prop: "SGTXT",
    label: "备注",
    width: 200,
    // width:140
  },
  {
    prop: "MEINS",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
    optionsCode: "meins",
  },
    {
    prop: "ABGRU",
    label: "拒绝原因",
    width: 140,
    attr: {
      disabled: true,
    },
    optionsCode:'abgru',
    fixed: "right",
    // width:140
  },
];

export const getallotData = (docStatus, ruku) => {
  let data = JSON.parse(JSON.stringify(columns)).map((item) => {
    if (
      ["UMLGO"].includes(item.prop) &&
      ["rkApproved"].includes(docStatus) &&
      ruku === "ruku"
    ) {
      item.hiddenSearch = false;
      item.attr = {
        disabled: false,
      };
    } else if (
      ["UMLGO"].includes(item.prop) &&
      ["approved", "rkApproved"].includes(docStatus) &&
      ruku === "ruku"
    ) {
      item.hiddenSearch = false;
      item.attr = {
        disabled: true,
      };
    } else if (["remark", "UMLGO"].includes(item.prop)) {
      item.hiddenSearch = true;
      item.attr = {
        disabled: true,
      };
    } else {
      item.attr = {
        disabled: true,
      };
    }
  // console.log(ruku,docStatus,'ooooo')
    if (
      ["remark"].includes(item.prop) &&
      ["rkApproved", "ckApproved", "E"].includes(docStatus) &&
      ruku
    ) {
      item.hiddenSearch = false;
      if ((ruku === "chuku" && docStatus === "rkApproved")) {
        item.attr = {
          disabled: true,
        };
      } else {
        item.attr = {
          disabled: false,
        };
      }
    } else if (["remark"].includes(item.prop) && ruku) {
      item.hiddenSearch = false;
      item.attr = {
        disabled: true,
      };
    }

    return item;
  });

  return data;
};

export const lineCloumn1 = JSON.parse(JSON.stringify(lineCloumn)).map(
  (item) => {
    item.attr = {
      disabled: true,
    };
    return {
      ...item,
    };
  }
);

const xuhaoCloumn = [
  {
    label: "",
    prop: "xuhao2",
    fixed: "left",
    width: 60,
    attr: {
      disabled: true,
    },
  },
  {
    label: "序号",
    prop: "xuhao",
    fixed: "left",
    width: 60,
    attr: {
      disabled: true,
    },
  },
];

// 计算总的审批数量
export const setParantNum = (editItem, val, self) => {
  const { zsasOrderLineId: pzsasOrderLineId } = editItem.parant || {};
  const { zsasOrderLineId: chzsasOrderLineId } = editItem;
  const lineTable =
    [...self.salesOrderLineList].filter(
      ({ zsasOrderLineId }) => pzsasOrderLineId === zsasOrderLineId
    )[0].children || [];
  let list = [];
  if (lineTable && lineTable.length) {
    list = lineTable
      .filter(({ zsasOrderLineId }) => zsasOrderLineId !== chzsasOrderLineId)
      .map(({ KWMENG_SP = 0 }) => KWMENG_SP || 0);
  }
  const otherSum = list.reduce(
    (accumulator, currentValue) => accumulator * 1 + currentValue * 1,
    0
  );

  const KWMENG_SP = otherSum + val;

  const PsalesOrderLineList = [...self.salesOrderLineList].map((item) => {
    if (item[self.keyId] === pzsasOrderLineId) {
      item.KWMENG_SP = KWMENG_SP;
    }
    return item;
  });

  return PsalesOrderLineList;
};

export const lineCloumn2 = JSON.parse(
  JSON.stringify([...xuhaoCloumn, ...lineCloumn])
).map((item) => {
  // if(item.prop === 'UMLGO'){
  //   item.otherBind=(item,row)=>{
  //     console.log(item,row,'row')
  //     // if(!row.children){
  //       return{
  //         disabled:true
  //       }
  //     // }
  //     // return item.attr || {}
  //   }
  // }else

  if (item.prop !== "KWMENG_SP") {
    item.attr = {
      disabled: true,
    };
  } else {
    item.attr = {
      disabled: false,
    };
    item.setTableLine = setParantNum;

    item.inputChange = (val, oldVal, self) => {
      const { zsasOrderLineId: pzsasOrderLineId, KWMENG: pKWMENG } =
        self?.row?.parant || {};
      const { KWMENG: chKWMENG = 0, zsasOrderLineId: chzsasOrderLineId } =
        self?.row;

      const lineTable =
        [...self.lineTable].filter(
          ({ zsasOrderLineId }) => pzsasOrderLineId === zsasOrderLineId
        )[0].children || [];

      let list = [];
      if (lineTable && lineTable.length) {
        list = lineTable
          .filter(
            ({ zsasOrderLineId }) => zsasOrderLineId !== chzsasOrderLineId
          )
          .map(({ KWMENG_SP }) => KWMENG_SP || 0);
      }
      const otherSum = list.reduce(
        (accumulator, currentValue) => accumulator * 1 + currentValue * 1,
        0
      );
      let chKWMENGSp = 0;
      // 总和大于申请量
      // console.log(pKWMENG,otherSum,val,val + otherSum)
      // 10 0 100 100

      if (val + otherSum > pKWMENG) {
        // 输入的值小于现有的库存量
        // 10 + 0 <=10
        if (pKWMENG - otherSum <= pKWMENG) {
          chKWMENGSp = pKWMENG - otherSum;
        } else {
          chKWMENGSp = chKWMENG;
        }
        self.$modal.msgError("输入的审批数量，不能超过申请数量");
      } else if (val > chKWMENG) {
        chKWMENGSp = chKWMENG;
        self.$modal.msgError("输入的审批数量，不能超过库存数量");
      } else {
        chKWMENGSp = val;
      }
      // self.row.parant.KWMENG_SP = otherSum + chKWMENGSp
      return chKWMENGSp;
    };
  }

  return {
    ...item,
  };
});


export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["Allot:line:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["Allot:line:del"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["Allot:line:del"],
  },
  uploadExcel: {
    isShow: true,
    hasPermi: ["Allot:line:uploadExcel"],
  },
};

const _btnProps = {};
Object.keys(btnProps).forEach((key) => {
  _btnProps[key] = {
    ...btnProps[key],
    isShow: false,
  };
});

export const lookBtnProps = _btnProps;

export const shenPiNumber = (zsasOrderLineList, childList) => {
  const _childListA = [...childList].filter(
    ({ LABST }) => LABST && LABST * 1 > 0
  );
  const listT = [...zsasOrderLineList].map((item, index) => {
    // const list = index === 0 ? childList1 : childList2;
    let num = item.KWMENG * 1 ? item.KWMENG * 1 : 0;
    let parantNum = 0;

    const _childList = _childListA.filter(
      ({ ZZMATNR = "" }) => item.ZZMATNR === ZZMATNR
    );

    let xuhao = index + 1;

    const obj = {
      ...item,
      // KWMENG_SP: item.KWMENG,
      disabled: true,
      children: _childList.map((item2, index2) => {
        // console.log(item2.LABST, "LABST");
        const KWMENG2 = item2.LABST * 1 ? item2.LABST * 1 : 0;

        // 申请的数量 》 库存
        if (num > KWMENG2) {
          item2.KWMENG_SP = KWMENG2;
          num = num - KWMENG2;
        } else if (num < KWMENG2) {
          item2.KWMENG_SP = num;
          num = num - num;
        } else if (num === KWMENG2) {
          item2.KWMENG_SP = num;
          num = 0;
        } else if (num <= 0) {
          item2.KWMENG_SP = 0;
          num = 0;
        }

        parantNum = parantNum + item2.KWMENG_SP;

        return {
          xuhao: `${xuhao}.${index2 + 1}`,
          parant: item,
          // ...item2,
          ZZMATNR: item2.ZZMATNR,
          LGORT: item2.LGORT,
          MAKTX: item2.MAKTX,
          KWMENG: KWMENG2, //item2.LABST,
          KWMENG_SP: item2.KWMENG_SP,
          MEINS: item2.MEINS,
          zsasOrderLineId: `A${item.zsasOrderLineId}_${index2}`,
        };
      }),
    };

    return {
      ...obj,
      xuhao,
      KWMENG_SP: parantNum,
    };
  });

  return listT;
};

export const shenpiChildren = (list, type, detail = {}) => {
  const data = [...list].map((item) => {
    const childList = item?.children?.filter(
      ({ KWMENG_SP = 0 }) => KWMENG_SP * 1 > 0
    );
    return {
      ...item,
      zsasInventoryDetailsList: childList.map((item2) => {
        return {
          ZZMATNR: item2.ZZMATNR,
          KWMENG_SP: item2.KWMENG_SP,
          MAKTX: item2.MAKTX,
          LGORT: item2.LGORT,
          WERKS: item2.WERKS,
          KWMENG: item2.KWMENG,
          KWMENG_SD: item2.KWMENG_SP || undefined,
          KWMENG_CK: item2.KWMENG_CK || undefined,
          KWMENG_RK: type === "rkApproved" ? item.KWMENG_SP : undefined,
          KWMENG_CK: type === "ckApproved" ? item.KWMENG_RK : undefined,
          zsasOrderHeaderId: detail.zsasOrderHeaderId,
          zsasOrderLineId: item.zsasOrderLineId,
          MEINS: item2.MEINS,
          delFlag: "0",
        };
      }),
      children: undefined,
    };
  });

  return data;
};

export const getChildShenPiNumber = (list, type) => {
  const _list = [...list].map((item, index) => {
    let xuhao = index + 1;
    let P_KWMENG_SP = 0;
    let P_KWMENG_RK = 0;

    item.children = item?.zsasInventoryDetailsList?.map((item2, index2) => {
      return {
        ...item2,
        zsasOrderLineId: `c${item2.LGORT}${item.zsasOrderLineId + 10}`,
        xuhao: `${xuhao}.${index2 + 1}`,
        KWMENG_CK: item2.KWMENG_SP,
        KWMENG_RK: item2.KWMENG_CK || undefined,
      };
    });
    P_KWMENG_SP = item.KWMENG_SP * 1 + P_KWMENG_SP;
    P_KWMENG_RK = item.KWMENG_CK * 1 + P_KWMENG_RK;
    return {
      ...item,
      zsasOrderLineId: `${item.zsasOrderLineId}`,
      xuhao,
      KWMENG_CK: P_KWMENG_SP,
      KWMENG_RK: P_KWMENG_RK || undefined,
    };
  });

  return _list;
};

export const getLineColumns = (type, ruku) => {
  const lineCloumn = lineCloumn2.map((item) => {
    if (["rkApproved", "ckApproved", "approved"].includes(type)) {
      item.attr = {
        disabled: true,
      };
    }
    if (["approving"].includes(type) && item.prop === "KWMENG_SP") {
      item.attr = {
        disabled: false,
      };
    }

    if (
      ["UMLGO"].includes(item.prop) &&
      type === "rkApproved" &&
      ruku === "ruku"
    ) {
      item.attr = {
        disabled: false,
      };
      item.otherBind = (item, row) => {
        if (!row.children) {
          return {
            disabled: true,
          };
        }
        return item.attr || {};
      };
    } else if (
      ["UMLGO"].includes(item.prop) &&
      type === "rkApproved" &&
      !ruku
    ) {
      item.attr = {
        disabled: true,
      };
    }
    return {
      ...item,
    };
  });

  return lineCloumn;
};
