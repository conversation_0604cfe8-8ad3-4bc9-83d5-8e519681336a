<template>
  <div class="add" v-loading="loading">
    <div class="movingContent" @click="closeEdit">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="6"
        labelWidth="140px"
        :showBtn="false"
        :rules="rules"
        :closesearch="closesearch"
        :checksearchMsg="checksearchMsg"
        :checkSelectMsg="checkSelectMsg"
        :addSearchKey="['BUKRS']"
        :addSearch="addSearch"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        :getSearchcallBack="getSearchcallBack"
        @submitEmits="submitEmits"
      >
        <template #searchButton v-if="pageType !== 'look'">
          <template v-if="!ruku">
            <el-button
              v-if="[''].includes(detail.docStatus)"
              type="primary"
              size="mini"
              @click="save('add')"
              v-hasPermi="['Allot:add']"
              >保存</el-button
            >
            <el-button
              type="primary"
              size="mini"
              v-if="['draft'].includes(detail.docStatus)"
              @click="save('upd')"
              v-hasPermi="['Allot:edit']"
              >修改</el-button
            >
            <el-button
              v-if="['draft'].includes(detail.docStatus)"
              type="primary"
              size="mini"
              @click="save('submit')"
              v-hasPermi="['Allot:submit']"
              >提交</el-button
            >
          </template>

          <el-button
            v-if="
              (['approving'].includes(detail.docStatus) && ruku === 'shenpi') || (['ckApproved'].includes(detail.docStatus) && ruku === 'chuku')
            "
            type="danger"
            size="mini"
            @click="goBackData('draft')"
            v-hasPermi="['Allot:approved']"
            >回退</el-button
          >

          <el-button
            v-if="['approving'].includes(detail.docStatus) && ruku === 'shenpi'"
            type="primary"
            size="mini"
            @click="save('approved')"
            v-hasPermi="['Allot:approved']"
            >审批</el-button
          >
           <el-button
            size="mini"
            v-if="['rkApproved','approved','ckApproved'].includes(detail.docStatus) && ruku === 'ruku'"
            v-hasPermi="['Allot:rkApproved']"
             type="primary"
            @click="getPrint"
            >打印</el-button
          >
          <el-button
            size="mini"
            @click="save('rkApproved')"
            v-if="['rkApproved'].includes(detail.docStatus) && ruku === 'ruku'"
            v-hasPermi="['Allot:rkApproved']"
             type="primary"
            >入库</el-button
          >
        </template>
      </ComSearch>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="申请明细" name="add"> </el-tab-pane>
      <el-tab-pane
        label="审批"
        v-if="
          ['ckApproved', 'approving', 'rkApproved'].includes(
            this.detail.docStatus
          ) && ruku
        "
        name="approving"
      >
      </el-tab-pane>
    </el-tabs>
    <LineTable
      :showSelection="
        !['ckApproved', 'approving', 'rkApproved'].includes(
          this.detail.docStatus
        )
      "
      v-show="activeName === 'add'"
      ref="lineTable"
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      :isShowCancel="['draft', 'ckApproved','approving'].includes(detail.docStatus) && detail.docStatus !== 'approving'"
      :cancelFuntion="cancelFuntion"
      keyId="zsasOrderLineId"
      POSNRKey="posnr"
      :requiredKeys="['ZZMATNR', 'KWMENG', 'UMLGO']"
    >
    </LineTable>

    <LineTable
      v-show="activeName === 'approving'"
      :indexXH="false"
      ref="lineTable2"
      :btnProps="btnProps"
      :lineList="lineList2"
      :lineCloumn="lineCloumn2"
      keyId="zsasOrderLineId"
      POSNRKey="posnr"
      :isShowCancel="['draft', 'ckApproved','approving'].includes(detail.docStatus)"
      :cancelFuntion="cancelFuntion"
      :requiredKeys="[]"
      :tableProps="tableProps"
      :showSelection="false"
    >
    </LineTable>
     <Inventoryprint ref="Inventoryprint" />
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import Inventoryprint from "../components/Inventoryprint.vue";

import {
  columns,
  lineCloumn,
  btnProps,
  searchData2,
  lookBtnProps,
  lineCloumn2,
  lineCloumn1,
  shenPiNumber,
  shenpiChildren,
  getLineColumns,
  getChildShenPiNumber,
  getallotData,
} from "./config";
import { setTableDictValue, confirmFuntion } from "@/utils/index.js";
import {
  getInventoryManage,
  addInventoryManage,
} from "@/api/inventory/inventoryManage";
import { queryScanBarInfo } from "@/api/inventory/inventoryManage";
import { queryInventory } from "@/api/inventory/stock";
import { checkzmzbFlag } from "../moving/config";
import movingMixin from '../moving/movingMixin'
export default {
  name: "AllotDetail",
  dicts: ["inventory_ordertype", "sas_spzt", "bd_flag", "meins", "ck_flag"],
  dictsKey: ["VKORG", "LGORT"],
  components: {
    ComSearch,
    LineTable,
    Inventoryprint
  },
  props: {},
  mixins:[movingMixin],
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      activeName: "add",
      btnProps,
      loading: false,
      searchInfo: {
        orderType: "DB",
        sysUsers: "", // this.$store.state.user.id,
        bdFlag: "N",
      },
      rules: {
        companyCode: [
          { required: true, message: "请选择选择公司", trigger: "change" },
        ],
        werks: [
          { required: true, message: "请选择发货工厂", trigger: "change" },
        ],
        unwrk: [
          { required: true, message: "请选择收货工厂", trigger: "change" },
        ],
        bdFlag: [
          { required: true, message: "请选择补单标识", trigger: "change" },
        ],
      },
      searchData: columns,
      pageType,
      lineCloumn,
      lineCloumn2: lineCloumn2,
      lineList: [],
      lineList2: [],
      detail: {
        docStatus: "",
      },
      tableProps: {
        defaultExpandAll: true,
        treeProps: { children: "children", hasChildren: "hasChildren" },
        rowKey: "zsasOrderLineId",
      },
      ruku: "",
    };
  },

  created() {
    this.getDetail();
    this.ruku = this.$route.query?.ruku || "";
    // console.log(this.ruku, "000");
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  mounted() {},
  watch: {},
  methods: {
    getPrint() {
      this.$refs.Inventoryprint.printInfo({
        ids: "278",
        type: "allot",
        title: "调拨单入库",
      });
    },
    closeEdit() {
      // console.log(this.$refs.lineTable,'11111')
      this.$refs.lineTable.closeEdit();
    },
    getDetail() {
      if (this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getInventoryManage(this.$route.params.id)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            this.searchInfo = {
              ...res.data,
              UMLGO: res?.data.lgort,
            };
            this.lineList = res.data.zsasOrderLineList;
            if (
              ["ckApproved", "approving", "rkApproved"].includes(
                this.detail.docStatus
              ) &&
              this.ruku
            ) {
              this.activeName = "approving";
            }

            if (
              (this.detail.docStatus !== "draft" && this.detail.docStatus) ||
              this.ruku
            ) {
              this.searchData = getallotData(this.detail.docStatus, this.ruku);
              this.btnProps = lookBtnProps;
              this.lineCloumn = lineCloumn1;
              this.lineCloumn2 = getLineColumns(
                this.detail.docStatus,
                this.ruku
              );
            }

            if (["approving"].includes(this.detail.docStatus) && this.ruku) {
              this.activeName = "approving";
              this.queryInventory(this.detail.werks);
            }
            if (
              ["ckApproved", "rkApproved"].includes(this.detail.docStatus) &&
              this.ruku
            ) {
              const list = getChildShenPiNumber(
                res.data.zsasOrderLineList,
                this.detail.docStatus
              );
              let rows2 = setTableDictValue(
                this.lineCloumn,
                list,
                this.dictDataOptions
              );
              this.lineList2 = rows2;
            }

            let _rows2 = setTableDictValue(
              this.lineCloumn,
              this.lineList,
              this.dictDataOptions
            );
            if (this.detail.docStatus === "draft") {
              _rows2 = _rows2.map((item, index) => {
                return {
                  ...item,
                  id:
                    this.detail.docStatus === "draft"
                      ? `${item.zsasOrderLineId}${index}`
                      : undefined,
                };
              });
            }
            this.lineList = _rows2;
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
    async queryInventory(WERKS) {
      try {
        const params = this.detail.zsasOrderLineList.map((item) => {
          return {
            WERKS,
            ZZMATNR: item.ZZMATNR,
          };
        });

        this.loading = true;
        const res = await queryInventory({
          action: "SDI035",
          inventoryInfos: params,
        });

        if (res.code === 200 && res.rows) {
          const _rows = shenPiNumber(
            [...this.detail.zsasOrderLineList],
            [...res.rows]
          );
          const _rows2 = setTableDictValue(
            this.lineCloumn,
            _rows,
            this.dictDataOptions
          );
          this.lineList2 = _rows2;
        }
        this.loading = false;
      } catch (error) {
        this.loading = false;

        const _rows2 = setTableDictValue(
          this.lineCloumn,
          this.detail.zsasOrderLineList,
          this.dictDataOptions
        );
        this.lineList2 = _rows2;

        console.log(error);
      }
    },

    goBackData() {
      confirmFuntion(this, "回退该订单", () => {
        this.save("draft");
      });
    },

    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    async submitEmits(data, _type = "add") {
      let type = _type;

      let list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }
      if (
        !["approved", "ckApproved", "rkApproved"].includes(type) &&
        _type !== "draft"
      ) {
        if (this.$refs.lineTable.checkList()) {
          return;
        }
      }

      if (["rkApproved"].includes(type)) {
        data.lgort = data.UMLGO;
      }

      if (["approved", "ckApproved"].includes(type)) {
        const lineTable2 = this.$refs.lineTable2.newlineList;
        list = shenpiChildren(lineTable2, type, this.detail);
      }

      if (this.loading) return;
      this.loading = true;

      try {
        // 出库校验
        if (type === "ckApproved" && this.detail?.bdFlag === "N") {
          const resData = await queryScanBarInfo({
            docNo: this.detail?.orderNo,
          });
          let obj = {};
          if (resData.code === 200 && resData.rows && resData.rows.length) {
            obj = resData.rows[0];
          }

          if (obj?.checkFlag === "Y" && obj.allScan === "N") {
            this.$modal.msgError("该订单号未全部扫码，不可出库");
            this.loading = false;
            return;
          }
        }
      } catch (error) {
        console.log(error, "error");
      }

      if (_type === "draft") {
        type = "upd";
      }

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];

      let fun = addInventoryManage;
      let posnr = 0;
      const _list = [...list].map((item) => {
        item.zsasOrderLineId = item.zsasOrderHeaderId
          ? item.zsasOrderLineId
          : undefined;
        posnr = item.posnr * 1 || (posnr + 10) * 1;
        let KWMENG_SQ = ["upd", "add"].includes(type)
          ? item.KWMENG
          : item?.KWMENG_SQ;

        if (_type === "draft") {
          KWMENG_SQ = 0;
          item.KWMENG_SP = 0;
        }

        item.posnr = posnr;
        item.KWMENG_SQ = KWMENG_SQ;
        return item;
      });

      const params = {
        action: type === "add" ? "add" : "submit",
        type: type === "add" ? "" : type,
        zsasOrderHeader: {
          ...this.detail,
          ...data,
          docStatus: _type === "draft" ? "draft" : undefined,
          zsasOrderLineList:
            type === "add"
              ? _list
              : [..._list, ...delLineIdList].filter(
                  ({ zsasOrderHeaderId = "" }) => zsasOrderHeaderId
                ),
          addZsasOrderLineList:
            type === "add"
              ? []
              : _list.filter(
                  ({ zsasOrderHeaderId = "" }) => !zsasOrderHeaderId
                ),
        },
      };

      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            let text = res?.msg || res?.data?.msg || "修改成功";
            if (_type === "draft") {
              text = "回退成功";
            }
            this.$modal.msgSuccess(text);
            if (!this.detail.zsasOrderHeaderId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "AllotDetail" })
                .then(() => {
                  this.$router.replace(
                    `/inventory/allot/allotApply-detail/index/edit/${res.data.zsasOrderHeaderId}`
                  );
                });
            } else {
              this.getDetail();
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    addSearch(type, item) {
      if (type !== "werks") return {};
      const params = {
        werks: item.werksName,
        "werks,werksName": [item.werks, item.werksName],
        unwrk: item.werksName,
        "unwrk,werksName": [item.werks, item.werksName],
      };
      return params;
    },
    closesearch() {
      if (type !== "werks") return {};
      return {
        werks: undefined,
        "werks,werksName": undefined,
        unwrk: undefined,
        "unwrk,werksName": undefined,
      };
    },
    getSearchcallBack(params, activeProps) {
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "UMLGO" && params[activeProps.prop]) {
        const lineList = [...this.$refs.lineTable2.salesOrderLineList];
        if (lineList && lineList.length) {
          const list = lineList.map((item) => {
            let [key = "", value = ""] = params["UMLGO,lgortName"];
            let label = `${value}`;
            if (!key) {
              return item;
            }
            return {
              ...item,
              UMLGO: params.UMLGO,
              "UMLGO,lgortName": params["UMLGO,lgortName"],
            };
          });
          this.$refs.lineTable2.salesOrderLineList = list;
        }
      }
    },

    checksearchMsg(item, dataInfo) {
      if (["werks", "unwrk"].includes(item.prop)) {
        if (!dataInfo.companyCode) {
          this.$modal.msgError("请先选择公司");
          return true;
        }
        return false;
      }
      return false;
    },
    addSearch(type, data) {
      if (type === "BUKRS") {
        return {
          companyCode: data.companyName || "",
          "companyCode,companyName": [
            data.company || "",
            data.companyName || "",
          ],
          werks: undefined,
          "werks,werksName": undefined,
          unwrk: undefined,
          "unwrk,werksName": undefined,
        };
      } else {
        return {};
      }
    },
    checkSelectMsg(row, self) {
      if (checkzmzbFlag(row, "调拨", this)) return true;

      const dataInfo = self?.$parent?.dataInfo || self.dataInfo;
      const prop = self?.$parent?.activeProps?.prop || self?.activeProps?.prop;
      if (dataInfo && ["werks", "unwrk"].includes(prop)) {
        const werks = dataInfo?.werks || "";
        const unwrk = dataInfo?.unwrk || "";

        let werksKey = prop === "werks";
        let unwrkKey = prop === "unwrk";
        const newLgort = row.werksName;

        if (werks && unwrkKey && werks === newLgort) {
          this.$modal.msgError("发货工厂与收货工厂不能相同，请重新选择");
          return true;
        } else if (unwrk && werksKey && unwrk === newLgort) {
          this.$modal.msgError("发货工厂与收货工厂不能相同，请重新选择");
          return true;
        } else {
          return false;
        }
      }

      return false;
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
