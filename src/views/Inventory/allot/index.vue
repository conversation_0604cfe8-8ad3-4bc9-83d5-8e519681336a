<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :isSelection="true"
      :selectable="selectable"
      @selection-change="handleSelectionChange"
    >
      <template #searchButton>
        <el-button
          v-if="ruku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="getPrint"
          v-hasPermi="['Allot:add']"
          :disabled="selectList.length === 0"
          >打印</el-button
        >
        <el-button
          v-if="!ruku"
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd('add', {})"
          v-hasPermi="['Allot:add']"
          >新增</el-button
        >
      </template>

      <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>

      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            v-hasPermi="['Allot:edit']"
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['Allot:detail']"
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
    <Inventoryprint ref="Inventoryprint"/>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import { listInventoryManage } from "@/api/inventory/inventoryManage";
import Inventoryprint from "../components/Inventoryprint.vue";
import { searchData, columns, optionsCodeList, getSearchData } from "./config";
export default {
  name: "Allot",
  dicts: ["inventory_ordertype", "sas_spzt", "meins", "ck_flag", "bd_flag"],
  dictsKey: ["VKORG", "LGORT"],
  components: {
    ComTableList,
    Inventoryprint,
  },
  data() {
    return {
      searchData: [],
      columns,
      optionsCodeList,
      ruku: "",
      selectList:[]
    };
  },
  created() {
    let ruku = "";
    if (location.href.indexOf("allotStore") > -1) {
      ruku = "ruku";
    }
    if (location.href.indexOf("allotOut") > -1) {
      ruku = "chuku";
    }
    if (location.href.indexOf("allotApproved") > -1) {
      ruku = "shenpi";
    }

    this.searchData = getSearchData(ruku);

    this.ruku = ruku;
  },
  methods: {
    selectable(row) {
      return ["rkApproved", "approved"].includes(row.docStatus);
    },
    handleSelectionChange(val) {
      this.selectList = val;
    },
    getPrint() {
      this.$refs.Inventoryprint.printInfo({
        ids: "278",
        type: "allot",
        title: "调拨单入库",
      });
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {
      // console.log(params)
      if (!params?.docStatusList || params.docStatusList.length === 0) {
        if (this.ruku === "ruku") {
          params.docStatusList = ["E", "rkApproved", "approved"];
        } else if (this.ruku === "chuku") {
          params.docStatusList = ["E", "ckApproved", "approved"];
        } else if (this.ruku === "shenpi") {
          params.docStatusList = ["approving"];
        } else {
          params.docStatusList = [
            "E",
            "draft",
            "cancel",
            "approving",
            "ckApproved",
            "rkApproved",
          ];
        }
      }

      return listInventoryManage({
        ...params,
        orderType: "DB",
      });
    },
    gotoAdd(type, data) {
      if (location.href.indexOf("allotStore") > -1) {
        this.$store
          .dispatch("tagsView/delPage", { name: "AllotStoreDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/store/allotStore-detail/index/${type}/${data.zsasOrderHeaderId}?ruku=ruku`
            );
          });
      } else if (location.href.indexOf("allotOut") > -1) {
        this.$store
          .dispatch("tagsView/delPage", { name: "AllotOutDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/outbound/allotOut-detail/index/${type}/${data.zsasOrderHeaderId}?ruku=chuku`
            );
          });
      } else if (location.href.indexOf("allotApproved") > -1) {
        this.$store
          .dispatch("tagsView/delPage", { name: "AllotApprovedDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/allot/allotApproved-detail/index/${type}/${data.zsasOrderHeaderId}?ruku=shenpi`
            );
          });
      } else {
        this.$store
          .dispatch("tagsView/delPage", { name: "AllotDetail" })
          .then(() => {
            this.$router.push(
              `/inventory/allot/allotApply-detail/index/${type}/${data.zsasOrderHeaderId}`
            );
          });
      }
    },
  },
};
</script>
