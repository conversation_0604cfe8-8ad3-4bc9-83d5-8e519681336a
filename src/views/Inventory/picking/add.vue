<template>
  <div class="add" v-loading="loading">
    <div class="movingContent" @click="closeEdit">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="6"
        labelWidth="140px"
        :showBtn="false"
        :rules="rules"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        :getSearchcallBack="getSearchcallBack"
        :setInput="setInput"
        :checkSelectMsg="checkSelectMsg"
        @submitEmits="submitEmits"
      >
      <template #searchButton v-if="pageType !== 'look'">
          <el-button
            v-if="[''].includes(detail.docStatus)"
            type="primary"
            size="mini"
            @click="save('add')"
            v-hasPermi="['picking:save']"
            >保存</el-button
          >
          <el-button
            type="primary"
            size="mini"
            v-if="['draft'].includes(detail.docStatus)"
            v-hasPermi="['picking:save']"
            @click="save('upd')"
            >修改</el-button
          >
          <el-button
            v-if="['draft'].includes(detail.docStatus)"
            v-hasPermi="['picking:submit']"
            type="primary"
            size="mini"
            @click="save('submit')"
            >提交</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="save('ckApproved')"
            v-hasPermi="['Inventory:inventoryManage:ckApproved']"
            v-if="['ckApproved'].includes(detail.docStatus)"
            >出库</el-button
          >
          <el-button
            type="primary"
            size="mini"
            @click="save('')"
            v-hasPermi="['Inventory:inventoryManage:ckApproved']"
            v-if="['ckApproved'].includes(detail.docStatus)"
            >取消</el-button
          >
        </template>
    </ComSearch>
    </div>
    <LineTable
      ref="lineTable"
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      keyId="zsasOrderLineId"
      POSNRKey="posnr"
      :requiredKeys="requiredKeys"
      :dataProps="dataProps"
      :checkUploadOther="checkUploadOther"
      :checkUploadExcel="setLineExport"

    >
      <template #lineTableHeader>
        <el-button type="primary" size="mini" @click="seachInventory()"
          >查询库存</el-button
        >
      </template>
    </LineTable>
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import { setTableDictValue } from "@/utils/index.js";
import {
  columns,
  lineCloumn,
  btnProps,
  searchData2,
  lookBtnProps,
  shenPiNumber,
  rules,
  getLineCloumn,
  getLineList,
} from "./config";
import {
  getInventoryManage,
  addInventoryManage,
} from "@/api/inventory/inventoryManage";
import { queryInventory } from "@/api/inventory/stock";
import { checkzmzbFlag } from '../moving/config'
import { template } from "lodash";
export default {
  name: "PickingDetail",
  dicts: ["inventory_ordertype", "sas_spzt", "bd_flag", "MEINS", "bwart"],
  dictsKey: ["VKORG", "LGORT",'KOSTL'],
  components: {
    ComSearch,
    LineTable,
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      btnProps,
      loading: false,
      searchInfo: {
        orderType: "LL",
        sysUsers: "", // this.$store.state.user.id,
      },
      rules: rules,
      searchData: columns,
      pageType,
      lineCloumn,
      lineList: [],
      detail: {
        docStatus: "",
      },
      dataProps: {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
        },
      },
      requiredKeys: ["ZZMATNR", "KWMENG", "LGORT", "KOSTL"],
    };
  },

  created() {
    this.getDetail();
    // console.log(this.dataInfo,'00000')
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },

    columnsLine() {
      return;
    },
    // dataInfo(){
    //   console.log(this.$refs?.ComSearchInfo,'this.$refs?.ComSearchInfo')
    //   return this.$refs?.ComSearchInfo?.dataInfo
    // }
  },
  watch: {
    // dataInfo(val){
    //   console.log(val,'val')
    // }
  },
  methods: {
    seachInventory(){
      const list = this.$refs.lineTable.newlineList
      if(list && list.length === 0){
         this.$modal.msgError("暂无数据查询");
        return
      }
      this.$confirm("是否确认查询库存?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(()=>{
            const werks = this.$refs.ComSearchInfo?.changeInfoData()?.werks
            this.queryInventory(werks,list)
        })

    },
    closeEdit() {
      // console.log(this.$refs.lineTable,'11111')
      this.$refs.lineTable.closeEdit();
    },
    getDetail() {
      if (this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getInventoryManage(this.$route.params.id)
        .then((res) => {
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            const zsasOrderLineList = getLineList(
              res.data.zsasOrderLineList || []
            );
            this.searchInfo = {
              ...res.data,
              bwart: zsasOrderLineList[0]?.bwart || undefined,
            };
            if (this.detail.docStatus !== "draft" && this.detail.docStatus) {
              this.searchData = searchData2;
              this.btnProps = lookBtnProps;
            }

            const cloumns = getLineCloumn(this.detail.docStatus);
            this.lineCloumn = cloumns;

            if (["draft"].includes(this.detail.docStatus)) {
              this.queryInventory(this.detail.werks, zsasOrderLineList);
            } else {
              const _rows2 = setTableDictValue(
                this.lineCloumn,
                zsasOrderLineList,
                this.dictDataOptions
              );
              this.lineList = _rows2;
              this.loading = false;
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    async queryInventory(WERKS, zsasOrderLineList) {
      try {
        const params = zsasOrderLineList.map((item) => {
          return {
            WERKS,
            ZZMATNR: item.ZZMATNR,
            LGORT: item.LGORT,
          };
        });
        const res = await queryInventory({
          action: "SDI035",
          inventoryInfos: params,
        });

        if (res.code === 200 && res.rows) {
          const _rows = shenPiNumber(
            [...zsasOrderLineList],
            [...res.rows],
            this.detail.docStatus
          );

          const _rows2 = setTableDictValue(
            this.lineCloumn,
            _rows,
            this.dictDataOptions
          );
          this.lineList = _rows2;
          this.loading = false;
        }
      } catch (error) {
        console.log(error);

          const _rows2 = setTableDictValue(
            this.lineCloumn,
            zsasOrderLineList,
            this.dictDataOptions
          );
          this.lineList = _rows2;

        this.loading = false;
      }
    },

    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    submitEmits(data, type = "add") {
      const list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }
      if (this.$refs.lineTable.checkList()) return;

      if (this.loading) return;
      this.loading = true;

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];
      // console.log(delLineIdList, "delLineIdList");

      let fun = addInventoryManage;
      let posnr = 0;
      const _list = [...list].map((item) => {
        item.zsasOrderLineId = item.zsasOrderHeaderId
          ? item.zsasOrderLineId
          : undefined;
        posnr = item.posnr * 1 || (posnr + 10) * 1;
        return {
          ...item,
          posnr: posnr,
          KWMENG_SQ:item.KWMENG,
          KWMENG_SP: type === "submit" ? item.KWMENG : item.KWMENG_SP,
        };
      });


      // console.log(_list,'llooooo')


      // return


      const params = {
        action: type === "add" ? "add" : "submit",
        type: type === "add" ? "" : type,
        zsasOrderHeader: {
          ...this.detail,
          ...data,
          docStatus: type === '' ? 'cancel' : this.detail.docStatus,
          zsasOrderLineList:
            type === "add"
              ? _list
              : [..._list, ...delLineIdList].filter(
                  ({ zsasOrderHeaderId = "" }) => zsasOrderHeaderId
                ),
          addZsasOrderLineList:
            type === "add"
              ? []
              : _list.filter(
                  ({ zsasOrderHeaderId = "" }) => !zsasOrderHeaderId
                ),
        },
      };

      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
              this.$modal.msgSuccess(res?.msg || res?.data?.msg || "修改成功");
            if (!this.detail.zsasOrderHeaderId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "PickingDetail" })
                .then(() => {
                  this.$router.replace(
                    `/inventory/picking-detail/index/edit/${res.data.zsasOrderHeaderId}`
                  );
                });
            } else {
              this.getDetail();
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
    getSearchcallBack(params, activeProps) {
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "LGORT" && params[activeProps.prop]) {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            LGORT: params.LGORT,
            "LGORT,lgortName": params["LGORT,lgortName"],
          },
        };
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const lineList2 = [...this.$refs.lineTable.salesOrderLineList].filter(
          ({ LGORT }) => !LGORT
        );

        if (lineList && lineList.length && lineList2.length) {
          const list = lineList.map((item) => {
            return {
              ...item,
              LGORT: item.LGORT ? item.LGORT : params.LGORT,
              "LGORT,lgortName": item.LGORT
                ? item["LGORT,lgortName"]
                : params["LGORT,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }

      // 成本中心号

      if (activeProps.prop === "KOSTL" && params[activeProps.prop]) {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            KOSTL: params.KOSTL,
            "KOSTL,ktext": params["KOSTL,ktext"],
          },
        };
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const lineList2 = [...this.$refs.lineTable.salesOrderLineList].filter(
          ({ KOSTL }) => !KOSTL
        );

        if (lineList && lineList.length && lineList2.length) {
          const list = lineList.map((item) => {
            return {
              ...item,
              KOSTL: item.KOSTL ? item.KOSTL : params.KOSTL,
              "KOSTL,ktext": item.LGORT
                ? item["KOSTL,ktext"]
                : params["KOSTL,ktext"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
    setInput(val, activeProps, self) {
      if (activeProps.prop !== "bwart") {
        return {};
      }

      let colomns = getLineCloumn("", { bwart: val });
      this.lineCloumn = colomns;
      // ZME和ZMF的时候，客户必填，成本中心不填
      // 根据移动类型回显
      this.dataProps = {
        ...this.dataProps,
        initLineData: {
          ...this.dataProps.initLineData,
          bwart: val,

        },
      };

      if (activeProps.prop === "bwart" && ["ZME", "ZMF"].includes(val)) {
        this.requiredKeys = [...this.requiredKeys, "KUNNR"].filter(
          (key) => key !== "KOSTL"
        );

        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const list = lineList.map((item) => {
          return {
            ...item,
            bwart: val,
            KOSTL: "",
            "KOSTL,ktext": ["", ""],
          };
        });
        this.$refs.lineTable.salesOrderLineList = list;

        return {
          KOSTL: undefined,
        };
      } else {
        // 其他ZM移动类型，成本中心必填,客户不填
        this.requiredKeys = [...this.requiredKeys, "KOSTL"].filter(
          (key) => key !== "KUNNR"
        );

        const lineList = [...this.$refs.lineTable.salesOrderLineList];

        // if (lineList) {
        const list = lineList.map((item) => {
          return {
            ...item,
            bwart: val,
            KUNNR: "",
            "KUNNR,NAME1": ["", ""],
          };
        });
        this.$refs.lineTable.salesOrderLineList = list;
        // }
      }
    },

    setLineExport(dataList,self){
       const parantdataInfo = self.parantdataInfo

       const val = parantdataInfo.bwart
      //  console.log(val,dataList,'lllll')
      if (["ZME", "ZMF"].includes(val)) {
        const lineList = [...dataList];
        const list = lineList.map((item) => {
          return {
            ...item,
            bwart: val,
            KOSTL: "",
            "KOSTL,ktext": ["", ""],
          };
        });
       return list
      } else {
        const lineList = [...dataList];
        const list = lineList.map((item) => {
          return {
            ...item,
            bwart: val,
            KUNNR: "",
            "KUNNR,NAME1": ["", ""],
          };
        });
        return list
      }
    },
    checkSelectMsg(row, self) {
       return checkzmzbFlag(row,'领料',this)
    },
    checkUploadOther(info,self){
      if(!info?.bwart){
          this.$modal.msgError("请先选择移动类型");
        return true
      }
      return false

    }
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
