export const searchData = [
  {
    label: "单据编号",
    type: "input",
    prop: "orderNo",
  },

  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },
  {
    label: "工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },
];

export const columns = [
  {
    label: "单据编号",
    type: "input",
    prop: "orderNo",
    attr: {
      disabled: true,
    },
  },

  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },
  {
    label: "工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },

  {
    prop: "LGORT",
    label: "库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
      // disabled: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  {
    label: "移动类型",
    type: "select",
    prop: "bwart",
    optionsCode: "bwart",
    otherBind: (item) => {
      return {
        ...item,
        disabled: ["702", "701"].includes(item.dictValue),
      };
    },
  },

  {
    label: "成本中心",
    type: "input",
    prop: "KOSTL",
    append: true,
    dictOptionCode:'KOSTL',
    attr: {},
    backProps: [
      {
        backProp: "kostl",
        backLable: "ktext",
        prop: "KOSTL",
      },
    ],
  },

  {
    label: "单据类型",
    type: "input",
    prop: "orderType",
    type: "select",
    optionsCode: "inventory_ordertype",
    slotName: "orderType",
    attr: {
      disabled: true,
    },
  },

  {
    label: "申请人",
    type: "input",
    prop: "createBy",
    attr: {
      disabled: true,
    },
  },

  {
    label: "补单标识",
    type: "input",
    prop: "SAS_BD_FALG",
  },

  {
    label: "备注",
    type: "input",
    prop: "SAS_FYTXT",
    searchOut: true,
  },

  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "sas_spzt",
    slotName: "docStatus",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
];

// 映射code
export const optionsCodeList = columns.filter(({ optionsCode }) => optionsCode);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lineCloumn = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 200,
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "ZZMATNR",
        backLable: "ZZMATNR",
        prop: "ZZMATNR",
      },
      {
        backProp: "maktxZh",
        backLable: "maktxZh",
        prop: "MAKTX",
      },
      {
        backProp: "MEINS",
        backLable: "MSEHT",
        prop: "MEINS",
      },
    ],
  },

  {
    prop: "MAKTX",
    label: "物料描述",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "KWMENG",
    label: "数量",
    type: "number",
    width: 140,
  },
  {
    prop: "LGORT",
    label: "库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },
  
  {
    label: "移动类型",
    type: "select",
    prop: "bwart",
    optionsCode: "bwart",
    width:220,
    attr:{
      disabled:true
    },
    otherBind: (item) => {
      return {
        ...item,
        disabled: ["702", "701"].includes(item.dictValue),
      };
    },
  },

  {
    label: "成本中心",
    type: "input",
    prop: "KOSTL",
    width: 200,
    append: true,
    dictOptionCode:'KOSTL',
    attr: {},
    backProps: [
    {
      backProp: "kostl",
      backLable: "ktext",
      prop: "KOSTL",
    },
  ],

  },

  {
    label: "客户",
    type: "input",
    prop: "KUNNR",
    width: 300,
    append: true,
    attr: {
      disabled:true
    },
    backProps: [
      {
        backProp: "KUNNR",
        backLable: "NAME1",
        prop: "KUNNR",
      },
    ],
  },

  {
    prop: "KWMENG_SQ",
    label: "申请数量",
    width: 100,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_SQ3",
    label: "可用库存数量",
    width: 100,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_SP",
    label: "审批数量",
    width: 100,
    type: "number",
    attr: {
      disabled: true,
    },
  },

  // {
  //   prop: "KWMENG_SD",
  //   label: "锁定数量",
  //   width: 100,
  //   attr: {
  //     disabled: true,
  //   },
  //   // width: 140,
  // },

  {
    prop: "KWMENG_CK",
    label: "出库数量",
    width: 100,
    type: "number",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "SGTXT",
    label: "备注",
    width: 200,
    // width:140
  },
  {
    prop: "MEINS",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
    optionsCode: "meins",
  },
];

export const searchData2 = JSON.parse(JSON.stringify(columns)).map((item) => {
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

export const shenPiNumber = (zsasOrderLineList, childList, docStatus) => {
  const listT = [...zsasOrderLineList].map((item, index) => {
    const total = childList
      .filter(
        ({ ZZMATNR = "", LGORT }) =>
          item.ZZMATNR === ZZMATNR && item.LGORT === LGORT
      )
      .map(({ LABST }) => LABST)
      .reduce(
        (accumulator, currentValue) => accumulator * 1 + currentValue * 1,
        0
      );

    const obj = {
      ...item,
      KWMENG_SQ3: total,
      id: docStatus === "draft" ? `${item.zsasOrderLineId}${index}` : undefined,
      docStatus,
    };

    return {
      ...obj,
    };
  });

  return listT;
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["picking:lineAdd"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["picking:lineDel"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["picking:lineDel"],
  },
  uploadExcel:{
    isShow: true,
    hasPermi: ["picking:line:uploadExcel"],
  }
};

const _btnProps = {};
Object.keys(btnProps).forEach((key) => {
  _btnProps[key] = {
    ...btnProps[key],
    isShow: false,
  };
});

export const lookBtnProps = _btnProps;

export const rules = {
  companyCode: [
    { required: true, message: "请选择选择公司", trigger: "change" },
  ],
  werks: [{ required: true, message: "请选择发货工厂", trigger: "change" }],
  bwart: [{ required: true, message: "请选择移动", trigger: "change" }],
};


export const getLineCloumn =(type,dataInfo)=>{

  let _lineCloumn =  JSON.parse(JSON.stringify(lineCloumn)).map((item)=>{
    if(["ZME", "ZMF"].includes(dataInfo?.bwart) && item.prop === 'KOSTL'){
      item.attr = {
        disabled:true
      }
    }else if(!["ZME", "ZMF"].includes(dataInfo?.bwart) && item.prop === 'KOSTL'){
      item.attr = {
        disabled:false
      }
    }

    if(["ZME", "ZMF"].includes(dataInfo?.bwart) && item.prop === 'KUNNR'){
      item.attr = {
        disabled:false
      }
    }else if(!["ZME", "ZMF"].includes(dataInfo?.bwart) && item.prop === 'KUNNR'){
      item.attr = {
        disabled:true
      }
    }

    if(['ckApproved','cancel','approved'].includes(type)){
       item.attr = {
        disabled:true
      }
    }
    
 
    return item
  })

  return _lineCloumn

}


export const getLineList = (list)=>{

  return list.map((item)=>{
    return {
     ...item,
     'KUNNR':item.NAME1,
     'KUNNR,NAME1':[item.KUNNR,item.NAME1]
    }
  })
}
