<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
    >
     <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd('add',{})"
           v-hasPermi="['picking:add']"
          >新增</el-button
        >
      </template>
       <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>
       <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
             v-hasPermi="['picking:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
             v-hasPermi="['picking:detail']"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import { searchData,columns, optionsCodeList } from "./config";
import { listInventoryManage } from "@/api/inventory/inventoryManage";

export default {
  name: "Picking",
  components: {
    ComTableList,
  },
  dicts: ["inventory_ordertype", "sas_spzt", "meins",'ck_flag','bwart'],
  dictsKey: ["VKORG", "LGORT",'KOSTL'],
  data() {
    return {
      searchData,
      columns:columns.filter((item)=>(!['LGORT','KOSTL','bwart'].includes(item.prop))),
      optionsCodeList
    };
  },
  created() {
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
     
      return listInventoryManage({
        ...params,
         orderType: "LL",
      });
     
    },
    gotoAdd(type,data){
      this.$store.dispatch("tagsView/delPage", { name: "PickingDetail" });
      this.$router.push(`/inventory/picking-detail/index/${type}/${data.zsasOrderHeaderId}`);
    }
   
  },
};
</script>
