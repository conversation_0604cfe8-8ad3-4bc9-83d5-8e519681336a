<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
    >
     <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          v-hasPermi="['Check:add']"
          @click="gotoAdd('add',{})"
          >新增</el-button
        >
      </template>
       <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>
       <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            v-hasPermi="['Check:edit']"
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['Check:detail']"
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";

import { listInventoryManage } from "@/api/inventory/inventoryManage";

import { searchData,columns, optionsCodeList } from "./config";

export default {
  name: "Check",
  components: {
    ComTableList,
  },
  dicts:['inventory_ordertype','bd_flag','sas_spzt'],
  dictsKey:['VKORG','LGORT'],
  data() {
    return {
      searchData,
      columns:columns.filter(({prop})=>(prop !== 'LGORT')),
      optionsCodeList
    };
  },
  created() {
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
    
      return listInventoryManage({
        ...params,
         orderType: "PD",
      });
     
    },
    gotoAdd(type,data){
      this.$store.dispatch("tagsView/delPage", { name: "CheckDetail" });
      this.$router.push(`/inventory/check-detail/index/${type}/${data.zsasOrderHeaderId}`);
    }
   
  },
};
</script>
