export const searchData = [
  {
    label: "申请人",
    type: "input",
    prop: "sysUsers",
  },
  {
    label: "订单号",
    type: "input",
    prop: "orderNo",
  },
  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },
  {
    label: "发货工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },
];

export const columns = [
  {
    label: "单据类型",
    type: "input",
    prop: "orderType",
    type: "select",
    optionsCode: "inventory_ordertype",
    slotName: "orderType",
    attr: {
      disabled: true,
    },
  },
  {
    label: "订单号",
    type: "input",
    prop: "orderNo",
    attr: {
      disabled: true,
    },
  },
  {
    label: "公司",
    type: "input",
    prop: "companyCode",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },
  {
    label: "发货工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "companyCode",
      },
    ],
  },
  {
    prop: "LGORT",
    label: "库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
      // disabled: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },
  {
    label: "是否补单",
    type: "select",
    prop: "bdFlag",
    optionsCode: "bd_flag",
    slotName: "bdFlag",
  },

  {
    label: "备注",
    type: "input",
    prop: "fytxt",
    searchOut: true,
  },
  {
    label: "申请人",
    type: "input",
    prop: "createBy",
    append: true,
    attr: {
      disabled: true,
    },
  },

  {
    label: "单据状态",
    type: "select",
    prop: "docStatus",
    optionsCode: "sas_spzt",
    slotName: "docStatus",
    fixed: "right",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.width = item.width || "140px";
  return item;
});

export const searchData2 = JSON.parse(JSON.stringify(columns)).map((item) => {
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

// 映射code
export const optionsCodeList = columns.filter(({ optionsCode }) => optionsCode);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lineCloumn = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
    append: true,
    attr: {
      // readonly: true,
    },
  },
  {
    prop: "MAKTX",
    label: "物料描述",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "LGORT",
    label: "库存地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  {
    prop: "KWMENG",
    label: "实时库存数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  // {
  //   prop: "KWMENG_SQ",
  //   label: "盘点前数量",
  //   width: 200,
  //   type: "number",
  //   attr: {
  //     disabled: true,
  //   },
  //   // width: 140,
  // },

  {
    prop: "KWMENG_SP",
    label: "实盘数量",
    label2:'数量',
    type: "number",
    width: 200,
    attr: {
      // disabled: true,
    },
    // width: 140,
  },

  {
    prop: "KWMENG_SD",
    label: "差异数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "MEINS",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
    optionsCode: "meins",
    // width:140
  },

  {
    prop: "SGTXT",
    label: "备注",
    width: 200,
    // width:140
  },
];

const changeLineItem = (val, item, editItem, self) => {
  if (["KWMENG_SQ", "KWMENG_SP"].includes(item.prop)) {
    // console.log(editItem,'editItem')
    editItem.KWMENG_SD = editItem.KWMENG_SP - (editItem?.KWMENG || 0);
    // console.log(editItem.KWMENG_SD,'editItem.KWMENG_SD')
  }
  return val;
};

const otherBind = (item, row) => {
  if (row.zsasOrderHeaderId) {
    return {
      disabled: true,
    };
  }

  return {};
};

export const getLineColumns = (docStatus = "") => {
  const list = JSON.parse(JSON.stringify(lineCloumn)).map((item) => {
    if (docStatus && ["ZZMATNR", "LGORT"].includes(item.prop)) {
      item.otherBind = otherBind;
    }
    if (
      docStatus === "draft" &&
      ["KWMENG_SQ", "KWMENG_SP"].includes(item.prop)
    ) {
      item.attr = {
        disabled: false,
      };
    }

    if (["KWMENG_SQ", "KWMENG_SP"].includes(item.prop)) {
      item.changeLineItem = changeLineItem;
    }

    if(['cancel','approved','gzApproved'].includes(docStatus)){
      item.attr = {
        disabled:true
      }
    }

    return item;
  });

  return list;
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["Check:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["Check:linde:del"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["Check:linde:del"],
  },
   uploadExcel:{
    isShow: true,
    hasPermi: ['Check:line:uploadExcel'],
  }
};

const _btnProps = {};
Object.keys(btnProps).forEach((key) => {
  _btnProps[key] = {
    ...btnProps[key],
    isShow: false,
  };
});

export const lookBtnProps = _btnProps;

export const shenPiNumber = (zsasOrderLineList, childList, docStatus) => {
  const listT = [...zsasOrderLineList].map((item, index) => {
    const total =
      childList
        .filter(
          ({ ZZMATNR = "", LGORT }) =>
            item.ZZMATNR === ZZMATNR && item.LGORT === LGORT
        )
        .map(({ LABST }) => LABST)
        .reduce(
          (accumulator, currentValue) => accumulator * 1 + currentValue * 1,
          0
        ) || 0;

    const obj = {
      ...item,
      KWMENG: total,
      // KWMENG_SQ: item.KWMENG_SQ,
      KWMENG_SP: item.KWMENG_SP,
      KWMENG_SD: ((item.KWMENG_SP * 1) - (total * 1)) || 0,
      id: docStatus === "draft" ? `${item.zsasOrderLineId}${index}` : undefined,
      docStatus,
    };

    return {
      ...obj,
    };
  });

  return listT;
};
