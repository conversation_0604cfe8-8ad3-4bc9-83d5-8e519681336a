<template>
  <div class="add" v-loading="loading">
    <div class="movingContent" @click="closeEdit">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="6"
        labelWidth="140px"
        :showBtn="false"
        :rules="rules"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        :getSearchcallBack="getSearchcallBack"
        :checkSelectMsg="checkSelectMsg"
        @submitEmits="submitEmits"
      >
        <template #searchButton v-if="pageType !== 'look'">
          <el-button
            v-if="[''].includes(detail.docStatus)"
            type="primary"
            size="mini"
            @click="save('add')"
            v-hasPermi="['Check:save']"
            >保存</el-button
          >
          <el-button
            type="primary"
            size="mini"
            v-if="['draft'].includes(detail.docStatus)"
            v-hasPermi="['Check:save']"
            @click="save('upd')"
            >修改</el-button
          >
          <el-button
            v-if="['draft'].includes(detail.docStatus)"
            v-hasPermi="['Check:submit']"
            type="primary"
            size="mini"
            @click="save('submit')"
            >提交</el-button
          >
          <el-button
            v-if="['gzApproved'].includes(detail.docStatus)"
            v-hasPermi="['Check:gzApproved']"
            type="primary"
            size="mini"
            @click="save('submit')"
            >过账</el-button
          >
          <el-button
            v-if="['ckApproved'].includes(detail.docStatus)"
            v-hasPermi="['Check:cancel']"
            type="primary"
            size="mini"
            @click="save('')"
            >取消</el-button
          >
        </template>
      </ComSearch>
    </div>
    <LineTable
      ref="lineTable"
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      keyId="zsasOrderLineId"
      POSNRKey="posnr"
      :dataProps="dataProps"
      :requiredKeys="requiredKeys"
    >
      <template #lineTableHeader>
        <el-button type="primary" size="mini" @click="seachInventory()"
          >查询库存</el-button
        >
      </template>
    </LineTable>
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import { setTableDictValue } from "@/utils/index.js";
import { checkzmzbFlag } from "../moving/config";
import {
  columns,
  btnProps,
  searchData2,
  lookBtnProps,
  shenPiNumber,
  getLineColumns,
} from "./config";
import {
  getInventoryManage,
  addInventoryManage,
} from "@/api/inventory/inventoryManage";
import { queryInventory } from "@/api/inventory/stock";
import { template } from "lodash";
export default {
  name: "CheckDetail",
  dicts: ["inventory_ordertype", "sas_spzt", "bd_flag", "MEINS"],
  dictsKey: ["VKORG", "LGORT", "ZZMATNR"],
  components: {
    ComSearch,
    LineTable,
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      requiredKeys: ["ZZMATNR", "LGORT"],
      btnProps,
      loading: false,
      searchInfo: {
        orderType: "PD",
        sysUsers: "", // this.$store.state.user.id,
        bdFlag: "N",
      },
      rules: {
        companyCode: [
          { required: true, message: "请选择选择公司", trigger: "change" },
        ],
        werks: [
          { required: true, message: "请选择发货工厂", trigger: "change" },
        ],
      },
      searchData: columns,
      pageType,
      // lineCloumn,
      lineList: [],
      detail: {
        docStatus: "",
      },
      dataProps: {
        initLineData: {
          delFlag: "0",
          // KWMENG: 1,
          ZZMATNR: undefined,
        },
      },
    };
  },

  created() {
    this.getDetail();
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
    lineCloumn() {
      return getLineColumns(this.detail.docStatus);
    },
  },
  watch: {},
  methods: {
    closeEdit() {
      // console.log(this.$refs.lineTable,'11111')
      this.$refs.lineTable.closeEdit();
    },
    getDetail() {
      if (this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getInventoryManage(this.$route.params.id)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.detail = JSON.parse(JSON.stringify(res.data));
            this.searchInfo = res.data;
            if (this.detail.docStatus !== "draft" && this.detail.docStatus) {
              this.searchData = searchData2;
              this.btnProps = lookBtnProps;
            }
            if (
              !["approved", "ckApproved", "rkApproved", "gzApproved"].includes(
                this.detail.docStatus
              )
            ) {
              this.queryInventory(this.detail.werks,this.detail.zsasOrderLineList);
            } else {
              const _rows2 = setTableDictValue(
                this.lineCloumn,
                res.data.zsasOrderLineList,
                this.dictDataOptions
              );
              this.lineList = _rows2;
              this.loading = false;
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    async queryInventory(WERKS,dataList) {
      try {
        if(this.loading)return
        this.loading = true
        const params = [...dataList].map((item) => {
          return {
            WERKS,
            ZZMATNR: item.ZZMATNR,
            LGORT: item.LGORT,
          };
        });
       
        const res = await queryInventory({
          action: "SDI035",
          inventoryInfos: params,
        })
        this.loading = false;
        // console.log(res,'res')

        if (res.code === 200 && res.rows) {
          const _rows = shenPiNumber(
            [...dataList],
            [...res.rows],
            this.detail.docStatus
          );

          const _rows2 = setTableDictValue(
            this.lineCloumn,
            _rows,
            this.dictDataOptions
          );
          this.lineList = _rows2;
          this.loading = false;
        }
         this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },

    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    submitEmits(data, type = "add") {
      const list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }
      if (this.$refs.lineTable.checkList()) return;

      if (this.loading) return;
      this.loading = true;

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];

      let fun = addInventoryManage;
      let posnr = 0;
      const _list = [...list].map((item) => {
        item.zsasOrderLineId = item.zsasOrderHeaderId
          ? item.zsasOrderLineId
          : undefined;
        posnr = item.posnr * 1 || (posnr + 10) * 1;
        return {
          ...item,
          posnr: posnr,
        };
      });

      const params = {
        action: type === "add" ? "add" : "submit",
        type: type === "add" ? "" : type,
        flag: this.detail.docStatus === "gzApproved",
        zsasOrderHeader: {
          ...this.detail,
          ...data,
          docStatus: type === "" ? "cancel" : undefined,
          zsasOrderLineList:
            type === "add"
              ? _list
              : [..._list, ...delLineIdList].filter(
                  ({ zsasOrderHeaderId = "" }) => zsasOrderHeaderId
                ),
          addZsasOrderLineList:
            type === "add"
              ? []
              : _list.filter(
                  ({ zsasOrderHeaderId = "" }) => !zsasOrderHeaderId
                ),
        },
      };

      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$modal.msgSuccess(res?.msg || res?.data?.msg || "修改成功");
            if (!this.detail.zsasOrderHeaderId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "CheckDetail" })
                .then(() => {
                  this.$router.replace(
                    `/inventory/check-detail/index/edit/${res.data.zsasOrderHeaderId}`
                  );
                });
            } else {
              this.getDetail();
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    getSearchcallBack(params, activeProps) {
      // console.log(params,activeProps)
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "LGORT" && params[activeProps.prop]) {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            LGORT: params.LGORT,
            "LGORT,lgortName": params["LGORT,lgortName"],
          },
        };
        // console.log(this.dataProps,'0000')
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const lineList2 = [...this.$refs.lineTable.salesOrderLineList].filter(
          ({ LGORT }) => !LGORT
        );

        if (lineList && lineList.length && lineList2.length) {
          const list = lineList.map((item) => {
            return {
              ...item,
              LGORT: item.LGORT ? item.LGORT : params.LGORT,
              "LGORT,lgortName": item.LGORT
                ? item["LGORT,lgortName"]
                : params["LGORT,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
    checkSelectMsg(row, self) {
      return checkzmzbFlag(row, "盘点", this);
    },
    getInventoryData(){
      const list = this.$refs.lineTable.newlineList
      // if(this.loading)return
      // this.loading = true
      const werks = this.$refs.ComSearchInfo?.changeInfoData()?.werks
      // console.log(werks,list,'werlk')
      this.queryInventory(werks,list)

    },
    seachInventory(){
      const list = this.$refs.lineTable.newlineList
      if(list && list.length === 0){
         this.$modal.msgError("暂无数据查询");
        return
      }
      this.$confirm("是否确认查询库存?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(()=>{

          this.getInventoryData()

        })

    }
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
