<template>
  <div>
    <Print :info="detail" :title="'⼴西⽟柴机器专卖发展有限公司销售提货单'"/>
  </div>
</template>

<script>
import Print from "@/components/Print/index";
import { printInfo } from "@/api/sales/salesDelivery/salesDelivery";
import { getData } from './config'
import { getDictKey, dictOptionCodeKey,param,param2Obj  } from "@/utils/index";
export default {
  name: "InventoryPrint",
  components: {
    Print,
  },
  dicts:['zterm'],
  dictsKey:['LGORT'],
  data() {
    return {
      loading: false,
      detail: [],
    };
  },
  created() {
  },
  mounted(){
   setTimeout(() => {
    this.printInfo()
   }, 200);
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  methods: {
    printInfo() {
      if (this.loading) return;
      let ids = param2Obj(location.href).ids
      if (!ids) return;
      if(ids.indexOf(',') > -1){
         ids = ids.split(',')
      }else{
         ids = [ids]
      }
      const getFun = ids.map((deliveryHeadId) => {
        return printInfo(deliveryHeadId);
      });
      Promise.all(getFun)
        .then((res) => {
          let data = getData(res,this.dictDataOptions)
           this.detail = data
           this.loading = false
        })
        .catch((error) => {
          console.log(error);
          this.loading = false
        });
    },
    
  },
 
};
</script>
