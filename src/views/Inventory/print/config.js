import { setTableDictValue } from "@/utils/index.js";
import { TableConfig } from "@/components/Print/config.js";
import { getPrintList } from '@/components/Print/config.js'

export const getData = (alist, options) => {
  let newList = [];
  alist.forEach((element) => {
    if (element.code === 200 && element.data) {
        // console.log(options,'options')
      let data = setTableDictValue(TableConfig, element.data.salesDeliveryLineList, options).map((item)=>{
        // item.shaoma = 'Y'
        return item
      });

      const obj = {
        ...element.data,
        tableList: data,
      };
      newList.push(obj);
    }
  });

 let list = newList.map((item)=>{

   return getPrintList(item)

  })
  

  return list;
};
