import {
  notesearchData,
  notecolumns,
  notelineCloumn,
} from "@/views/purchase/orderNote/config.js";

const lgortf = [
  {
    label: "入库子库",
    prop: "lgort",
    // hiddenSearch: true,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },
  {
    label: "入库备注",
    prop: "remark",
    searchOut: true,
    // hiddenSearch: true,
    attr: {},
  },
];
export const searchData = [...notesearchData].map((item) => {
  if (["docStatus"].includes(item.prop)) {
    item.otherBind = (item2) => {
      let dis = !["rkSuccess", "rkError", "S"].includes(item2.value);
      return {
        disabled: dis,
      };
    };
  }

  return item;
});

export const searchDataDetail = [...notesearchData]
export const searchData2 = JSON.parse(
  JSON.stringify([...notecolumns, ...lgortf])
).map((item) => {
    item.attr = {
      disabled:true
    }
  return {
    ...item,
  };
});

export const getSearchData = (docStatus,WERKS) => {
  const searchData2 = JSON.parse(
    JSON.stringify([...notecolumns, ...lgortf])
  ).map((item) => {
    if (
      ["lgort", "remark"].includes(item.prop) &&
      ["S", "rkError"].includes(docStatus)
    && (WERKS !== '6001')) {
      item.attr = {
        disabled: false,
      };
    } else {
      item.attr = {
        disabled:true
      };
    }
    return {
      ...item,
    };
  });

  return searchData2;
};

// console.log(notecolumns,'notecolumns')
export const columns = [...notecolumns];
export const keyList = columns.filter(({ optionsCode }) => optionsCode);
export const lineCloumn = JSON.parse(JSON.stringify(notelineCloumn)).map(
  (item) => {
    return {
      ...item,
      attr: {
        disabled: true,
      },
    };
  }
);

export const getlineColums = (docStatus) => {
  const lineCloumn = JSON.parse(JSON.stringify(notelineCloumn)).map((item) => {
    if (["S", "rkError"].includes(docStatus) && item.prop === "LGORT") {
      item.attr = {
        disabled: false,
      };
    } else {
      item.attr = {
        disabled: true,
      };
    }

    return {
      ...item,
    };
  });

  return lineCloumn;
};

export const btnProps = {
  add: {
    isShow: false,
    hasPermi: [],
  },
  batchDelete: {
    isShow: false,
    hasPermi: [],
  },
  deleteLine: {
    isShow: false,
    hasPermi: [],
  },
  caozuoShow: {
    isShow: false,
    hasPermi: [],
  },
};

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};
