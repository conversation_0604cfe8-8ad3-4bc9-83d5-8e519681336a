<template>
  <div class="add" v-loading="loading">
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="16"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      :getSearchcallBack="getSearchcallBack"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      @submitEmits="submitEmits"
    >
      <template #searchButton v-if="pageType !== 'look'">
        <el-button
          type="primary"
          size="mini"
          @click="getPrint"
          v-hasPermi="['Inventory:inventoryManage:rkApproved']"
          v-if="
            ['S', 'rkError', 'rkSuccess'].includes(detail.docStatus) &&
            detail.WERKS !== '6001'
          "
          >打印</el-button
        >
        <el-button
          v-hasPermi="['store:in']"
          v-if="
            ['S', 'rkError'].includes(detail.docStatus) &&
            detail.WERKS !== '6001'
          "
          type="primary"
          size="mini"
          @click="save('ruku')"
          >入库</el-button
        >
      </template>
    </ComSearch>
    <LineTable
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      keyId="purchaseLineId"
      :btnProps="btnProps"
      :showSelection="false"
      :requiredKeys="['LGORT']"
      ref="lineTable"
    ></LineTable>
    <Inventoryprint ref="Inventoryprint" />
  </div>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import Inventoryprint from "../../components/Inventoryprint.vue";

import {
  btnProps,
  lineCloumn,
  searchData2,
  getlineColums,
  getSearchData,
  searchDataDetail,
} from "./config";
import {
  inventoryAccountRu,
  getPurDelivery,
  addPurDelivery,
} from "@/api/purchase/purchaseDelivery/purDelivery";

import { dictOptionCodeKey, setTableDictValue } from "@/utils/index";
export default {
  name: "OrderNoteStoreDetail",
  components: {
    ComSearch,
    LineTable,
    Inventoryprint,
  },
  dicts: ["demand_send_status", "doct_status"],
  dictsKey: dictOptionCodeKey(lineCloumn),
  props: {},
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      searchInfo: {},
      rules: {},
      searchData: searchData2,
      pageType,
      lineCloumn: lineCloumn,
      btnProps,
      detail: {},
      lineList: [],
    };
  },

  created() {
    this.getDetail();
  },
  watch: {},
  methods: {
    getPrint() {
      this.$refs.Inventoryprint.printInfo({
        ids: "278",
        type: "orderNote",
        title: "采购交货单入库直发/关联交易",
      });
    },
    getDetail() {
      if (!this.$route.params.id || this.loading) return;
      this.loading = true;
      getPurDelivery(this.$route.params.id)
        .then((res) => {
          // console.log(res, "res");
          if (res.code === 200) {
            let list = res.data.purchaseDeliveryLineList;
            const WERKS = res.data.purchaseDeliveryLineList
              ? res.data.purchaseDeliveryLineList[0]?.WERKS
              : "";
            this.detail = { ...res.data, WERKS };
            this.searchData = getSearchData(res.data.docStatus, WERKS);

            // console.log(this.searchData, this.detail,'00000')

            this.searchInfo = this.detail;

            this.lineCloumn = getlineColums(this.detail.docStatus);
            const _rows = setTableDictValue(
              this.lineCloumn,
              list,
              this.dictDataOptions
            );
            this.lineList = _rows;
          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    submitEmits(data, type) {
      this.inventoryAccountRu(data, type);
    },
    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },

    async inventoryAccountRu(data) {
      try {
        const list = this.$refs.lineTable.newlineList;
        if (list.length === 0) {
          this.$modal.msgError("请添加行信息");
          return;
        }

        // console.log(list,'list')
        if (this.$refs.lineTable.checkList()) return;
        if (!this.$route.params.id || this.loading) return;
        this.loading = true;

        let params = {
          ...this.detail,
          ...data,
          purchaseDeliveryLineList: list,
        };
        let resT = await addPurDelivery(params);
        if (resT.code === 200) {
          const res = await inventoryAccountRu(
            this.detail.vbeln || this.detail.VBELN
          );
          this.$modal.msgSuccess(res?.msg || res?.data?.msg || "入库成功");
        }
        this.loading = false;
        this.getDetail();
      } catch (error) {
        this.loading = false;
        console.log(error);
        this.getDetail();
      }
    },
    getSearchcallBack(params, activeProps) {
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "lgort" && params[activeProps.prop]) {
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        if (lineList && lineList.length) {
          const list = lineList.map((item) => {
            let [key = "", value = ""] = params["lgort,lgortName"];
            let label = `${value}`;
            if (!key) {
              return item;
            }
            return {
              ...item,
              LGORT: params.lgort,
              "LGORT,lgortName": params["lgort,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
