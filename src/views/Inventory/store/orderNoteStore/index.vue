<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchInfo="searchInfo"
      ref="ComTableList"
      :isSelection="true"
      :tableProps="tableProps"
      :selectable="selectable"
      @selection-change="handleSelectionChange"
    >
      <template v-for="(item, index) in keyList" #[item.slotName]="{ row }">
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
        />
      </template>

      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="inventoryAccountRu"
          :disabled="selectList.length === 0"
          v-hasPermi="['store:in']"
          >批量入库</el-button
        >
          <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="getPrint" 
          :disabled="selectList.length === 0"
          >批量打印</el-button
        >
      </template>

      <template #caozuo="{ row }">
        <el-button
          v-hasPermi="['store:edit']"
          size="mini"
          type="primary"
          @click="gotoAdd('edit', row)"
          >修改</el-button
        >
        <el-button
          v-hasPermi="['store:detail']"
          size="mini"
          type="primary"
          @click="gotoAdd('look', row)"
          >详情</el-button
        >
      </template>
    </ComTableList>
        <Inventoryprint ref="Inventoryprint"/>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import Inventoryprint from '../../components/Inventoryprint.vue'
import { listPurDelivery,inventoryAccountRu } from "@/api/purchase/purchaseDelivery/purDelivery";
import { changeInfoData } from "@/components/LineTable/units";
import { getDictKey, dictOptionCodeKey } from "@/utils/index";
import { searchData, columns, keyList } from "./config";

export default {
  name: "OrderNoteStore",
  components: {
    ComTableList,
    Inventoryprint
  },
  dictsKey: dictOptionCodeKey(columns),
  dicts: ["demand_send_status", "doct_status"],
  data() {
    return {
      searchData,
      columns,
      keyList,
      tableProps: {
        rowKey: "purchaseHeadId",
      },
      selectList: [],
      searchInfo:{
        // docStatus:'S'
      }
    };
  },
  created() {},
  methods: {
    getPrint(){
      this.$refs.Inventoryprint.printInfo({
        ids:'278',
        type:'orderNote',
        title:'采购交货单入库直发/关联交易'
      })
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {

       if (!params?.docStatusList || params.docStatusList.length === 0) {
         params.docStatusList = ["rkSuccess", "rkError", "S"];
       }


      return listPurDelivery(params);
    },
    gotoAdd(type, data) {
      this.$store
        .dispatch("tagsView/delPage", { name: "OrderNoteStoreDetail" })
        .then(() => {
          this.$router.push(
            `/inventory/store/orderNoteStore-detail/index/${type}/${data.purchaseHeadId}`
          );
        });
    },
    handleSelectionChange(val) {
      this.selectList = val;
    },
    selectable(row) {
       const params =  changeInfoData(row)
      return (params.WERKS !== '6001');
    },
    inventoryAccountRu() {
      const list = this.selectList.filter((item)=>{
       const params =  changeInfoData(item)
        return ['rkError','S'].includes(item.docStatus) &&  (params.WERKS !== '6001');
      }) 
      if (!list || list.length === 0) {
        this.$message({
          type: "error",
          message: `暂无入库的数据`,
        });
        return;
      }
      const getFun = list.map(({ vbeln, VBELN }) => {
        return inventoryAccountRu(vbeln || VBELN);
      });
      Promise.all(getFun)
        .then((res) => {
          // console.log(res);
          let data = this.$refs.ComTableList.$refs.searchComTableInfo.dataInfo
          this.$refs.ComTableList.submitEmits(data)
          this.$modal.msgSuccess("入库成功");
        })
        .catch((error) => {
          console.log(error);
          let data = this.$refs.ComTableList.$refs.searchComTableInfo.dataInfo
          this.$refs.ComTableList.submitEmits(data)
        });
    },
  },
};
</script>
