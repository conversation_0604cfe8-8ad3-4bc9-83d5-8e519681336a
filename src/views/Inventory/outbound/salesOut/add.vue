<template>
  <div class="add" v-loading="loading">
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="16"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      @submitEmits="submitEmits"
    >
      <template #searchButton v-if="pageType !== 'look'">
        <el-button
          v-if="['E', 'INIT','S','ckError'].includes(detail.docStatus)"
          type="danger"
          size="mini"
          @click="goBackData('huiTui')"
          v-hasPermi="['sales/purDelivery:purDelivery:submit']"
          :disabled="loading"
          >回退</el-button
        >
        <el-button
          type="primary"
          size="mini"
          v-if="['S', 'ckError'].includes(detail.docStatus) && (detail.WERKS !== '6001')"
          @click="inventoryAccountPost"
          v-hasPermi="['sales/salesDelivery:out']"
          >出库</el-button
        >
        
      </template>
    </ComSearch>
    <LineTable
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      :isShowCancel="['E', 'INIT','S','ckError'].includes(detail.docStatus)"
      :cancelFuntion="cancelFuntion"
       @refreshList="getDetail"
      :btnProps="btnProps"
       ref="LineTable"
      :showSelection="true"
    ></LineTable>
  </div>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import { btnProps, lineCloumn, searchData2,getSearchData2 } from "./config";
import { queryScanBarInfo } from "@/api/inventory/inventoryManage";
import {
  inventoryAccountPost,
  getSalesDelivery,
  printInfo,
} from "@/api/sales/salesDelivery/salesDelivery";
import { listWerksInfo } from "@/api/sales/werksInfo/werksInfo";
import {
  addSalesDelivery,
} from "@/api/sales/salesDelivery/salesDelivery";

import { dictOptionCodeKey, setTableDictValue } from "@/utils/index";
export default {
  name: "OutboundDetail",
  components: {
    ComSearch,
    LineTable,
  },
  dicts: ["demand_doc_status", "doct_status",'zterm'],
  dictsKey: dictOptionCodeKey(lineCloumn),
  props: {},
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      searchInfo: {},
      rules: {},
      searchData: searchData2,
      pageType,
      lineCloumn,
      btnProps,
      detail: {},
      lineList: [],
    };
  },

  created() {
    this.getDetail();
  },
  watch: {},
  methods: {
    getDetail() {
      if (!this.$route.params.id || this.loading) return;
      this.loading = true;
      getSalesDelivery(this.$route.params.id)
        .then((res) => {
          // console.log(res, "res");
          if (res.code === 200) {
            this.detail = res.data;
            this.searchInfo = this.detail;
            this.searchData = getSearchData2(this.detail.docStatus,res.data.WERKS)
             const _rows = setTableDictValue(
              this.lineCloumn,
              res.data.salesDeliveryLineList,
              this.dictDataOptions
            );
            this.lineList = _rows;


          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
      goBackData() {
      this.$confirm("是否回退该单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        
      });
    },

   async cancelFuntion(data, rows) {

      try {
        const ids = rows
          .filter(({ ABGRU }) => ABGRU !== "12")
          .map(({ deliveryLineId }) => deliveryLineId);

        if (!ids || ids.length === 0) {
          this.$message({
            type: "error",
            message: `暂无取消的数据`,
          });
          return;
        }

        let list = this.$refs.LineTable.newlineList;
        list = [...list].map((item) => {
          item.ABGRU = ids.includes(item.deliveryLineId) ? "12" : undefined;
          return item;
        });

        const dataInfo = this.$refs.ComSearchInfo.changeInfoData();

        const params = {
          ...this.detail,
          ...dataInfo,
          antion: "upd",
          delFlag: 0,
          VSTEL: this.detail.vstel,
          salesDeliveryLineList: list,
        };

        if (this.loading) return;
        this.loading = true;

        await addSalesDelivery({
          ...params,
        });
        this.loading = false;
        this.getDetail();
      } catch (error) {
        this.loading = false;
        this.getDetail();
        console.log(error, "error");
      }
    },
    inventoryAccountPost(){
      this.$refs.ComSearchInfo.onSubmit('chuku');
    },
    async submitEmits(data) {
      try {
        if (!this.$route.params.id || this.loading) return;
        this.loading = true;
        // 出库校验
        const resData = await queryScanBarInfo({
          docNo: this.detail?.vbelnDr,
        });

        let obj = {};
        if (resData.code === 200 && resData.rows && resData.rows.length) {
          obj = resData.rows[0];
        }

        if (obj?.checkFlag === "Y" && obj?.allScan === "N") {
          this.$modal.msgError('订单号：' +this.detail?.vbelnDr +"未全部扫码，不可出库");
          this.loading = false;
          return;
        }

        await addSalesDelivery({
          ...this.detail,
          ...data,
          antion:'upd'
        })
        const res = await inventoryAccountPost(this.detail.deliveryHeadId);
        this.$modal.msgSuccess(res?.msg || res?.data?.msg || "操作成功");
        this.loading = false;
        this.getDetail();
      } catch (error) {
        this.loading = false;
        this.getDetail();
        console.log(error);
      }
    },
    printInfo() {
      window.open(
        `${location.origin}/inventory/print?ids=${this.$route.params.id}`
      );
      // this.$store.dispatch("tagsView/delPage", { name: "InventoryPrint" }).then(()=>{
      //     this.$router.push(
      //       `/inventory/print?ids=${this.$route.params.id}`
      //     );
      // });
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
