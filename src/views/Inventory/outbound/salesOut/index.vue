<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :searchInfo="searchInfo"
      :column="columns"
      :getTableList="getList"
      ref="ComTableList"
      :isSelection="true"
      :tableProps="tableProps"
      :selectable="selectable"
      @selection-change="handleSelectionChange"
    >
      <template v-for="(item, index) in keyList" #[item.slotName]="{ row }">
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>
       <template #salesDeliveryNo="{ row }">
         <a class="href" @click="gotoAdd('look', row)">{{ row.salesDeliveryNo }}</a>
      </template>
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          :disabled="selectList.length === 0 ? true : false"
          @click="inventoryAccountPost"
          v-hasPermi="['sales/salesDelivery:out']"
          >批量出库</el-button
        >
        <!-- <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="inventoryAccountPrint"
          v-hasPermi="['sales/salesDelivery:print']"
          >批量打印</el-button
        > -->
      </template>

      <template #caozuo="{ row }">
        <el-button
          v-hasPermi="['sales/salesDelivery:edit']"
          size="mini"
          type="primary"
          @click="gotoAdd('edit', row)"
          >修改</el-button
        >
        <el-button
          v-hasPermi="['sales/salesDelivery:salesDelivery:detail']"
          size="mini"
          type="primary"
          @click="gotoAdd('look', row)"
          >详情</el-button
        >
      </template>
    </ComTableList>

    <printA ref="printA"/>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import printA from "@/components/Print/printA.vue";

import {
  listSalesDelivery,
  inventoryAccountPost,
} from "@/api/sales/salesDelivery/salesDelivery";
import { getDictKey, dictOptionCodeKey,param,param2Obj  } from "@/utils/index";
import { searchData, columns, keyList } from "./config";
import { queryScanBarInfo } from "@/api/inventory/inventoryManage";
import { changeInfoData } from "@/components/LineTable/units";
export default {
  name: "SalesOut",
  components: {
    ComTableList,
    printA
  },
  dictsKey: dictOptionCodeKey(columns),
  dicts: ["demand_send_status", "doct_status",'zterm'],
  data() {
    return {
      searchData:searchData,
      columns,
      keyList,
      selectList:[],
      searchInfo:{
      },
      dictKey: getDictKey(columns),
      tableProps: {
        rowKey: "deliveryHeadId",
      },
    };
  },
  created() {},
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      if (!params?.docStatusList || params.docStatusList.length === 0) {
         params.docStatusList = ['S','ckSuccess','ckError']
      }

      return listSalesDelivery(params);
    },
    gotoAdd(type, data) {
      this.$store.dispatch("tagsView/delPage", { name: "OutboundDetail" }).then(()=>{
          this.$router.push(
            `/inventory/outbound/salesOut-detail/index/${type}/${data.deliveryHeadId}`
          );
      });
      
    },
    handleSelectionChange(val) {
      this.selectList = val;
    },
    selectable(row) {
      const params = changeInfoData(row)
      return (['S','ckError'].includes(row.docStatus) && (params.WERKS !== '6001'));
    },
    inventoryAccountPrint(){
       let ids = this.selectList.map(({deliveryHeadId})=>(deliveryHeadId)).join(',')

       this.$refs.printA.printInfo(ids,'0000')
       this.$refs.printA.printDialogVisible = true

      // ids = param({ids:ids})

      //  window.open(`${location.origin}/inventory/print?${ids}`)
      //  this.$store.dispatch("tagsView/delPage", { name: "InventoryPrint" }).then(()=>{
      //     this.$router.push(
      //       `/inventory/print?${ids}`
      //     );
      // });
    },
    inventoryAccountPost() {
      if (!this.selectList || this.selectList.length === 0) {
        this.$message({
          type: "error",
          message: `请选择需要出库的数据`,
        });
        return;
      }
      const getFun = this.selectList.map(async({ deliveryHeadId,vbelnDr }) => {
         try {
          // 出库校验
          const resData = await queryScanBarInfo({
            docNo: vbelnDr,
          });

          let obj = {};
          if (resData.code === 200 && resData.rows && resData.rows.length) {
            obj = resData.rows[0];
          }

          if (obj?.checkFlag === "Y" && obj.allScan === "N") {
            this.$modal.msgError('订单号：' +this.detail?.vbelnDr +"未全部扫码，不可出库");
            this.loading = false;
            return;
          }
         } catch (error) {

          console.log(error,'error')
          
         }

        return inventoryAccountPost(deliveryHeadId);

      });
      Promise.all(getFun)
        .then((res) => {
          // console.log(res);
          let data = this.$refs.ComTableList.$refs.searchComTableInfo.dataInfo
          this.$refs.ComTableList.submitEmits(data)
          this.$modal.msgSuccess("出库成功");
        })
        .catch((error) => {
           let data = this.$refs.ComTableList.$refs.searchComTableInfo.dataInfo
           this.$refs.ComTableList.submitEmits(data)
          console.log(error);
        });
    },
  },
};
</script>
