import { deliverColums,deliverLine,deliverSearchData,searchData1 } from '@/views/sales/purDelivery/config.js'



export const columns = [...deliverColums].filter(({prop})=>(!['creditLimit','availableCreditLimit'].includes(prop)))
export const searchData2  = JSON.parse(JSON.stringify(deliverSearchData))

const obj = {
  prop:'remark',
  label:'出库备注',
  searchOut:true
}
export const getSearchData2 = (docStatus,WERKS)=>{
  const searchData  = JSON.parse(JSON.stringify([...deliverSearchData,obj]))

  const _searchData = searchData.map((item)=>{
    if(item.prop === 'remark' && docStatus === 'S' && (WERKS !== '6001')){
      item.attr={
        disabled:false
      }
    }else{
      item.attr = {
        disabled:true
      }
    }
    return item
  })

  return  _searchData
}

export const searchData = JSON.parse(JSON.stringify(searchData1)).map((item)=>{
  item.attr = {}
  if(item.prop === 'docStatus'){
    item.otherBind = (item)=>{
      let dis = !['ckSuccess','ckError','S'].includes(item.value)
      return {
         disabled: location.href.indexOf("outbound") > -1 ? dis : false
      }
    }
  }

  return item
})

export const lineCloumn = deliverLine
export const keyList = columns.filter(({optionsCode})=>(optionsCode))

export const btnProps = {
  add: {
    isShow: false,
    hasPermi: [],
  },
  batchDelete: {
    isShow: false,
    hasPermi: [],
  },
  deleteLine: {
    isShow: false,
    hasPermi: [],
  },
  caozuoShow:{
    isShow: true,
    hasPermi: [''],
  }
};

