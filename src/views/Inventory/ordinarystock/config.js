export const searchData = [
  {
    label: "公司",
    type: "input",
    prop: "",
  },
  {
    label: "工厂",
    type: "input",
    prop: "",
  },
  {
    label: "库存地点",
    type: "input",
    prop: "",
  },

  {
    label: "物料编码",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "图号",
    type: "input",
    prop: "",
  },
  {
    label: "供应商",
    type: "input",
    prop: "LIFNR",
  },
];

export const columns = [
  {
    label: "公司",
    type: "input",
    prop: "",
  },
  {
    label: "工厂",
    type: "input",
    prop: "",
  },
  {
    label: "库存地点",
    type: "input",
    prop: "",
  },

  {
    label: "物料编码",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "图号",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "物料名称",
  },

  {
    prop: "",
    label: "库存总数",
    width: 120,
  },
  {
    prop: "H",
    label: "销售锁定库存",
    width: 140,
    slotName: "H",
  },

  {
    prop: "M",
    label: "移库/调拨锁定库存",
    width: 200,
    slotName: "M",
  },

  {
    prop: "",
    label: "可用库存",
  },

  {
    prop: "",
    label: "仓储组",
    width: 140,
  },
  {
    prop: "",
    label: "供应商编码",
    width: 120,
  },

  {
    prop: "",
    label: "供应商名称",
    width: 120,
  },

  {
    prop: "",
    label: "物料类型",
    width: 200,
  },

  {
    prop: "",
    label: "业务主体",
    width: 200,
  },
];

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const moveSeachData = [
  {
    label: "库存地点",
    type: "input",
    prop: "",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "工厂",
    type: "input",
    prop: "",
  },
];

export const moveColums = [
  {
    label: "行号",
    type: "input",
    prop: "POSNR",
  },
  {
    label: "物料号",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "库存地点",
    type: "input",
    prop: "",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "移库数量",
    type: "input",
    prop: "KWMENG",
  },

  {
    prop: "WERKS",
    label: "发货工厂",
  },

  {
    label: "收货工厂",
    prop: "UMWRK",
    width: 120,
  },
];

export const getMoveListData = () => {
  const columnsObj = {};
  moveColums.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lockSearch = [
  {
    prop: "WERKS",
    label: "工厂",
    width: 140,
  },
  {
    prop: "LGORT",
    label: "库存地点",
    width: 140,
  },
  {
    label: "物料编码",
    type: "input",
    prop: "MATNR",
  },
  {
    prop: "KWMENG",
    label: "数量",
    hiddenSearch: true,
    // width: 140,
  },
];

export const getLockSearch = () => {
  const columnsObj = {};
  lockSearch.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};
