<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 6 }"
      :tableProps="tableProps"
      ref="stock"
    >
      <template #searchButton>
        <el-button size="mini" :type="'primary'" icon="el-icon-plus"
          >导出</el-button
        >
      </template>
      <template #M="{ row }">
        <a class="link-type" @click="open('M')">{{ row.M }}</a>
      </template>
      <template #H="{ row }">
        <a class="link-type" @click="open('H')">{{ row.H }}</a>
      </template>
    </ComTableList>
    <ComModalTable
      ref="moveComModalTable"
      title="移库/调拨锁定库存"
      :searchData="moveSeachData"
      :column="moveColums"
      :getTableList="getMoveList"
    >
    </ComModalTable>
    <ComModalTable
      ref="lockComModalTable"
      title="销售锁定库存"
      :searchProps="searchProps"
      :searchData="lockSearch"
      :column="lockSearch"
      :getTableList="getMoveList"
    >
    </ComModalTable>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";

import { listInventory } from "@/api/inventory/stock";
import {
  searchData,
  columns,
  getListData,
  moveColums,
  moveSeachData,
  getMoveListData,
  lockSearch,
  getLockSearch,
} from "./config";

export default {
  name: "Ordinarystock",
  components: {
    ComTableList,
    ComModalTable,
  },
  data() {
    return {
      lockSearch,
      moveColums,
      moveSeachData,
      columns,
      searchData: searchData,
      innerVisible: false,
      tableProps: {
        height: 460,
      },
      searchProps: {
        rules: {
          WERKS: [{ required: true, message: "请输入工厂", trigger: "blur" }],
        },
      },
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return {
        code: 200,
        rows: getListData(),
        total: getListData().length,
      };
      const res = await listInventory(params);
      return res;
    },
    async getMoveList() {
      return {
        code: 200,
        rows: getMoveListData(),
        total: getMoveListData().length,
      };
    },
    async getlockList() {
      return {
        code: 200,
        rows: getLockSearch(),
        total: getLockSearch().length,
      };
    },
    open(type) {
      if (type === "M") {
        this.$refs.moveComModalTable.dialogVisible = true;
      } else if (type === "H") {
        this.$refs.lockComModalTable.dialogVisible = true;
      }
    },
  },
};
</script>
