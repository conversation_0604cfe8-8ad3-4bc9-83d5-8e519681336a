<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="searchProps"
      :tableProps="tableProps"
      ref="stock"
    >
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="inventoryExport"
          >导出</el-button
        >
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";
import {
  queryWmsScanInfo,
  inventoryExport,
} from "@/api/inventory/inventoryManage";
import { columns, rules } from "./config";

export default {
  name: "ScanTablelist",
  components: {
    ComTableList,
    ComModalTable,
  },
  data() {
    return {
      columns,
      searchData: columns.slice(0, 6),
      searchProps: {
        rules,
      },
      tableProps: {
        // height: 460,
      },
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      const res = await queryWmsScanInfo(params);
      // let rows = res?.data?.items || []
      // res.rows = rows
      // res.total = rows.length

      return res;
    },
    inventoryExport() {
      let params = this.$refs.stock.$refs.searchComTableInfo.dataInfo

      this.download(
        "/sales/inventory/wmsScanInfo/export",
        {
          ...params,
        },
        `扫码报表_.xlsx`
      );

    },
      
  },
};
</script>
