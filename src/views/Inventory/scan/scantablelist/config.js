
export const columns = [
  {
    label: "仓库ID",
    type: "input",
    prop: "warehouseId1",
  },
 
  {
    label: "条码工单",
    type: "input",
    prop: "workingno",
  },

    {
    label: "工厂名称",
    type: "input",
    prop: "werksName",
  },

    {
    label: "字库",
    type: "input",
    prop: "lgort",
  },

  {
    label: "字库名称",
    type: "input",
    prop: "lgortName",
  },
  {
    label: "上游单据",
    type: "input",
    prop: "soReference1",
  },


   {
    label: "单据类型",
    type: "input",
    prop: "orderType1",
  },
  // {
  //   label: "工单分类",
  //   type: "input",
  //   prop: "hedi07",
  // },
 {
    label: "条码",
    type: "input",
    prop: "barcode",
  },

  //  {
  //   label: "条码类型",
  //   type: "input",
  //   prop: "barcodeType",
  // },
  {
    label: "父条码",
    type: "input",
    prop: "barcodeParent",
  },

  
   {
    label: "条码数量",
    type: "input",
    prop: "barcodeQty",
  },
  {
    label: "出库日期",
    type: "input",
    prop: "addTime",
  },

   {
    label: "物料编码",
    type: "input",
    prop: "sku",
  },
  {
    label: "物料名称",
    type: "input",
    prop: "skudesc",
  },

  //    {
  //   label: "销售方描述",
  //   type: "input",
  //   prop: "fmName",
  // },
  // {
  //   label: "销售方",
  //   type: "input",
  //   prop: "fmlocationId",
  // },

      {
    label: "客户方",
    type: "input",
    prop: "toName",
  },
  {
    label: "客户方地址",
    type: "input",
    prop: "address",
  },
    {
    label: "条码批次",
    type: "input",
    prop: "tmid",
  },
  {
    label: "零件加工编号",
    type: "input",
    prop: "workingsnno",
  },

  //   {
  //   label: "业务数量",
  //   type: "input",
  //   prop: "orderQty",
  // },
  {
    label: "保外标识",
    type: "input",
    prop: "empty",
  },

    {
    label: "创建人",
    type: "input",
    prop: "createBy",
  },


].map((item)=>{
  item.width = item.width || 140
  return item
});

														

export const rules = {
    // partProcessNum: [
    //   { required: true, message: "请输入零件加工编码", trigger: "change" },
    // ],
  }
export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

