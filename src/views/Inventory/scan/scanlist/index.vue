<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="searchProps"
      :tableProps="tableProps"
      ref="stock"
    >
      <template #searchButton>
        <el-button size="mini" :type="'primary'" icon="el-icon-plus"
          >导出</el-button
        >
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";
import { barcodeFlow } from "@/api/inventory/inventoryManage";
import {
  columns,
  rules
} from "./config";

export default {
  name: "Scanlist",
  components: {
    ComTableList,
    ComModalTable,
  },
  data() {
    return {
      columns,
      searchData: columns.slice(0,6),
      searchProps:{
         rules
      },
      tableProps: {
        // height: 460,
      },
     
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      const res = await barcodeFlow({
           "xmldata": {
           "data": {
            "orderinfo": {
              ...params
            }
          }
        }
      });
      let rows = res?.data?.items || []
      res.rows = rows
      res.total = rows.length

      return res;
    },
  },
};
</script>
