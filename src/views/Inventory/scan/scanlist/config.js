
export const columns = [
  {
    label: "销售单号",
    type: "input",
    prop: "salesOrderNum",
  },
 
  {
    label: "物料图号",
    type: "input",
    prop: "drawingNo",
  },

  {
    label: "条码编码",
    type: "input",
    prop: "barCode",
  },
  {
    label: "零件加工编码",
    type: "input",
    prop: "partProcessNum",
  },

  {
    prop: "shortCode",
    label: "供应商三位码",
  },

   {
    label: "DMS经销商",
    type: "input",
    prop: "sourceCustomName",
  },
  {
    label: "DMS仓库地址",
    type: "input",
    prop: "sourceCustomSite",
  },
 {
    label: "销售时间",
    type: "input",
    prop: "salesTime",
  },

   {
    label: "DMS客户",
    type: "input",
    prop: "customName",
  },
  {
    label: "DMS中客户地址",
    type: "input",
    prop: "customSite",
  },
];
export const rules = {
    partProcessNum: [
      { required: true, message: "请输入零件加工编码", trigger: "change" },
    ],
  }
export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

