<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="searchProps"
      :tableProps="tableProps"
      ref="stock"
    >

      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
             v-hasPermi="['Scanck:scanbtn']"
            @click="gotoAdd('edit', row)"
            >扫码</el-button
          >
          <el-button
            size="mini"
            type="primary"
            v-hasPermi="['Scanck:detail']"
            @click="gotoAdd('look', row)"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";
import { queryScanBarInfo } from "@/api/inventory/inventoryManage";
import {
  columns,
} from "./config";

export default {
  name: "Scanck",
  components: {
    ComTableList,
    ComModalTable,
  },
   dictsKey: ["VKORG",'KUNNR','LGORT'],
  dicts:['doc_type','scan_flag'],
  data() {
    return {
      columns,
      searchData: columns.slice(0,6),
      searchProps:{
        //  rules
      },
      tableProps: {
        // height: 460,
      },
     
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      // const data = getListData()

      // return {
      //   code:200,
      //   rows:data,
      //   total:2
      // }

      const res = await queryScanBarInfo(params);
      return res;
    },
    changeInfoData(_searchInfo) {
      Object.keys(_searchInfo).forEach((item) => {
        // console.log(item,_searchInfo,'00000')
        if (item.indexOf(",") > -1 && _searchInfo[item]) {
          item.split(",").forEach((key, index) => {
            _searchInfo[key] = _searchInfo[item][index];
          });
          delete _searchInfo[item];
        }
      });
      const searchInfo2 = JSON.parse(JSON.stringify(_searchInfo));
      return searchInfo2;
    },
    gotoAdd(type, data = {}) {
      this.$store.dispatch("tagsView/delPage", { name: "ScanckDetail" }).then(()=>{
        const query = this.changeInfoData(JSON.parse(JSON.stringify(data)))
        this.$router.push({
          path:`/inventory/scan/scanck-detail/index/${type}/${data?.zsasOrderHeaderId || 'null'}`,
          query:query
        });
      });
      
    },
  },
};
</script>
