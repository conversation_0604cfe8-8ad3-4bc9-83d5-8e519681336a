<template>
  <el-dialog
    title=""
    :visible.sync="dialogVisible"
    width="100%"
    class="scanDialog"
    :before-close="handleClose"
    :top="'0px'"
  >
    <div v-loading="loading">
      <ComTableList
        :searchData="searchData"
        :column="columns"
        :getTableList="getList"
        :searchProps="searchProps"
        :searchInfo="searchInfo"
        :tableProps="tableProps"
        ref="ComTableList"
        :isSelection="true"
        @selection-change="handleSelectionChange"
      >
        <template>
          <el-button
            :disabled="selectList.length === 0"
            style="margin-bottom: 10px"
            v-hasPermi="['Scanck:lineDel']"
            size="mini"
            type="danger"
            @click="del('all','')"
            
            >批量删除</el-button
          >
        </template>
        <template #caozuo="{ row }">
          <el-button
            v-hasPermi="['Scanck:lineDel']"
            size="mini"
            type="danger"
            @click="del('', row)"
            >删除</el-button
          >
        </template>
      </ComTableList>
    </div>
  </el-dialog>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import { detailHeader, detailLine, getListData } from "./config";
import {
  queryScanDetailsInfo,
  delScanDetailsLine,
} from "@/api/inventory/inventoryManage";
export default {
  components: {
    ComTableList,
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      columns: detailLine,
      searchData: detailHeader,
      selectList: [],
      labelWidth: "90px",
      searchInfo: {},
      searchProps: {
        showHeader: false,
        searchInfo: {},
      },
      tableProps: {
        // height: 200,
        rowKey: "deliveryHeadId",
      },
      detail: {},
      hasDel:''
    };
  },
  created() {},
  mounted() {},
  methods: {
    handleSelectionChange(val) {
      this.selectList = val;
    },
    getData(data = {}) {
      this.searchInfo = data;
      const query = {
        "docNo": data?.docNo,
        "docLineNo": data?.docLineNo,
        "logrt": data?.lgort,
        // ...data,
      };
      this.$refs.ComTableList.submitEmits(query);
    },
    async getList(params) {
      const res = await queryScanDetailsInfo(params);
      res.rows = res?.data?.scanDetailsLineList;
      res.total = res?.data?.scanDetailsLineList.length || 0
      return res;
    },
    del(type,row) {
      this.$confirm("是否确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.delScanDetailsLine(row);

        //  this.$refs.ComTableList.submitEmits(this.detail);
      });
    },

    async delScanDetailsLine(row) {
      try {
        let ids = []
        if(row && row?.scanDetailsLineId){
          ids = [row?.scanDetailsLineId]
        }else{
          console.log(this.selectList,'ppppp')
          ids = this.selectList?.map(({ scanDetailsLineId:id }) => id);
        }
        if (ids.length === 0) return;
        if (this.loading) return;
        this.loading = true;
        const res = await delScanDetailsLine(ids);
        this.loading = false;
        this.hasDel = true

        if (res.code === 200) {
          this.getData(this.searchInfo)
          this.$modal.msgSuccess(res?.msg || '删除成功');
        }
      } catch (error) {
        this.loading = false;

        console.log(error);
      }
    },

    handleClose(done) {
      this.dialogVisible = false;
      this.$emit("close", this.hasDel);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .scanDialog .el-dialog:not(.is-fullscreen) {
  margin-top: 0px !important;
}

::v-deep .comTable .el-table__fixed-right {
  // width: 60px !important;
}
</style>
