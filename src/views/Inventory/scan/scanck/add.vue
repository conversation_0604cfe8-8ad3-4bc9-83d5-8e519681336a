<template>
  <div class="add" v-loading="loading">
    <div class="movingContent">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="6"
        :labelWidth="labelWidth"
        :showBtn="false"
      >
      </ComSearch>

      <el-form
        ref="form"
        @submit.native.prevent
        :model="form"
        label-width="80px"
        v-if="pageType !== 'look'"
      >
        <el-form-item label="条码">
          <el-input
            v-model="form.barid"
            clearable
            placeholder="请输入条码"
            ref="recode"
          >
            <el-button
              @click.stop="clickCode"
              slot="append"
              icon="el-icon-search"
            ></el-button>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-button
      v-if="pageType !== 'look' || lineList.length === 0"
      type="danger"
      size="mini"
      @click.stop="delFuntion('del')"
      v-hasPermi="['Scanck:handDel']"
      >整单删除</el-button
    >
    <LineTable
      ref="lineTable"
      :showSelection="false"
      :btnProps="btnProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      keyId="docLineNo"
      POSNRKey="posnr"
    >
      <template #caozuo="row">
        <span
          class="detailText"
          @click="gotoDetail(row)"
          v-hasPermi="['Scanck:lineDetail']"
          >详情</span
        >
      </template>
    </LineTable>
    <LineModal ref="LineModal" @close="close" />
  </div>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import LineModal from "./LineModal.vue";
import { setTableDictValue, getQueryObject } from "@/utils/index.js";
import { searchData, lineCloumn, btnProps, getListData } from "./config";
import {
  queryDocDetailsInfo,
  getCheckBarCode,
  delScanDetails,
} from "@/api/inventory/inventoryManage";

export default {
  name: "ScanckDetail",
  dictsKey: ["VKORG", "KUNNR", "LGORT"],
  dicts: ["doc_type", "scan_flag"],
  components: {
    ComSearch,
    LineTable,
    LineModal,
  },

  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      searchData,
      isScannerOpen: false, // 控制扫码组件显示状态
      btnProps,
      loading: false,
      searchInfo: {},
      form: {
        barid: "",
      },
      labelWidth: "90px",
      rules: {
        companyCode: [
          { required: true, message: "请选择选择公司", trigger: "change" },
        ],
        werks: [
          { required: true, message: "请选择发货工厂", trigger: "change" },
        ],
      },
      pageType,
      lineCloumn,
      lineList: [],
      detail: {
        docStatus: "",
      },
      codeData: {},
    };
  },

  created() {
    this.setCodeDetail();
    this.getKeyDown()
    

  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  watch: {},
  activated() {
    const pageType = this.$route?.params?.type || "";
    if (pageType !== "look") {
      window.addEventListener("keyup", this.handClickSearch);
      this.$refs.recode.focus();
      this.getKeyDown()
    }
  },
  deactivated() {
    window.removeEventListener("keyup", this.handClickSearch);
     document.onkeydown = null

  },
  updated() {},
  mounted() {
    const pageType = this.$route?.params?.type || "";
    if (pageType !== "look") {
      window.addEventListener("keyup", this.handClickSearch);
      this.$refs.recode.focus();
    }
  },
  beforeDestroy() {
    window.removeEventListener("keyup", this.handClickSearch);
     document.onkeydown = null
    // window.addEventListener('resize', this.getWidth);
  },
  methods: {
    getKeyDown(){


      // document.onkeydown = (e) => {
        
      //   if(e.which === 229){
      //     console.log(this.form?.barid,'this.form?.barid222')

      //   }
     
      
      // }
    },
    closeEdit() {
      this.$refs.lineTable.closeEdit();
    },
    isString(str) {
      return (
        str instanceof String ||
        Object.prototype.toString.call(str) === "[object String]"
      );
    },
    // 获取条码
    handClickSearch(event) {
      if (event.keyCode === 13 || event.which === 229) {
        // 例如，阻止表单默认的提交行为
        event.preventDefault();

        if (
          this.pageType === "look" ||
          !event ||
          location.href?.indexOf("scanck-detail") === -1
        )
          return;
        let newBarid = this.form?.barid;

        // this.$modal.msgSuccess(newBarid + "    newBarid条码开开始",event);

        if (!newBarid) return;
        if (newBarid.indexOf("?") > -1) {
          this.form.barid = undefined
          this.$nextTick(()=>{
            const obj = getQueryObject(newBarid);
            this.codeData = obj;
            this.form.barid = obj.barid;
            this.$modal.msgSuccess(obj.barid + "    event条码1",this.form?.barid);
          })
          // this.handleDecode(obj.barid);
        } else {
          //  this.form.barid = undefined
          this.$modal.msgSuccess(this.form.barid + "    event条码2");
          this.handleDecode(this.form.barid);
        }
      }
    },

    clickCode() {
      if (!this.form.barid) return;
      // this.$modal.msgSuccess(this.form.barid + "点击条码3");
      this.handleDecode(this.form.barid);
    },

    async setCodeDetail() {
      try {
        if (this.loading) return;
        this.loading = true;
        let query = this.$route.query;
        const res = await queryDocDetailsInfo({
          docNo: query?.docNo,
          werks: query?.werks,
          kunnr: query?.kunnr,
          // ...query
        });


        if (res.code === 200) {
          this.detail = res?.rows[0] || {};
          this.searchInfo = res?.rows[0];
          const _rows = setTableDictValue(
            this.lineCloumn,
            res.rows,
            this.dictDataOptions
          );
          this.lineList = _rows;

          this.form.barid = undefined;

        }
        this.loading = false;
        this.$refs.recode.focus();
      } catch (error) {
        // this.form.barid = undefined;
        this.$refs.recode.focus();
        this.loading = false;
      }
    },

    /**
     * 处理扫码结果
     * @param {string} result 二维码内容
     */
    // 查询行列表明细
    async handleDecode(id = "") {
      if (!id) return;
      try {
        if (this.loading) return;
        this.loading = true;
        const res = await getCheckBarCode({
          // "barcode": "230900000029",
          barcode: id,
          docDetailsInfoList: [
            {
              ...this.detail,
            },
          ],
        });
        this.loading = false;
        if (res.code === 200) {
          this.form.barid = "";
          this.setCodeDetail();
        }

        this.$modal.msgSuccess(res.msg || '操作成功');
      } catch (error) {
        this.$refs.recode.focus();
        // this.$modal.msgError('操作失败');
        this.loading = false;
      }
    },

    delFuntion(rows) {
      this.$confirm("是否确认删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.delScanDetails();
        })
        .catch(() => {
          this.close();
        });
    },
    async delScanDetails() {
      if (!this.detail?.docNo) return;

      try {
        const res = await delScanDetails(this.detail?.docNo);
        if (res.code === 200) {
          this.setCodeDetail();
        }
      } catch (error) {
        console.log(error, "error");
        this.close();
      }
    },
    close(val) {
      this.$refs.recode.focus();
      if (val) {
        this.setCodeDetail();
      }
    },
    changeInfoData(_searchInfo) {
      Object.keys(_searchInfo).forEach((item) => {
        // console.log(item,_searchInfo,'00000')
        if (item.indexOf(",") > -1 && _searchInfo[item]) {
          item.split(",").forEach((key, index) => {
            _searchInfo[key] = _searchInfo[item][index];
          });
          delete _searchInfo[item];
        }
      });
      const searchInfo2 = JSON.parse(JSON.stringify(_searchInfo));
      return searchInfo2;
    },
    gotoDetail(row = {}) {
      this.$refs.LineModal.dialogVisible = true;
      this.$refs.LineModal.detail = row;
      this.$nextTick(() => {
        const query = this.changeInfoData(row);
        // console.log(query,'query')
        this.$refs.LineModal.getData(query);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}

::v-deep .el-form-item__label {
  font-size: 14px;
  line-height: 15px;
  margin-top: 10px;
  color: #666;
  font-weight: 400;
}

.detailText {
  cursor: pointer;
  color: #1890ff;
}
</style>
