export const columns = [
  {
    label: "扫码单据",
    type: "input",
    prop: "docNo",
  },

  {
    label: "单据类型",
    prop: "docType",
    type: "select",
    optionsCode: "doc_type",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "docType",
      },
    ],
  },

  {
    label: "装运点",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    // slotName:'vstel',
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },
  {
    label: "客户",
    type: "input",
    prop: "kunnr",
    dictOptionCode: "KUNNR",
    append: true,
    attr: {
      // disabled:true
    },
    backProps: [
      {
        backProp: "NAME1",
        backLable: "NAME1",
        prop: "kunnr",
      },
    ],
  },

  {
    prop: "checkFlag",
    label: "需要扫码",
    type: "select",
    optionsCode: "scan_flag",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "checkFlag",
      },
    ],
  },

  {
    label: "已全部扫码",
    type: "input",
    prop: "allScan",
    optionsCode: "scan_flag",
    type: "select",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "allScan",
      },
    ],
  },

  {
    prop: "caozuo",
    label: "操作",
    // width: 140,
    className: "caozuo",
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
];

export const searchData = JSON.parse(JSON.stringify(columns)).map((item) => {
  if(!['docNo','werks','kunnr'].includes(item.prop)){
    item.hiddenSearch = true

  }
  item.attr = {
    disabled: true,
  };
  return item;
});

export const lineCloumn = [
  {
    label: "行号",
    type: "input",
    prop: "docLineNo",
  },
  {
    label: "专卖物料号",
    type: "input",
    prop: "zzmatnr",
  },

  {
    label: "物料描述",
    type: "input",
    prop: "zzmatnrName",
  },
  {
    label: "子库",
    type: "input",
    prop: "lgort",
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
      disabled: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },

  {
    label: "单据数量",
    type: "input",
    prop: "qty",
  },

  {
    label: "已扫数量",
    type: "input",
    prop: "scanQty",
  },
  {
    label: "已全部扫码",
    type: "input",
    prop: "scanFlag",
    optionsCode: "scan_flag",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "scanFlag",
      },
    ],
  },

  {
    prop: "caozuo1",
    label: "操作",
    // width: 60,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.attr = {
    disabled: true,
  };
  return item;
});

export const detailHeader = [
  {
    label: "单据编号",
    type: "input",
    prop: "docNo",
  },

  {
    label: "行号",
    type: "input",
    prop: "docLineNo",
  },
  {
    label: "库存地点",
    type: "input",
    prop: "lgort",
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
      disabled: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },
].map((item) => {
  item.attr = {
    disabled: true,
  };
  return item;
});

export const detailLine = [
  {
    label: "父条码",
    type: "input",
    prop: "barcode1",
    // fixed: "left",
  },
  {
    label: "条码",
    type: "input",
    prop: "barcode2",
    // fixed: "left",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "zzmatnr",
  },

  {
    label: "物料描述",
    type: "input",
    prop: "zzmatnrName",
  },
  {
    label: "单据数量",
    type: "input",
    prop: "docQty",
  },

  {
    label: "已扫数量",
    type: "input",
    prop: "qty",
  },

  {
    prop: "caozuo",
    label: "操作",
    // width: 60,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.attr = {
    disabled: true,
  };
  return item;
});

export const rules = {
  partProcessNum: [
    { required: true, message: "请输入零件加工编码", trigger: "change" },
  ],
};
export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};
export const btnProps = {
  add: {
    isShow: false,
  },
  batchDelete: {
    isShow: false,
  },
  deleteLine: {
    isShow: false,
  },
  uploadExcel: {
    isShow: false,
  },
  caozuoShow: {
    isShow: false,
  },
};
