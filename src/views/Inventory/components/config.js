
export const headerConfig = [
   {
    label: "客商单位",
    prop: "po",
  },
   {
    label: "来源单号",
    prop: "po",
  },
   {
    label: "接受号",
    prop: "po",
  },
   {
    label: "客商关联单号",
    prop: "po",
  },
   {
    label: "业务类型",
    prop: "po",
  },
   {
    label: "业务日期",
    prop: "po",
  },

   {
    label: "直发订单",
    prop: "po",
  },
   
].map((item)=>{
  item.type = 'text'
  return item
})


export const TableConfig = [
      {
    label: "序号",
    prop: "xuhao",
  },
  {
    label: "物料编码",
    prop: "ZZMATNR",
    // width:160
  },

  {
    label: "物料名称",
    prop: "MAKTX_ZH",
  },
  
  {
    label: "物料类别",
    prop: "MAKTX_ZH",
  },

  {
    label: "数量",
    prop: "KWMENG",
  },


   
  {
    label: "接收子库",
    prop: "MAKTX_ZH",
  },

  {
    label: "接收货位",
    prop: "KWMENG",
  },

  
  {
    label: "来源单行号",
    prop: "KWMENG",
  },

]


export const footerConfig = [
    {
    label: "主管",
    prop: "supervisor",
  },

 
  {
    label: "复核人",
    prop: "fuhe",
  },

   {
    label: "仓管",
    prop: "fuhe",
  },

  
   {
    label: "备注",
    prop: "fuhe",
    span: 14,
  },

    
   {
    label: "直发供应商名称",
    prop: "fuhe",
    span: 24,
  },

].map((item)=>{
  item.type = 'text'
  return item
})



export const getDetailRequest = {
  'moveing':'',
  allot:'',
  orderNote:'',
  purOrder:''
}