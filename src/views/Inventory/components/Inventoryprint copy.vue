<template>
  <div class="print-container">
    <!-- 打印内容区域 -->
    <div class="a5-page">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="main-title">A5格式8列表格打印文档</h1>
        <p class="subtitle">横向版布局 | 8列数据表格 | 内容自适应</p>
      </div>
      
      <!-- 正文内容 -->
      <div class="page-content">
        <p class="intro-text">
          这是一个包含8列数据表格的A5横向打印页面示例。表格设计为内容自适应，确保数据在不换行的情况下完整显示在A5纸张范围内（210mm × 148mm）。
        </p>
        
        <h2 class="section-title">详细数据表格</h2>
        
        <!-- 8列表格 -->
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>产品名称</th>
                <th>类别类别类别类别</th>
                <th>规格</th>
                <th>单位</th>
                <th>单价</th>
                <th>数量</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in tableData" :key="item.id">
                <td>{{ item.id }}</td>
                <td class="text-ellipsis">{{ item.name }}</td>
                <td>{{ item.category }}</td>
                <td>{{ item.spec }}</td>
                <td>{{ item.unit }}</td>
                <td>{{ item.price }}</td>
                <td>{{ item.quantity }}</td>
                <td :class="['status', item.statusClass]">{{ item.status }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <p class="additional-text">
          表格采用了内容自适应设计，通过调整字体大小和列宽确保8列数据在A5横向页面内完整显示，不发生内容分行。
        </p>
      </div>
      
      <!-- 底部文本 -->
      <div class="page-footer">
        <p>文档生成日期: {{ currentDate }}</p>
        <p>联系信息：<EMAIL> | 电话：400-123-4567</p>
        <p>第 1 / 1 页</p>
      </div>
    </div>
    
    <!-- 打印按钮 -->
    <div class="print-button-container">
      <button @click="printPage" class="print-button">
        <i class="fa fa-print"></i> 打印文档
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 8列表格数据
      tableData: [
        { id: 101, name: "高级办公椅", category: "家具", spec: "标准", unit: "把", price: "¥399", quantity: 24, status: "在售", statusClass: "available" },
        { id: 102, name: "无线鼠标", category: "电脑配件", spec: "蓝牙", unit: "个", price: "¥89", quantity: 156, status: "在售", statusClass: "available" },
        { id: 103, name: "高清显示器", category: "电脑设备", spec: "27寸", unit: "台", price: "¥1599", quantity: 8, status: "低库存", statusClass: "low-stock" },
        { id: 104, name: "USB-C数据线asasasasas", category: "配件", spec: "2米", unit: "条", price: "¥29", quantity: 320, status: "在售", statusClass: "available" },
        { id: 105, name: "机械键盘", category: "电脑配件", spec: "青轴", unit: "个", price: "¥299", quantity: 45, status: "在售", statusClass: "available" },
        { id: 106, name: "多功能打印机", category: "办公设备", spec: "打印/扫描", unit: "台", price: "¥1299", quantity: 3, status: "缺货", statusClass: "out-of-stock" },
        { id: 107, name: "移动硬盘", category: "存储设备", spec: "1TB", unit: "个", price: "¥499", quantity: 17, status: "在售", statusClass: "available" }
      ],
      currentDate: ""
    };
  },
  mounted() {
    // 设置当前日期
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    this.currentDate = new Date().toLocaleDateString('zh-CN', options);
    
    // 动态调整表格布局以确保不换行
    this.adjustTableLayout();
  },
  methods: {
    // 触发打印功能
    printPage() {
      window.print();
    },
    
    // 调整表格布局，确保内容不分行
    adjustTableLayout() {
      // 针对不同屏幕尺寸动态调整表格样式
      const adjustTable = () => {
        const table = document.querySelector('.data-table');
        if (!table) return;
        
        // 根据页面宽度调整字体大小
        const pageWidth = document.querySelector('.a5-page').offsetWidth;
        const fontSize = Math.max(6, Math.min(9, pageWidth / 40)) + 'pt';
        
        table.style.fontSize = fontSize;
      };
      
      // 初始调整
      adjustTable();
      
      // 窗口大小变化时重新调整
      window.addEventListener('resize', adjustTable);
      
      // 组件销毁时移除事件监听
      this.$once('hook:beforeDestroy', () => {
        window.removeEventListener('resize', adjustTable);
      });
    }
  }
};
</script>

<style scoped>
/* 基础样式 */
.print-container {
  font-family: 'Arial', 'SimHei', sans-serif;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

/* 打印按钮样式 */
.print-button-container {
  text-align: center;
  margin: 20px 0;
}

.print-button {
  background-color: #42b983;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.3s;
}

.print-button i {
  margin-right: 8px;
}

.print-button:hover {
  background-color: #359e75;
}

/* A5页面样式 - 横向 */
.a5-page {
  width: 210mm;  /* A5横向宽度 */
  min-height: 148mm;  /* A5横向高度 */
  padding: 10mm;  /* 减少内边距以容纳8列表格 */
  margin: 0 auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background-color: white;
  position: relative;
  overflow: hidden;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  margin-bottom: 8mm;
  padding-bottom: 3mm;
  border-bottom: 1px solid #e0e0e0;
}

.main-title {
  font-size: 16pt;
  margin: 0 0 2mm 0;
  color: #333;
}

.subtitle {
  font-size: 9pt;
  color: #666;
  margin: 0;
}

/* 页面内容样式 */
.page-content {
  margin-bottom: 10mm;
}

.intro-text {
  font-size: 9pt;
  line-height: 1.5;
  margin: 0 0 6mm 0;
  color: #333;
}

.section-title {
  font-size: 12pt;
  margin: 0 0 4mm 0;
  color: #444;
}

.additional-text {
  font-size: 9pt;
  line-height: 1.5;
  margin: 6mm 0 0 0;
  color: #333;
}

/* 表格容器 - 用于处理溢出 */
.table-container {
  width: 100%;
  overflow: hidden; /* 确保表格不超出页面 */
  margin: 4mm 0;
}

/* 8列表格样式 */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 8pt; /* 较小的字体以适应8列 */
  table-layout: fixed; /* 固定表格布局，确保列宽均匀 */
}

.data-table th,
.data-table td {
  border: 1px solid #ddd;
  padding: 1.5mm;
  text-align: center;
  white-space: nowrap; /* 禁止文本换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 溢出内容用省略号表示 */
}

/* 为不同列设置适当宽度 */
.data-table th:nth-child(1),
.data-table td:nth-child(1) { width: 5%; }  /* ID */
.data-table th:nth-child(2),
.data-table td:nth-child(2) { width: 18%; } /* 产品名称 */
.data-table th:nth-child(3),
.data-table td:nth-child(3) { width: 12%; } /* 类别 */
.data-table th:nth-child(4),
.data-table td:nth-child(4) { width: 12%; } /* 规格 */
.data-table th:nth-child(5),
.data-table td:nth-child(5) { width: 8%; }  /* 单位 */
.data-table th:nth-child(6),
.data-table td:nth-child(6) { width: 12%; } /* 单价 */
.data-table th:nth-child(7),
.data-table td:nth-child(7) { width: 10%; } /* 数量 */
.data-table th:nth-child(8),
.data-table td:nth-child(8) { width: 15%; } /* 状态 */

.data-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.data-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* 文本溢出处理 */
.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 状态样式 */
.status {
  padding: 0.5mm 1mm;
  border-radius: 2px;
  font-weight: bold;
  font-size: 7pt;
  display: inline-block;
  min-width: 40px;
}

.status.available {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status.out-of-stock {
  background-color: #ffebee;
  color: #c62828;
}

.status.low-stock {
  background-color: #fff8e1;
  color: #ff8f00;
}

/* 页脚样式 */
.page-footer {
  position: absolute;
  bottom: 8mm;
  left: 10mm;
  right: 10mm;
  font-size: 7pt;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.page-footer p {
  margin: 0.5mm 0;
}

/* 打印样式设置 */
@media print {
  /* 隐藏打印按钮 */
  .print-button-container {
    display: none;
  }
  
  /* 设置纸张为A5横向 */
  @page {
    size: A5 landscape;
    margin: 10mm;
  }
  
  /* 确保打印内容适应页面 */
  body, .print-container {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: white;
  }
  
  .a5-page {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    box-shadow: none;
    page-break-after: always;
  }
  
  /* 确保背景色打印正常 */
  .data-table th,
  .data-table tr:nth-child(even),
  .status {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* 打印时确保表格不换行 */
  .data-table td, .data-table th {
    white-space: nowrap !important;
    font-size: 7pt !important;
  }
}
</style>
    