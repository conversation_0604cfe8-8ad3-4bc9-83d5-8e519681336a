<template>
  <printA
    ref="printA"
    :headerConfig="headerConfig"
    :TableConfig="TableConfig"
    :footerConfig="footerConfig"
    :tableFooterConfig="[]"
    :printInfoData="printInfoData"
    :type="'inventory'"
  />
</template>

<script>
import { printInfo } from "@/api/sales/salesDelivery/salesDelivery";
import printA from "@/components/Print/printA.vue";
import { headerConfig, TableConfig, footerConfig,getDetailRequest } from "./config";

export default {
  data() {
    return {
      headerConfig,
      TableConfig,
      footerConfig,
      type:''
    };
  },
  components: {
    printA,
  },
  methods: {
    printInfoData(params){

      const fun = getDetailRequest[this.type]

      return printInfo(params)
    },
    printInfo(options) {
      const { ids,title,type = '' } = options
      this.type = type
      this.$refs.printA.printInfo(ids, 'inventory',title);
      this.$refs.printA.printDialogVisible = true;

      // this.$refs.printA.type='money'
    },
  },
};
</script>


<style scoped>

::v-deep .UniformCss{
  /* margin-top: 10px; */
}


</style>
