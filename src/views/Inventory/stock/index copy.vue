<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料号" prop="MATNR">
        <el-input
          v-model="queryParams.MATNR"
          placeholder="请输入物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专卖物料号" prop="ZZMATNR">
        <el-input
          v-model="queryParams.ZZMATNR"
          placeholder="请输入专卖物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="LIFNR">
        <el-input
          v-model="queryParams.LIFNR"
          placeholder="请输入供应商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inventory:inventory:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inventory:inventory:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inventory:inventory:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inventory:inventory:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inventoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="inventoryId" />
      <el-table-column label="物料号" align="center" prop="MATNR" />
      <el-table-column label="专卖物料号" align="center" prop="ZZMATNR" />
      <el-table-column label="物料描述" align="center" prop="MAKTX" />
      <el-table-column label="工厂" align="center" prop="WERKS" />
      <el-table-column label="库存地点" align="center" prop="LGORT" />
      <el-table-column label="供应商" align="center" prop="LIFNR" />
      <el-table-column label="基本单位" align="center" prop="MEINS" />
      <el-table-column label="非限制库存/销售订单库存总数" align="center" prop="LABST" />
      <el-table-column label="已冻结库存" align="center" prop="SPEME" />
      <el-table-column label="限制库存" align="center" prop="EINME" />
      <el-table-column label="在途库存" align="center" prop="UMLME" />
      <el-table-column label="销售订单库存/单个销售订单库存" align="center" prop="LABST1" />
      <el-table-column label="销售订单号" align="center" prop="VBELN" />
      <el-table-column label="销售订单行项目" align="center" prop="POSNR" />
      <el-table-column label="客户编码" align="center" prop="KUNNR" />
      <el-table-column label="客户描述" align="center" prop="NAME" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inventory:inventory:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inventory:inventory:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改库存信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="物料号" prop="MATNR">
          <el-input v-model="form.MATNR" placeholder="请输入物料号" />
        </el-form-item>
        <el-form-item label="专卖物料号" prop="ZZMATNR">
          <el-input v-model="form.ZZMATNR" placeholder="请输入专卖物料号" />
        </el-form-item>
        <el-form-item label="物料描述" prop="MAKTX">
          <el-input v-model="form.MAKTX" placeholder="请输入物料描述" />
        </el-form-item>
        <el-form-item label="库存地点" prop="LGORT">
          <el-input v-model="form.LGORT" placeholder="请输入库存地点" />
        </el-form-item>
        <el-form-item label="供应商" prop="LIFNR">
          <el-input v-model="form.LIFNR" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="基本单位" prop="MEINS">
          <el-input v-model="form.MEINS" placeholder="请输入基本单位" />
        </el-form-item>
        <el-form-item label="非限制库存/销售订单库存总数" prop="LABST">
          <el-input v-model="form.LABST" placeholder="请输入非限制库存/销售订单库存总数" />
        </el-form-item>
        <el-form-item label="已冻结库存" prop="SPEME">
          <el-input v-model="form.SPEME" placeholder="请输入已冻结库存" />
        </el-form-item>
        <el-form-item label="限制库存" prop="EINME">
          <el-input v-model="form.EINME" placeholder="请输入限制库存" />
        </el-form-item>
        <el-form-item label="在途库存" prop="UMLME">
          <el-input v-model="form.UMLME" placeholder="请输入在途库存" />
        </el-form-item>
        <el-form-item label="销售订单库存/单个销售订单库存" prop="LABST1">
          <el-input v-model="form.LABST1" placeholder="请输入销售订单库存/单个销售订单库存" />
        </el-form-item>
        <el-form-item label="销售订单号" prop="VBELN">
          <el-input v-model="form.VBELN" placeholder="请输入销售订单号" />
        </el-form-item>
        <el-form-item label="销售订单行项目" prop="POSNR">
          <el-input v-model="form.POSNR" placeholder="请输入销售订单行项目" />
        </el-form-item>
        <el-form-item label="客户编码" prop="KUNNR">
          <el-input v-model="form.KUNNR" placeholder="请输入客户编码" />
        </el-form-item>
        <el-form-item label="客户描述" prop="NAME">
          <el-input v-model="form.NAME" placeholder="请输入客户描述" />
        </el-form-item>
        <el-form-item label="删除标识" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标识" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInventory, getInventory, delInventory, addInventory, updateInventory } from "@/api/inventory/stock";

export default {
  name: "Inventory",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库存信息表格数据
      inventoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        MATNR: null,
        ZZMATNR: null,
        WERKS: null,
        LIFNR: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询库存信息列表 */
    getList() {
      this.loading = true;
      listInventory(this.queryParams).then(response => {
        this.inventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inventoryId: null,
        MATNR: null,
        ZZMATNR: null,
        MAKTX: null,
        WERKS: null,
        LGORT: null,
        LIFNR: null,
        MEINS: null,
        LABST: null,
        SPEME: null,
        EINME: null,
        UMLME: null,
        LABST1: null,
        VBELN: null,
        POSNR: null,
        KUNNR: null,
        NAME: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.inventoryId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加库存信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const inventoryId = row.inventoryId || this.ids
      getInventory(inventoryId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库存信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.inventoryId != null) {
            updateInventory(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInventory(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const inventoryIds = row.inventoryId || this.ids;
      this.$modal.confirm('是否确认删除库存信息编号为"' + inventoryIds + '"的数据项？').then(function() {
        return delInventory(inventoryIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inventory/inventory/export', {
        ...this.queryParams
      }, `inventory_.xlsx`)
    }
  }
};
</script>
