<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 6, ...searchProps }"
      :tableProps="tableProps"
      :isPage="false"
      ref="stock"
    >
      <template #searchButton>
        <el-button
          ize="mini"
          @click="handleExport"
          type="warning"
          plain
          icon="el-icon-download"
          >导出</el-button
        >
      </template>
      <template #SPEME="{ row }">
        <a class="link-type" @click="open('SPEME', row)">{{ row.SPEME }}</a>
      </template>
      <template #LABST1="{ row }">
        <a class="link-type" @click="open('LABST1', row)">{{ row.LABST1 }}</a>
      </template>
    </ComTableList>
    <ComModalTable
      ref="moveComModalTable"
      title="移库/调拨锁定库存"
      :searchData="moveSeachData"
      :column="moveColums"
      :getTableList="getMoveList"
    >
    </ComModalTable>
    <ComModalTable
      ref="lockComModalTable"
      title="销售锁定库存"
      :searchInfo="lockSearchInfo"
      :isPage="false"
      :searchProps="searchProps"
      :searchData="lockSearch"
      :column="columnlock"
      :getTableList="getlockList"
    >
    </ComModalTable>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";
import { exportExcel } from "@/utils/request.js";
import { queryInventory } from "@/api/inventory/stock";
import { dictOptionCodeKey } from "@/utils/index.js";
import {
  searchData,
  columns,
  moveColums,
  moveSeachData,
  getMoveListData,
  lockSearch,
  getLockSearch,
  columnlock,
  lookRules,
} from "./config";

export default {
  name: "Stock",
  components: {
    ComTableList,
    ComModalTable,
  },
  dicts: ["meins"],
  dictsKey: dictOptionCodeKey(searchData),
  data() {
    return {
      lockSearch,
      moveColums,
      moveSeachData,
      columns,
      searchData: searchData,
      columnlock,
      innerVisible: false,
      lockSearchInfo: {},
      tableProps: {
        height: 460,
        // rowKey:'id',
      },
      searchProps: {
        rules: {
          // WERKS: [{ required: true, message: "请选择工厂", trigger: "change" }],
          // BUKRS:[{ required: true, message: "请选择公司", trigger: "change" }],
        },
      },
      searchProps2: {
        // rules: lookRules,
      },
    };
  },
  created() {},
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      const res = await queryInventory({
        action: "SDI035",
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        inventoryInfos: [
          {
            ...params,
          },
        ],
      });
      if (res.data) {
        res.rows = res.data?.data || [];
      }
      return res;
    },
    async getMoveList() {
      return {
        code: 200,
        rows: getMoveListData(),
        total: getMoveListData().length,
      };
    },
    async getlockList() {
      return {
        code: 200,
        rows: getLockSearch(),
        total: getLockSearch().length,
      };
    },
    open(type, data) {
      if (type === "SPEME") {
        this.$refs.moveComModalTable.dialogVisible = true;
      } else {
        this.$refs.lockComModalTable.dialogVisible = true;
        this.lockSearchInfo = data;
        this.$refs.lockComModalTable.$refs?.ComTableList?.submitEmits(data);
      }
    },
    async getlockList(params) {
      const _searchInfo = { ...params };
      Object.keys(_searchInfo).forEach((item) => {
        // console.log(item,_searchInfo,'00000')
        if (item.indexOf(",") > -1 && _searchInfo[item]) {
          item.split(",").forEach((key, index) => {
            _searchInfo[key] = _searchInfo[item][index];
          });
          delete _searchInfo[item];
        }
      });
      const searchInfo2 = JSON.parse(JSON.stringify(_searchInfo));

      const res = await queryInventory({
        action: "SDI019",
        inventoryInfos: [
          {
            ...searchInfo2,
          },
        ],
      });

      if (res.data) {
        res.rows = res.data?.data || [];
      }
      return res;
    },
    /** 导出按钮操作 */
    async handleExport() {
      // const params = this.$refs.stock.$refs.searchComTableInfo.changeInfoData();
      const tableData = this.$refs.stock.tableData;
      if (!tableData || tableData.length === 0) {
        // await this.$refs.stock.$refs.searchComTableInfo.onSubmit("search");
        this.$message({
          type: "error",
          message: "暂无数据导出，请先点击查询获取数据",
        });
      }

      const tableData2 = this.$refs.stock.tableData;
      if (!tableData2 || tableData2.length === 0) return;
      exportExcel(this.columns, tableData2, "库存查询");
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .lineBtnright {
  top: 0;
  right: 0;
}
</style>
