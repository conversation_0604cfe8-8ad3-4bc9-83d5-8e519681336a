import { WERKSOPtions } from "../../sales/demand/config";
export const columns = [
  //  {
  //   label: "物料号",
  //   type: "input",
  //   prop: "MATNR",
  // },
  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },

  {
    label: "物料描述",
    type: "input",
    prop: "MAKTX",
  },

  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "BUKRS",
      },
    ],
  
  },

  {
    label: "库存地点",
    type: "input",
    prop: "LGORT",
    dictOptionCode: "LGORT",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  // {
  //   label: "物料编码",
  //   type: "input",
  //   prop: "ZZMATNR",
  // },
  // {
  //   label: "图号",
  //   type: "input",
  //   prop: "",
  // },
  {
    label: "供应商",
    type: "input",
    prop: "LIFNR",
    dictOptionCode: "LIFNR",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "LIFNR",
        backLable: "NAME1",
        prop: "LIFNR",
      },
    ],
  },
  // {
  //   label: "专卖物料号",
  //   type: "input",
  //   prop: "ZZMATNR",
  // },

  {
    label: "基本单位",
    type: "input",
    prop: "MEINS",
    optionsCode: "meins",
    backProps: [
      {
        backProp: "value",
        backLable: "label",
        prop: "MEINS",
      },
    ],
  },
  {
    label: "非限制库存",
    type: "input",
    prop: "LABST",
  },
  {
    label: "已冻结库存(销售锁定库存)",
    type: "input",
    prop: "LABST1",
    slotName: "LABST1",
  },
  {
    label: "限制库存(移库/调拨锁定库存)",
    type: "input",
    prop: "SPEME",
    slotName: "SPEME",
    width:200
  },
  // {
  //   label: "在途库存",
  //   type: "input",
  //   prop: "UMLME",
  // },
  // {
  //   label: "销售订单库存",
  //   type: "input",
  //   prop: "LABST1",
  // },
];

export const searchData = [
  {
    label: "公司",
    type: "input",
    prop: "BUKRS",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "BUKRS",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },
  ...columns.slice(0, 5),
];

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const moveSeachData = [
  {
    label: "库存地点",
    type: "input",
    prop: "LGORT",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
  },
];

export const moveColums = [
  {
    label: "行号",
    type: "input",
    prop: "POSNR",
  },
  {
    label: "物料号",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "库存地点",
    type: "input",
    prop: "",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "移库数量",
    type: "input",
    prop: "KWMENG",
  },

  {
    prop: "WERKS",
    label: "发货工厂",
  },

  {
    label: "收货工厂",
    prop: "UMWRK",
    width: 120,
  },
];

export const getMoveListData = () => {
  const columnsObj = {};
  moveColums.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lockSearch = [
  {
    label: "库存地点",
    type: "input",
    prop: "LGORT",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
    type: "select",
    options: WERKSOPtions,
  },
];

const rulesObj = {};
lockSearch.forEach((item) => {
  rulesObj[item.prop] = [
    { required: true, message: `请输入${item.label}`, trigger: "blur" },
  ];
});

export const lookRules = rulesObj;

export const columnlock = [
  "物料号	MATNR",
  "专卖物料号	ZZMATNR",
  "物料描述	MAKTX",
  "工厂	WERKS",
  "库存地点	LGORT",
  "基本单位	MEINS",
  "销售订单库存总数	LABST",
  "销售订单号	VBELN",
  // "销售订单行项目	POSNR",
  "单个销售订单库存	LABST1",
  "客户编码	KUNNR",
  "客户描述	NAME",
].map((item) => {
  let [label, value] = item.split("	");
  let obj = {};

  if (value === "WERKS") {
    obj = {
      dictOptionCode: "VKORG",
      backProps: [
        {
          backProp: "werks",
          backLable: "werksName",
          prop: "WERKS",
        },
      ],
    };
  }
  if (value === "LGORT") {
    obj = {
      dictOptionCode: "LGORT",
      backProps: [
        {
          backProp: "lgort",
          backLable: "lgortName",
          prop: "LGORT",
        },
      ],
    };
  }

  return {
    ...obj,
    prop: value,
    label,
  };
});
export const getLockSearch = () => {
  const columnsObj = {};
  lockSearch.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};
