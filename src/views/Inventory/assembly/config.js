import { getRequestData } from "@/components/ComSearch/config.js";
import { getaddSearch } from "@/components/ComSearch/utils.js";
import { c_list } from "@/components/LineTable/units.js";
export const columns = [
  {
    label: "SAS组装单号",
    type: "input",
    prop: "assemblySheetNo",
    attr: {
      disabled: true,
    },
  },
  {
    label: "SAP组装单号",
    type: "input",
    prop: "ebeln",
    attr: {
      disabled: true,
    },
  },
  {
    label: "公司",
    type: "input",
    prop: "bukrs",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "company",
        backLable: "companyName",
        prop: "bukrs",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },
  {
    label: "工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "company",
        backLable: "companyName",
        prop: "bukrs",
      },
      // {
      //   backProp: "lifnr",
      //   backLable: "lifnr",
      //   prop: "lifnr",
      // },
    ],
  },
  {
    label: "采购组",
    type: "select",
    prop: "ekgrp",
    optionsCode: "ekgrp",
    slotName: "ekgrp",
    // backProps: [
    //   {
    //     backProp: "value",
    //     backLable: "label",
    //     prop: "ekgrp",
    //   },
    // ],
  },

  {
    label: "组装单备注",
    type: "input",
    prop: "sasRemark",
    searchOut: true,
    width: 300,
  },

  {
    label: "组装件入库库存地点",
    type: "input",
    prop: "lgort",
    dictOptionCode: "LGORT",
    append: true,
    // width: 300,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },

  {
    label: "子件出库库存地点",
    type: "input",
    prop: "UMLGO",
    dictOptionCode: "LGORT",
    append: true,
    // width: 300,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "UMLGO",
      },
    ],
  },

  {
    label: "组装单状态",
    type: "select",
    prop: "docStatus",
    fixed: "right",
    optionsCode: "sas_spzt",
    slotName: "docStatus",
    //  width:120,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "caozuo",
    label: "操作",
    width: 200,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.width = item.width ? item.width : 140;
  if (item.prop === "sasRemark") {
    item.width = undefined;
  }
  return item;
});

// 映射code
export const optionsCodeList = columns.filter(({ optionsCode }) => optionsCode);

const changeLineItem = (val, editItem, self) => {
  let num = val * 1;
  let value2 = editItem.zzzcMengeBom * 1;
  const falge = Number.isInteger(num / value2);

  //  组装件数量必须是最小组装数量的整数倍
  if (val && (num < value2 || !falge)) {
    return editItem;
  }
  if (editItem.children) {
    let arr = editItem.children;
    let zzzcMengeBom = editItem.zzzcMengeBom * 1 || 0;
    let peishu = val / zzzcMengeBom;

    // console.log(zzzcMengeBom,peishu,val,'ddddddddddddddd')
    let newList = arr.map((item) => {
      item.zzzcMenge = item.zzzjMenge *1 * peishu;
      return item;
    });
    editItem.zzzcMenge = num;
    editItem.OldZzzcMenge = num;
    editItem.children = newList;
  }

  return editItem;
};

export const lineCloumn = [
  {
    label: "",
    prop: "xuhao2",
    fixed: "left",
    width: 60,
    attr: {
      disabled: true,
    },
  },
  {
    label: "组装件物料号/组装件子件物料号",
    prop: "zzzcZzmatnr",
    propList: ["zzzcZzmatnr", "zzzjZzmatnr"],
    append: true,
    width: 300,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "zzzcZzmatnr",
        backLable: "zzzcZzmatnr",
        prop: "zzzcZzmatnr",
      },

      {
        backProp: "zzzcMengeBom",
        backLable: "zzzcMengeBom",
        prop: "zzzcMenge",
      },
      {
        backProp: "zzzcMengeBom",
        backLable: "zzzcMengeBom",
        prop: "OldZzzcMenge",
      },
      {
        backProp: "zzzcMengeBom",
        backLable: "zzzcMengeBom",
        prop: "zzzcMengeBom",
      },
      {
        backProp: "meins",
        backLable: "meins",
        prop: "meins",
      },
    ],
    isSetChildren: true,
    // 修改组装件件的子级
    changechildParams: (params, rowsList, self) => {
      let initLineData = self.dataProps.initLineData;
      const rows = rowsList.filter(
        ({ zzzcZzmatnr }) => params.zzzcZzmatnr === zzzcZzmatnr
      );
      if (rows && rows.length) {
        params.children = rows.map((item, index) => {
          let time = new Date();
          const id = `${time.getTime()}`.slice(4);
          item.assemblySheetLineId = `c${id}${index}`;
          item.zzzcZzmatnr = item.zzzjZzmatnr;
          item.zzzcMenge = item.zzzjMenge;
          item.zzzjMenge = item.zzzjMenge;
          item.zzzjxh = item.ZZZJXH;

          item.lgort = initLineData.UMLGO ? initLineData.UMLGO : "";
          item["lgort,lgortName"] = initLineData.UMLGO
            ? initLineData["UMLGO,lgortName"]
            : "";
          return item;
        });
      }
      return params;
    },
    otherBind: (item, row, self) => {
      if (item.prop === "zzzcZzmatnr" && !row?.children) {
        return {
          disabled: true,
        };
      } else {
        return {};
      }
    },
  },

  {
    label: "组装件组装数量/组装件子件数量",
    type: "number",
    prop: "zzzcMenge",
    propList: ["zzzcMenge", "zzzjMenge"],
    width: 200,
    isSetChildren: true,
    blur: (item, row, self) => {
      if (row.zzzcMenge && row.children) {
        let num = row.zzzcMenge * 1;
        let value2 = row.zzzcMengeBom * 1;
        const falge = Number.isInteger(num / value2);
        if (num && (num < value2 || !falge)) {
          // self.$modal.msgError("组装件数量必须是最小组装数量的整数倍");
          // console.log(self,value2,'00000')
          row.zzzcMenge = row.OldZzzcMenge || value2;
        }
      }
    },
    inputChange: (val, oldVal, self) => {
      const { row } = self;
      let num = val * 1;
      let value2 = row.zzzcMengeBom * 1;
      const falge = Number.isInteger(num / value2);
      if (!falge) {
        self.$modal.msgError("组装件数量必须是最小组装数量的整数倍");
      }
      return val;
    },
    changeLineItem: (val, item, editItem, self) => {
      return changeLineItem(val, editItem, self);
    },
    otherBind: (item, row, self) => {
      if (item.prop === "zzzcMenge" && !row?.children) {
        return {
          disabled: true,
        };
      } else {
        return {};
      }
    },
  },

  {
    label: "组装件入库库存地点/子件出库库存地点",
    type: "input",
    prop: "lgort",
    dictOptionCode: "LGORT",
    propList: ["lgort", "UMLGO"],
    append: true,
    width: 300,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },
  {
    prop: "zzzjxh",
    label: "组装件子件序号",
    width: 120,
    attr: {
      disabled: true,
    },
  },
  {
    label: "组装件最小组装数量",
    // type: "number",
    width: 200,
    prop: "zzzcMengeBom",
    attr: {
      disabled: true,
    },
  },
  {
    prop: "meins",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
    optionsCode: "meins",
  },
];
const lgortBackProps = [
  {
    prop: "lgort",
    label: "组装件入库库存地点",
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "lgort",
      },
    ],
  },
  {
    prop: "UMLGO",
    label: "子件出库库存地点",
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "UMLGO",
      },
    ],
  },
];


export const lineBomColumns = [
  {
    label: "组装件物料号",
    prop: "zzzcZzmatnr",
  },
  {
    label: "组装件组装数量",
    prop: "zzzcMenge",
  },
  ...lgortBackProps,
  ...c_list
]

export const columns2 = JSON.parse(JSON.stringify(columns)).map((item) => {
  item.attr = {
    disabled: true,
  };
  return item;
});

export const lineCloumn2 = JSON.parse(JSON.stringify(lineCloumn)).map(
  (item) => {
    item.attr = {
      disabled: true,
    };
    return item;
  }
);

export const changeProp = {
  zzzcZzmatnr: "zzzjZzmatnr",
  zzzcMengeBom: "zzzjMenge",
  lgort: "zzzjLgort",
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["master:AssemblySheet:lineAdd"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["master:AssemblySheet:linedel"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["master:AssemblySheet:linedel"],
  },
};

export const btnProps2 = {
  add: {
    isShow: false,
    hasPermi: ["master:AssemblySheet:lineAdd"],
  },
  batchDelete: {
    isShow: false,
    hasPermi: ["master:AssemblySheet:linedel"],
  },
  deleteLine: {
    isShow: false,
    hasPermi: ["master:AssemblySheet:linedel"],
  },
};

export const _columns = JSON.parse(JSON.stringify(columns));

export const rulesData = {};
_columns.forEach((item) => {
  if (
    ![
      "lgort",
      "UMLGO",
      "caozuo",
      "docStatus",
      "assemblySheetNo",
      "ebeln",
      "sasRemark",
      "ebelp",
    ].includes(item.prop)
  ) {
    let text = item?.type === "select" ? "请选择" : "请输入";
    let trigger = "blur";
    let arr = [
      { required: true, message: `${text}${item.label}`, trigger: trigger },
    ];
    // if (item.prop === "zzzcMengeBom") {
    //   arr = [{ validator: checkZzzcMenge, trigger: "blur" }]
    // }

    rulesData[item.prop] = arr;
  }
});

export const rules = rulesData;

// 需要接口请求的数据
export const dictsKey = _columns
  .filter(({ dictOptionCode }) => dictOptionCode)
  .map(({ dictOptionCode }) => dictOptionCode);

export const searchListData = JSON.parse(JSON.stringify(columns)).map(
  (item) => {
    item.attr = {};
    return item;
  }
);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [
    { ...columnsObj, id: "A" },
    { ...columnsObj, id: "B" },
  ];
};

export const changeLine = (list) => {
  const data = list.map((item) => {
    item.assemblySheetDetailsList = item.children.map((item2) => {
      return {
        ...item2,
        zzzjZzmatnr: item2.zzzcZzmatnr,
        zzzjMenge: item2.zzzcMenge,

        zzzjLgort: item2.lgort,
        zzzcZzmatnr: item.zzzcZzmatnr,

        menge: item2.zzzjMenge,
        meins: item2.meins,
        zzzcMenge: "",
        lgort: "",
        lgortName: "",
        assemblyPartId: undefined,
        assemblySheetLineId: item.assemblySheetHeadId
          ? item.assemblySheetLineId
          : undefined,
      };
    });

    if (!item.assemblySheetHeadId) {
      item.assemblySheetLineId = undefined;
    }

    return {
      ...item,
      children: undefined,
    };
  });

  return data;
};

export const changeResLine = (list, docStatus) => {
  if (Array.isArray(list)) {
    const data = [...list].map((item) => {
      if (item.assemblySheetDetailsList) {
        item.children = item.assemblySheetDetailsList.map((item2, index2) => {
          return {
            ...item2,
            assemblySheetLineId: `c${item.assemblySheetLineId}${index2}`,
            zzzjZzmatnr: undefined,
            zzzjMenge: undefined,
            zzzjLgort: undefined,
            zzzcZzmatnr: item2.zzzjZzmatnr,
            zzzcMenge: item2.zzzjMenge,
            lgort: item2.zzzjLgort,
            zzzjMenge: item2.menge,
            // meins:item.zmeins
          };
        });
      }
      return {
        ...item,
        OldZzzcMenge: item.zzzcMenge,
        id: docStatus === "draft" ? `${item.assemblySheetLineId}` : undefined,
      };
    });

    return data;
  }

  return list;
};

export const checkLine = (list) => {
  return list.filter((row) => {
    let num = row.zzzcMenge * 1;
    let value2 = row.zzzcMengeBom * 1;
    const falge = Number.isInteger(num / value2);
    return !falge;
  });
};

export const getZZLgort = async (list, self) => {
  try {
    // console.log(list, "list");
    const zz = [...new Set(list.map(({ zzzcZzmatnr }) => zzzcZzmatnr))];
    let arr = [];
    list.forEach((item) => {
      arr = [...arr, item.UMLGO, item.lgort];
    });

    let newData = [...list];

    const zzLgort = [...new Set(arr)];

    let fun = [];

    if (zz && zz.length) {
      const fun1 = getRequestData.zzzcZzmatnr({
        zzmatnrList: zz,
        pageNum: 1,
        pageSize: 10000,
      });

      fun.push(fun1);
    }

    if (zz && zz.length) {
      const fun2 = getRequestData.zzzzjLgort({
        lgortList: zzLgort,
        pageNum: 1,
        pageSize: 10000,
      });

      fun.push(fun2);
    }

    let results = await Promise.allSettled(fun);

    const results2 = results
      .filter(({ status }) => status === "fulfilled")
      .map(({ value }) => value);

    // console.log(results2, "results2");
    // const results3 = results
    //   .filter(({ status }) => status === "rejected")
    let zzzcZzmatnrList =
      results2.filter(({ key }) => key === "zzzcZzmatnr") || [];

    let LGORTList = results2.filter(({ key }) => key === "LGORT") || [];

    // console.log(zzzcZzmatnrList,LGORTList)
    if (
      zzzcZzmatnrList &&
      zzzcZzmatnrList.length &&
      zzzcZzmatnrList[0]?.rows &&
      zzzcZzmatnrList[0]?.rows?.length
    ) {
       const resRows = zzzcZzmatnrList[0]?.rows
      // const zzzcZzmatnrBackProps = lineCloumn.filter(
      //   ({ prop }) => prop === "zzzcZzmatnr"
      // )[0].backProps;

      newData = newData.map((newItem) => {
        let item = newItem;
        let _zzzcZzmatnrList = resRows.filter(
          (item2) => item2.zzzcZzmatnr === item.zzzcZzmatnr
        );
        if (!_zzzcZzmatnrList || _zzzcZzmatnrList.length === 0) {
          item.errMsg = `组装件物料号不存在,${item?.errMsg || ""}`;
        } else {
          let row = _zzzcZzmatnrList[0];

          let num = item.zzzcMenge * 1;
          let value2 = row.zzzcMengeBom * 1;
          const falge = Number.isInteger(num / value2);
          // console.log(row,num,value2,falge,'num')
          if (!falge) {
            item.errMsg = `组装件数量必须是最小组装数量${row?.zzzcMengeBom }的整数倍,${item?.errMsg || ""}`;
          }
          item = {
            ...item,
            ...row,
            itemList:_zzzcZzmatnrList
          };
        }
        return item;
      });
    } else {
      newData = newData.map((item) => {
        item.errMsg = `组装件物料号不存在,${item?.errMsg || ""}`;
        return item;
      });
    }

    if (
      LGORTList &&
      LGORTList.length &&
      LGORTList[0]?.rows &&
      LGORTList[0]?.rows?.length
    ) {
      const resRows = LGORTList[0]?.rows
      newData = newData.map((item2) => {
        let item = item2;
        lgortBackProps.forEach(({ prop, label, backProps }) => {
          let lgortData = resRows.filter((item2) => item2.lgort === item[prop]);

          if (!lgortData || lgortData.length === 0) {
            item.errMsg = `${label}不存在,${item?.errMsg || ""}`;
          } else {
            let row = lgortData[0];
            let obj = getaddSearch(prop, row, backProps);
            item = {
              ...item,
              ...obj,
            };
          }
        });
        return item;
      });
    } else {
      newData = newData.map((item) => {
        lgortBackProps.forEach(({ label }) => {
          item.errMsg = `${label}不存在,${item?.errMsg || ""}`;
        });
        return item;
      });
    }

    return newData;
  } catch (error) {
    console.log(error);
    return [];
  }
};

const getList = (excelData,tableList) => {
  const data = JSON.parse(JSON.stringify(excelData));
  const obj = {};
  [...lineCloumn,...c_list].forEach((item) => {
    obj[item.label] = item.prop;
    if (item.label?.indexOf("/") && item.propList) {
      let label = item.label.replaceAll(")", "");
      const [prop1, prop2] = item.propList;
      let labelAll = label.split("/");
      obj[labelAll[0]] = prop1;
      obj[labelAll[1]] = prop2;
    }
  });
  const _data = data.map((item) => {
    let obj2 = {};
    Object.keys(item).forEach((key) => {
      const prop = obj[key];
      obj2 = {
        ...obj2,
        [prop]: item[key],
      };
    });
    return obj2;
  });

  const newParantList = JSON.parse(JSON.stringify(_data));
  const azzzcZzmatnr = tableList.map(({zzzcZzmatnr})=>(zzzcZzmatnr))
  let arr = [...azzzcZzmatnr];
  let _newParantList = newParantList.map((item) => {

    if (!item?.zzzcZzmatnr) {
      item.errMsg = `组装件物料号不能为空`;
    } else if (item.zzzcZzmatnr && arr.includes(item.zzzcZzmatnr)) {
        item.errMsg = `组装件物料号已选择`;
    } else if (item.zzzcZzmatnr && !arr.includes(item.zzzcZzmatnr)) {
      arr.push(item.zzzcZzmatnr);
    }
    
     if (!item?.zzzcMenge) {
      item.errMsg = `${
        item?.errMsg ? item?.errMsg + "," : ""
      }组装件组装数量不能为空`;
    }
    if (!item?.lgort) {
      item.errMsg = `${
        item?.errMsg ? item?.errMsg + "," : ""
      }组装件入库库存地点不能为空`;
    }
    if (!item?.UMLGO) {
      item.errMsg = `${
        item?.errMsg ? item?.errMsg + "," : ""
      }子件出库库存地点不能为空`;
    }
    return item;
  });

  return _newParantList;
};

const setTableList = (ParantListData) => {

  let parantList = ParantListData;


  const newParantList = JSON.parse(JSON.stringify(parantList));
  let list = [...newParantList].map((item, index) => {
    let obj = {}
    let time = new Date();
    const id = `P${time.getTime()}${index}`;
    let _itemList = []
    const zzzcMenge = item?.zzzcMenge * 1
    const zzzcMengeBom=  item?.zzzcMengeBom * 1

    let peishu = zzzcMenge / zzzcMengeBom

    // console.log(peishu,zzzcMenge,zzzcMengeBom,'zzzcMengeBom')
      // 组装子集
    if (item.itemList && item.itemList.length) {
      _itemList = item.itemList.map((item2, index2) => {
        let obj2 = {}
        const id2 = `c${id}${index2}`;
        obj2.assemblySheetLineId = `c${id2}${index2}`;
        obj2.id = id2;
        obj2.zzzcZzmatnr = item2.zzzjZzmatnr;

        obj2.zzzcMenge = item2.zzzjMenge * 1 * peishu;
        obj2.zzzjMenge = item2.zzzjMenge

        obj2.zzzcMengeBom = item2.zzzcMengeBom * 1

        obj2.zzzjxh = item2.ZZZJXH;
        obj2.lgort = item.UMLGO;
        obj2.meins = item.meins
        obj2['lgort,lgortName'] = item['UMLGO,lgortName'];
        return obj2;
      });
    }


    obj.assemblySheetLineId = `P${id}${index}`;

    obj.id = id;
    obj.zzzcZzmatnr = item.zzzcZzmatnr;

    obj.lgort = item.lgort;
    obj['lgort,lgortName'] = item['lgort,lgortName'];

    obj.OldZzzcMenge = zzzcMenge;
    obj.zzzcMenge = zzzcMenge;

    obj.zzzjxh = undefined;
    obj.meins = item.meins

    obj.zzzcMengeBom = zzzcMengeBom;
    obj.children = _itemList || [];

    return obj;
  });


  return list;
};

export const getExcelId = async (excelData,tableList) => {
  const parantList = getList(excelData,tableList);

  const handList = parantList.filter(({ errMsg = "" }) => !errMsg);
  let errList = parantList.filter(({ errMsg = "" }) => errMsg);
  let aList = [];
  if (handList && handList.length) {
    const handList2 = await getZZLgort(handList);

    const handList3 = handList2.filter(({ errMsg = "" }) => !errMsg);

    let errList2 = handList2.filter(({ errMsg = "" }) => errMsg);

    aList = setTableList(handList3);
    errList = [...errList, ...errList2];
  }

  return [errList, aList];
};
