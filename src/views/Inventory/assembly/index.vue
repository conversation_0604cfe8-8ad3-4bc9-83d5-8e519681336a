<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchListData"
      :column="columns"
      :getTableList="getList"
      ref="ComTableList"
    >
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd('add', {})"
           v-hasPermi="['master:AssemblySheet:add']"
          >新增</el-button
        >
      </template>

      <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>


      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button size="mini" 
           v-hasPermi="['master:AssemblySheet:edit']"
          type="primary" @click="gotoAdd('edit', row)"
            >修改</el-button
          >
          <el-button 
           v-hasPermi="['master:AssemblySheet:query']"
          size="mini" type="primary" @click="gotoAdd('look', row)"
            >详情</el-button
          >
          <el-button 
           v-hasPermi="['master:AssemblySheet:remove']"
          size="mini" type="primary" @click="handleDelete(row)"
            >删除</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";

import { columns, dictsKey, searchListData,optionsCodeList } from "./config";
import {
  listAssemblySheet,
  getAssemblySheet,
  delAssemblySheet,
  addAssemblySheet,
  updateAssemblySheet,
} from "@/api/master/assemblySheet";

export default {
  name: "Assembly",
  components: {
    ComTableList,
  },
  dicts:['meins','ekgrp','sas_spzt',],
  dictsKey,
  data() {
    return {
      optionsCodeList,
      columns:columns.filter(({prop})=>(!['lgort','UMLGO'].includes(prop))),
      searchListData:searchListData.slice(0,4),
    };
  },
  created() {},
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return listAssemblySheet(params);
    },
    gotoAdd(type, data) {
      this.$store
        .dispatch("tagsView/delPage", { name: "AssemblyDetail" })
        .then(() => {
          this.$router.push(
            `/inventory/assembly-detail/index/${type}/${data.assemblySheetHeadId}`
          );
        });
    },
     /** 删除按钮操作 */
    handleDelete(row) {
      const assemblyPartIds = row.assemblySheetHeadId;
      this.$modal.confirm('是否确认删除编号为"' + assemblyPartIds + '"的数据项？').then(function() {
        return delAssemblySheet(assemblyPartIds);
      }).then(() => {
        // this.getList();
        this.$refs.ComTableList.submitEmits()
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
};
</script>
