<template>
  <div class="add" v-loading="loading">
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="8"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      :getSearchcallBack="getSearchcallBack"
      :closesearch="closesearch"
      @submitEmits="submitEmits"
      @handClickSearch="handClickSearch"
    >
      <template #searchButton v-if="pageType !== 'look'">
        <el-button
          v-hasPermi="['master:AssemblySheet:save']"
          v-if="['', 'draft'].includes(detail.docStatus)"
          type="primary"
          size="mini"
          @click="save('add')"
          >{{ detail.docStatus === "draft" ? "修改" : "保存" }}</el-button
        >
        <el-button
          v-hasPermi="['master:AssemblySheet:submit']"
          v-if="['E', 'draft'].includes(detail.docStatus)"
          type="primary"
          size="mini"
          @click="save('submit')"
          >提交</el-button
        >
        <el-button
          v-hasPermi="['master:AssemblySheet:finsh']"
          v-if="['CONF'].includes(detail.docStatus)"
          type="primary"
          size="mini"
          @click="save('ckApproved')"
          >完成</el-button
        >
        <el-button
          v-hasPermi="['master:AssemblySheet:cancel']"
          v-if="['CONF'].includes(detail.docStatus)"
          type="primary"
          size="mini"
          @click="save('cancel')"
          >取消</el-button
        >
      </template>
    </ComSearch>

    <LineTable
      :showSelection="
        !['CONF', 'cancel', 'approved'].includes(detail.docStatus)
      "
      :tableProps="tableProps"
      :indexXH="false"
      ref="lineTable"
      :btnProps="btnProps"
      :dataProps="dataProps"
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      keyId="assemblySheetLineId"
      POSNRKey="ebelp"
      :selectable="selectable"
      :requiredKeys="['zzzcZzmatnr', 'zzzcMenge', 'lgort']"
    >
      <template #lineTableHeader v-if="pageType !== 'look'">
        <UploadExcel
          v-if="['', 'draft'].includes(detail.docStatus)"
          v-hasPermi="['master:AssemblySheet:line:uploadExcel']"
          :checkUpload="checkUpload"
          @getUploadExcel="getUploadExcel"
        />
      </template>
    </LineTable>

    <ComModalTable
      ref="lineComModalSearch"
      :isPage="false"
      :column="modalColumn"
      :title="'导入信息提示'"
      :searchProps="{ showHeader: false }"
      @handenOk="errList = []"
    >
      <el-button type="primary" size="mini" @click.stop="excelDown"
        >导出excel</el-button
      >
    </ComModalTable>
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import ComModalSearch from "@/components/ComModalSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import Print from "@/components/Print/index.vue";
import UploadExcel from "@/components/UploadExcel/index.vue";
import { exportExcel, c_list } from "@/components/LineTable/units.js";
import { setTableDictValue } from "@/utils/index.js";
import {
  columns,
  dictsKey,
  rules,
  lineCloumn,
  btnProps,
  changeLine,
  changeResLine,
  lineCloumn2,
  columns2,
  btnProps2,
  checkLine,
  getExcelId,
  lineBomColumns
} from "./config";
import {
  assemblyColumns,
  searchListData,
} from "../../masterdata/assemblyPart/config";
import {
  getAssemblySheet,
  addAssemblySheet,
  updateAssemblySheet,
} from "@/api/master/assemblySheet";

export default {
  name: "AssemblyDetail",
  dictsKey,
  dicts: ["meins", "ekgrp", "sas_spzt"],
  components: {
    ComSearch,
    ComModalSearch,
    LineTable,
    Print,
    UploadExcel,
    ComModalTable: () =>
      import("@/components/ComModalSearch/ComModalTable.vue"),
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    // const newrules = this.setRules(rules);
    return {
      loading: false,
      searchInfo: {},
      rules: rules,
      searchData: columns,
      pageType,
      assemblyColumns,
      searchListData,
      detail: {
        docStatus: "",
      },
      lineList: [],
      lineCloumn,
      btnProps,
      tableProps: {
        defaultExpandAll: true,
        treeProps: { children: "children", hasChildren: "hasChildren" },
        rowKey: "assemblySheetLineId",
      },
      dataProps: {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
        },
      },
      errList: [],
    };
  },
  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
    modalColumn() {
      return lineBomColumns;
    },
  },
  mounted() {
    this.getDetail();
  },
  watch: {},
  methods: {
    checkUpload(){
      let info = this.$refs.ComSearchInfo?.dataInfo
      if(!info?.werks){
         this.$modal.msgError("请先选择工厂");
        return true
      }
      return false
    },
    getDetail() {
      if (this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getAssemblySheet(this.$route.params.id)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.detail = res.data;
            this.searchInfo = res.data;
            const list = changeResLine(
              res.data?.assemblySheetLineList,
              res.data.docStatus
            );
            if (["CONF", "cancel", "approved"].includes(res.data.docStatus)) {
              this.searchData = columns2;
              this.lineCloumn = lineCloumn2;
              this.btnProps = btnProps2;
            }else{
              this.searchData = columns;
              this.lineCloumn = lineCloumn;
              this.btnProps = btnProps;
            }

            this.$nextTick(() => {
              let rows2 = setTableDictValue(
                this.lineCloumn,
                list,
                this.dictDataOptions
              );
              this.lineList = rows2;
            });
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
    save(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    submitEmits(data, atype = "add") {
      let type = atype;
      let list = this.$refs.lineTable.newlineList;
      if (list.length === 0) {
        this.$modal.msgError("请添加行信息");
        return;
      }
      if (this.$refs.lineTable.checkList()) return;
      const checkLineList = checkLine(list);
      if (checkLineList && checkLineList.length) {
        return;
      }

      const fun = addAssemblySheet;

      const delLineIdList = this.$refs.lineTable.delLineIdList || [];

      const list1 = changeLine(list);
      const list2 = changeLine([...list, ...delLineIdList]);

      if (this.detail.assemblySheetHeadId && type === "add") {
        type = "upd";
      }
      if (type === "cancel") {
        type = "upd";
      }

      const params = {
        action: type,

        assemblySheetHead: {
          ...this.detail,
          ...data,
          assemblySheetLineList: type === "add" ? list1 : list2,
          addAssemblySheetLineList: [],
          docStatus: atype === "cancel" ? "cancel" : this.detail.docStatus,
        },
      };

      if (this.loading) return;
      this.loading = true;
      fun(params)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$modal.msgSuccess(res?.data?.msg || res?.msg || "操作成功");
            if (!this.detail.assemblySheetHeadId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "AssemblyDetail" })
                .then(() => {
                  this.$router.replace(
                    `/inventory/assembly-detail/index/edit/${res.data.assemblySheetHeadId}`
                  );
                });
            } else {
              this.getDetail();
            }
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
          this.getDetail()
        });
    },
    excelDown() {
      exportExcel(this.modalColumn, this.errList);
    },
    handClickSearch(item) {
      if (item.prop === "zzzcZzmatnr") {
        this.$refs.ComModalSearch.dialogVisible = true;
      }
    },
    selectable(row) {
      let keyValue = `${row?.assemblySheetLineId}`;
      if (keyValue?.indexOf("c") > -1) {
        return false;
      } else {
        return true;
      }
    },
    closesearch(backProps, prop) {
      if (prop === "UMLGO") {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            UMLGO: "",
            "UMLGO,lgortName": [],
          },
        };
      }

      if (prop === "lgort") {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            lgort: "",
            "lgort,lgortName": [],
          },
        };
      }
    },
    getSearchcallBack(params, activeProps) {
      // 根据库存地点回显行库存地点
      if (activeProps.prop === "lgort" && params[activeProps.prop]) {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            lgort: params.lgort,
            "lgort,lgortName": params["lgort,lgortName"],
          },
        };
        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        const lineList2 = [...this.$refs.lineTable.salesOrderLineList].filter(
          ({ lgort }) => !lgort
        );

        if (lineList && lineList.length && lineList2.length) {
          const list = lineList.map((item) => {
            return {
              ...item,
              lgort: item.lgort ? item.lgort : params.lgort,
              "lgort,lgortName": item.lgort
                ? item["lgort,lgortName"]
                : params["lgort,lgortName"],
            };
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }

      // 子库存地点回显
      if (activeProps.prop === "UMLGO" && params[activeProps.prop]) {
        this.dataProps = {
          ...this.dataProps,
          initLineData: {
            ...this.dataProps.initLineData,
            UMLGO: params.UMLGO,
            "UMLGO,lgortName": params["UMLGO,lgortName"],
          },
        };

        const lineList = [...this.$refs.lineTable.salesOrderLineList];
        if (lineList && lineList.length) {
          const list = lineList.map((item) => {
            const lineList2 = [...item.children].filter(({ lgort }) => !lgort);
            if (lineList2 && lineList2.length) {
              item.children = item.children.map((item2) => {
                return {
                  ...item2,
                  lgort: item2.lgort ? item2.lgort : params?.UMLGO || "",
                  "lgort,lgortName": item2.lgort
                    ? item2["lgort,lgortName"]
                    : params["UMLGO,lgortName"] || "",
                };
              });
            }

            return item;
          });
          this.$refs.lineTable.salesOrderLineList = list;
        }
      }
    },
    async getUploadExcel(val) {
      const tableList = [...this.$refs.lineTable.salesOrderLineList]
      const [errLists, data] = await getExcelId(val,tableList);
      console.log(errLists,data,'0000')
      this.errList = errLists;
      this.errList = errLists;
      if (errLists && errLists.length) {
        this.$refs.lineComModalSearch.open(errLists);
      }

      if (data && data.length) {
        const list = this.$refs.lineTable.salesOrderLineList;
        this.$refs.lineTable.salesOrderLineList = [...list, ...data];
        this.$refs.lineTable.total = [...list, ...data]?.length || 0;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
