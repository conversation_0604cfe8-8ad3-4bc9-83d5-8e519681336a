<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item label="组装件物料号" prop="zzzcZzmatnr">
        <el-input
          v-model="queryParams.zzzcZzmatnr"
          placeholder="请输入组装件物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组装件子件序号" prop="ZZZJXH">
        <el-input
          v-model="queryParams.ZZZJXH"
          placeholder="请输入组装件子件序号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组装件子件物料号" prop="zzzjZzmatnr">
        <el-input
          v-model="queryParams.zzzjZzmatnr"
          placeholder="请输入组装件子件物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:assemblyPart:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:assemblyPart:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:assemblyPart:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:assemblyPart:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assemblyPartList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="assemblyPartId" />
      <el-table-column label="组装件物料号" align="center" prop="zzzcZzmatnr" />
      <el-table-column label="组装件最小组装数量" align="center" prop="zzzcMengeBom" />
      <el-table-column label="组装件子件序号" align="center" prop="ZZZJXH" />
      <el-table-column label="组装件子件物料号" align="center" prop="zzzjZzmatnr" />
      <el-table-column label="组装件子件数量" align="center" prop="zzzjMenge" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:assemblyPart:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:assemblyPart:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改SAS组装件BOM信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="组装件物料号" prop="zzzcZzmatnr">
          <el-input v-model="form.zzzcZzmatnr" placeholder="请输入组装件物料号" />
        </el-form-item>
        <el-form-item label="组装件最小组装数量" prop="zzzcMengeBom">
          <el-input v-model="form.zzzcMengeBom" placeholder="请输入组装件最小组装数量" />
        </el-form-item>
        <el-form-item label="组装件子件序号" prop="ZZZJXH">
          <el-input v-model="form.ZZZJXH" placeholder="请输入组装件子件序号" />
        </el-form-item>
        <el-form-item label="组装件子件物料号" prop="zzzjZzmatnr">
          <el-input v-model="form.zzzjZzmatnr" placeholder="请输入组装件子件物料号" />
        </el-form-item>
        <el-form-item label="组装件子件数量" prop="zzzjMenge">
          <el-input v-model="form.zzzjMenge" placeholder="请输入组装件子件数量" />
        </el-form-item>
        <!-- <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAssemblyPart, getAssemblyPart, delAssemblyPart, addAssemblyPart, updateAssemblyPart } from "@/api/master/assemblyPart";

export default {
  name: "AssemblyPart",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // SAS组装件BOM信息表格数据
      assemblyPartList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        zzzcZzmatnr: null,
        ZZZJXH: null,
        zzzjZzmatnr: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询SAS组装件BOM信息列表 */
    getList() {
      this.loading = true;
      listAssemblyPart(this.queryParams).then(response => {
        this.assemblyPartList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        assemblyPartId: null,
        zzzcZzmatnr: null,
        zzzcMengeBom: null,
        ZZZJXH: null,
        zzzjZzmatnr: null,
        zzzjMenge: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.assemblyPartId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加SAS组装件BOM信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const assemblyPartId = row.assemblyPartId || this.ids
      getAssemblyPart(assemblyPartId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改SAS组装件BOM信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.assemblyPartId != null) {
            updateAssemblyPart(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssemblyPart(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const assemblyPartIds = row.assemblyPartId || this.ids;
      this.$modal.confirm('是否确认删除SAS组装件BOM信息编号为"' + assemblyPartIds + '"的数据项？').then(function() {
        return delAssemblyPart(assemblyPartIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/assemblyPart/export', {
        ...this.queryParams
      }, `assemblyPart_.xlsx`)
    }
  }
};
</script>
