<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="SAS组装单号" prop="zsasZzordeno">
        <el-input
          v-model="queryParams.zsasZzordeno"
          placeholder="请输入SAS组装单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="BUKRS">
        <el-input
          v-model="queryParams.BUKRS"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂" prop="WERKS">
        <el-input
          v-model="queryParams.WERKS"
          placeholder="请输入工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组装单供应商" prop="LIFNR">
        <el-input
          v-model="queryParams.LIFNR"
          placeholder="请输入组装单供应商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="子件出库库存地点" prop="zzzzjLgort">
        <el-input
          v-model="queryParams.zzzzjLgort"
          placeholder="请输入子件出库库存地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAP组装单号" prop="EBELN">
        <el-input
          v-model="queryParams.EBELN"
          placeholder="请输入SAP组装单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:assemblySheet:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:assemblySheet:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:assemblySheet:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:assemblySheet:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assemblySheetList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="assemblySheetId" />
      <el-table-column label="SAS组装单号" align="center" prop="zsasZzordeno" />
      <el-table-column label="公司代码" align="center" prop="BUKRS" />
      <el-table-column label="工厂" align="center" prop="WERKS" />
      <el-table-column label="总成件入库库存地点" align="center" prop="LGORT" />
      <el-table-column label="组装单状态" align="center" prop="zsasZzordeStatus" />
      <el-table-column label="组装单供应商" align="center" prop="LIFNR" />
      <el-table-column label="组装单行项目" align="center" prop="EBELP" />
      <el-table-column label="组装单备注" align="center" prop="zsasZzordertxt" />
      <el-table-column label="组装件物料号" align="center" prop="zzzcZzmatnr" />
      <el-table-column label="组装件最小组装数量" align="center" prop="zzzcMengeBom" />
      <el-table-column label="组装件子件序号" align="center" prop="ZZZJXH" />
      <el-table-column label="组装件子件物料号" align="center" prop="zzzjZzmatnr" />
      <el-table-column label="组装件子件数量" align="center" prop="zzzjMenge" />
      <el-table-column label="子件出库库存地点" align="center" prop="zzzzjLgort" />
      <el-table-column label="SAP组装单号" align="center" prop="EBELN" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:assemblySheet:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:assemblySheet:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改SAS组装单信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="SAS组装单号" prop="zsasZzordeno">
          <el-input v-model="form.zsasZzordeno" placeholder="请输入SAS组装单号" />
        </el-form-item>
        <el-form-item label="公司代码" prop="BUKRS">
          <el-input v-model="form.BUKRS" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="工厂" prop="WERKS">
          <el-input v-model="form.WERKS" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="总成件入库库存地点" prop="LGORT">
          <el-input v-model="form.LGORT" placeholder="请输入总成件入库库存地点" />
        </el-form-item>
        <el-form-item label="组装单供应商" prop="LIFNR">
          <el-input v-model="form.LIFNR" placeholder="请输入组装单供应商" />
        </el-form-item>
        <el-form-item label="组装单行项目" prop="EBELP">
          <el-input v-model="form.EBELP" placeholder="请输入组装单行项目" />
        </el-form-item>
        <el-form-item label="组装单备注" prop="zsasZzordertxt">
          <el-input v-model="form.zsasZzordertxt" placeholder="请输入组装单备注" />
        </el-form-item>
        <el-form-item label="组装件物料号" prop="zzzcZzmatnr">
          <el-input v-model="form.zzzcZzmatnr" placeholder="请输入组装件物料号" />
        </el-form-item>
        <el-form-item label="组装件最小组装数量" prop="zzzcMengeBom">
          <el-input v-model="form.zzzcMengeBom" placeholder="请输入组装件最小组装数量" />
        </el-form-item>
        <el-form-item label="组装件子件序号" prop="ZZZJXH">
          <el-input v-model="form.ZZZJXH" placeholder="请输入组装件子件序号" />
        </el-form-item>
        <el-form-item label="组装件子件物料号" prop="zzzjZzmatnr">
          <el-input v-model="form.zzzjZzmatnr" placeholder="请输入组装件子件物料号" />
        </el-form-item>
        <el-form-item label="组装件子件数量" prop="zzzjMenge">
          <el-input v-model="form.zzzjMenge" placeholder="请输入组装件子件数量" />
        </el-form-item>
        <el-form-item label="子件出库库存地点" prop="zzzzjLgort">
          <el-input v-model="form.zzzzjLgort" placeholder="请输入子件出库库存地点" />
        </el-form-item>
        <el-form-item label="SAP组装单号" prop="EBELN">
          <el-input v-model="form.EBELN" placeholder="请输入SAP组装单号" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAssemblySheet, getAssemblySheet, delAssemblySheet, addAssemblySheet, updateAssemblySheet } from "@/api/master/assemblySheet";

export default {
  name: "AssemblySheet",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // SAS组装单信息表格数据
      assemblySheetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        zsasZzordeno: null,
        BUKRS: null,
        WERKS: null,
        LIFNR: null,
        zzzzjLgort: null,
        EBELN: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询SAS组装单信息列表 */
    getList() {
      this.loading = true;
      listAssemblySheet(this.queryParams).then(response => {
        this.assemblySheetList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        assemblySheetId: null,
        zsasZzordeno: null,
        BUKRS: null,
        WERKS: null,
        LGORT: null,
        zsasZzordeStatus: null,
        LIFNR: null,
        EBELP: null,
        zsasZzordertxt: null,
        zzzcZzmatnr: null,
        zzzcMengeBom: null,
        ZZZJXH: null,
        zzzjZzmatnr: null,
        zzzjMenge: null,
        zzzzjLgort: null,
        EBELN: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.assemblySheetId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加SAS组装单信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const assemblySheetId = row.assemblySheetId || this.ids
      getAssemblySheet(assemblySheetId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改SAS组装单信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.assemblySheetId != null) {
            updateAssemblySheet(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssemblySheet(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const assemblySheetIds = row.assemblySheetId || this.ids;
      this.$modal.confirm('是否确认删除SAS组装单信息编号为"' + assemblySheetIds + '"的数据项？').then(function() {
        return delAssemblySheet(assemblySheetIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/assemblySheet/export', {
        ...this.queryParams
      }, `assemblySheet_.xlsx`)
    }
  }
};
</script>
