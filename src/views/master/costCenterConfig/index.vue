<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司代码" prop="bukrs">
        <el-input
          v-model="queryParams.bukrs"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="butxt">
        <el-input
          v-model="queryParams.butxt"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本中心编码
" prop="kostl">
        <el-input
          v-model="queryParams.kostl"
          placeholder="请输入成本中心编码
"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本中心短文本
" prop="ktext">
        <el-input
          v-model="queryParams.ktext"
          placeholder="请输入成本中心短文本
"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="负责人
" prop="verak">
        <el-input
          v-model="queryParams.verak"
          placeholder="请输入负责人
"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:costCenterConfig:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:costCenterConfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:costCenterConfig:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:costCenterConfig:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="costCenterConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="costCenterConfigId" />
      <el-table-column label="公司代码" align="center" prop="bukrs" />
      <el-table-column label="公司名称" align="center" prop="butxt" />
      <el-table-column label="成本中心编码
" align="center" prop="kostl" />
      <el-table-column label="成本中心短文本
" align="center" prop="ktext" />
      <el-table-column label="负责人
" align="center" prop="verak" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:costCenterConfig:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:costCenterConfig:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改成本中心配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司代码" prop="bukrs">
          <el-input v-model="form.bukrs" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="公司名称" prop="butxt">
          <el-input v-model="form.butxt" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="成本中心编码
" prop="kostl">
          <el-input v-model="form.kostl" placeholder="请输入成本中心编码
" />
        </el-form-item>
        <el-form-item label="成本中心短文本
" prop="ktext">
          <el-input v-model="form.ktext" placeholder="请输入成本中心短文本
" />
        </el-form-item>
        <el-form-item label="负责人
" prop="verak">
          <el-input v-model="form.verak" placeholder="请输入负责人
" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCostCenterConfig, getCostCenterConfig, delCostCenterConfig, addCostCenterConfig, updateCostCenterConfig } from "@/api/master/costCenterConfig";

export default {
  name: "CostCenterConfig",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 成本中心配置表格数据
      costCenterConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bukrs: null,
        butxt: null,
        kostl: null,
        ktext: null,
        verak: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询成本中心配置列表 */
    getList() {
      this.loading = true;
      listCostCenterConfig(this.queryParams).then(response => {
        this.costCenterConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        costCenterConfigId: null,
        bukrs: null,
        butxt: null,
        kostl: null,
        ktext: null,
        verak: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.costCenterConfigId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加成本中心配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const costCenterConfigId = row.costCenterConfigId || this.ids
      getCostCenterConfig(costCenterConfigId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改成本中心配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.costCenterConfigId != null) {
            updateCostCenterConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCostCenterConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const costCenterConfigIds = row.costCenterConfigId || this.ids;
      this.$modal.confirm('是否确认删除成本中心配置编号为"' + costCenterConfigIds + '"的数据项？').then(function() {
        return delCostCenterConfig(costCenterConfigIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/costCenterConfig/export', {
        ...this.queryParams
      }, `costCenterConfig_.xlsx`)
    }
  }
};
</script>
