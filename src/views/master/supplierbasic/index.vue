<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
      <el-form-item label="供应商编号" prop="LIFNR">
        <el-input
          v-model="queryParams.LIFNR"
          placeholder="请输入供应商编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户组" prop="KTOKK">
        <el-input
          v-model="queryParams.KTOKK"
          placeholder="请输入账户组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="税号" prop="STCEG">
        <el-input
          v-model="queryParams.STCEG"
          placeholder="请输入税号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="简称" prop="SORT1">
        <el-input
          v-model="queryParams.SORT1"
          placeholder="请输入简称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="简称" prop="SORT2">
        <el-input
          v-model="queryParams.SORT2"
          placeholder="请输入简称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管车平台服务站站号" prop="STCD3">
        <el-input
          v-model="queryParams.STCD3"
          placeholder="请输入管车平台服务站站号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="CRM系统服务站编号" prop="STCD4">
        <el-input
          v-model="queryParams.STCD4"
          placeholder="请输入CRM系统服务站编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:supplierbasic:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:supplierbasic:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:supplierbasic:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:supplierbasic:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="supplierbasicList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="supplierMasterBasicId" />
      <el-table-column label="供应商编号" align="center" prop="LIFNR" />
      <el-table-column label="名称 1" align="center" prop="NAME1" />
      <el-table-column label="账户组" align="center" prop="KTOKK" />
      <el-table-column label="账户组描述" align="center" prop="KTOKKTXT" />
      <el-table-column label="税号" align="center" prop="STCEG" />
      <el-table-column label="简称" align="center" prop="SORT1" />
      <el-table-column label="简称" align="center" prop="SORT2" />
      <el-table-column label="授权属性" align="center" prop="ZSQBS" />
      <el-table-column label="寄售/非寄售" align="center" prop="ZJSBS" />
      <el-table-column label="收货地址2" align="center" prop="STREET" />
      <el-table-column label="联系人" align="center" prop="NAME" />
      <el-table-column label="联系人电话" align="center" prop="TELF1" />
      <el-table-column label="联系人邮件" align="center" prop="smtpAddr" />
      <el-table-column label="管车平台服务站站号" align="center" prop="STCD3" />
      <el-table-column label="CRM系统服务站编号" align="center" prop="STCD4" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:supplierbasic:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:supplierbasic:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改供应商主数据基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="供应商编号" prop="LIFNR">
          <el-input v-model="form.LIFNR" placeholder="请输入供应商编号" />
        </el-form-item>
        <el-form-item label="名称 1" prop="NAME1">
          <el-input v-model="form.NAME1" placeholder="请输入名称 1" />
        </el-form-item>
        <el-form-item label="账户组" prop="KTOKK">
          <el-input v-model="form.KTOKK" placeholder="请输入账户组" />
        </el-form-item>
        <el-form-item label="账户组描述" prop="KTOKKTXT">
          <el-input v-model="form.KTOKKTXT" placeholder="请输入账户组描述" />
        </el-form-item>
        <el-form-item label="税号" prop="STCEG">
          <el-input v-model="form.STCEG" placeholder="请输入税号" />
        </el-form-item>
        <el-form-item label="简称" prop="SORT1">
          <el-input v-model="form.SORT1" placeholder="请输入简称" />
        </el-form-item>
        <el-form-item label="简称" prop="SORT2">
          <el-input v-model="form.SORT2" placeholder="请输入简称" />
        </el-form-item>
        <el-form-item label="授权属性" prop="ZSQBS">
          <el-input v-model="form.ZSQBS" placeholder="请输入授权属性" />
        </el-form-item>
        <el-form-item label="寄售/非寄售" prop="ZJSBS">
          <el-input v-model="form.ZJSBS" placeholder="请输入寄售/非寄售" />
        </el-form-item>
        <el-form-item label="收货地址2" prop="STREET">
          <el-input v-model="form.STREET" placeholder="请输入收货地址2" />
        </el-form-item>
        <el-form-item label="联系人" prop="NAME">
          <el-input v-model="form.NAME" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系人电话" prop="TELF1">
          <el-input v-model="form.TELF1" placeholder="请输入联系人电话" />
        </el-form-item>
        <el-form-item label="联系人邮件" prop="smtpAddr">
          <el-input v-model="form.smtpAddr" placeholder="请输入联系人邮件" />
        </el-form-item>
        <el-form-item label="管车平台服务站站号" prop="STCD3">
          <el-input v-model="form.STCD3" placeholder="请输入管车平台服务站站号" />
        </el-form-item>
        <el-form-item label="CRM系统服务站编号" prop="STCD4">
          <el-input v-model="form.STCD4" placeholder="请输入CRM系统服务站编号" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-divider content-position="center">供应商主数据采购信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddSupplierMasterPurInfo">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteSupplierMasterPurInfo">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="supplierMasterPurInfoList" :row-class-name="rowSupplierMasterPurInfoIndex" @selection-change="handleSupplierMasterPurInfoSelectionChange" ref="supplierMasterPurInfo">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="供应商编号" prop="LIFNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.LIFNR" placeholder="请输入供应商编号" />
            </template>
          </el-table-column>
          <el-table-column label="采购组织" prop="EKORG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.EKORG" placeholder="请输入采购组织" />
            </template>
          </el-table-column>
          <el-table-column label="供应商类别" prop="ZGYSLB" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZGYSLB" placeholder="请输入供应商类别" />
            </template>
          </el-table-column>
          <el-table-column label="供应商类别描述" prop="ZGYSLBTXT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZGYSLBTXT" placeholder="请输入供应商类别描述" />
            </template>
          </el-table-column>
          <el-table-column label="授权标识" prop="ZSQBS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZSQBS" placeholder="请输入授权标识" />
            </template>
          </el-table-column>
          <el-table-column label="授权标识描述" prop="ZSQBSTXT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZSQBSTXT" placeholder="请输入授权标识描述" />
            </template>
          </el-table-column>
          <el-table-column label="寄售标识" prop="ZJSBS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZJSBS" placeholder="请输入寄售标识" />
            </template>
          </el-table-column>
          <el-table-column label="寄售标识描述" prop="ZJSBSTXT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZJSBSTXT" placeholder="请输入寄售标识描述" />
            </template>
          </el-table-column>
          <el-table-column label="供应商分类" prop="ZGYSL" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZGYSL" placeholder="请输入供应商分类" />
            </template>
          </el-table-column>
          <el-table-column label="供应商分类描述" prop="ZGYSLTXT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZGYSLTXT" placeholder="请输入供应商分类描述" />
            </template>
          </el-table-column>
          <el-table-column label="现款比例" prop="ZXKBL" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZXKBL" placeholder="请输入现款比例" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSupplierbasic, getSupplierbasic, delSupplierbasic, addSupplierbasic, updateSupplierbasic } from "@/api/master/supplierbasic";

export default {
  name: "Supplierbasic",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedSupplierMasterPurInfo: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商主数据基本信息表格数据
      supplierbasicList: [],
      // 供应商主数据采购信息表格数据
      supplierMasterPurInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        LIFNR: null,
        KTOKK: null,
        STCEG: null,
        SORT1: null,
        SORT2: null,
        STCD3: null,
        STCD4: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询供应商主数据基本信息列表 */
    getList() {
      this.loading = true;
      listSupplierbasic(this.queryParams).then(response => {
        this.supplierbasicList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        supplierMasterBasicId: null,
        LIFNR: null,
        NAME1: null,
        KTOKK: null,
        KTOKKTXT: null,
        STCEG: null,
        SORT1: null,
        SORT2: null,
        ZSQBS: null,
        ZJSBS: null,
        STREET: null,
        NAME: null,
        TELF1: null,
        smtpAddr: null,
        STCD3: null,
        STCD4: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.supplierMasterPurInfoList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.supplierMasterBasicId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应商主数据基本信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const supplierMasterBasicId = row.supplierMasterBasicId || this.ids
      getSupplierbasic(supplierMasterBasicId).then(response => {
        this.form = response.data;
        this.supplierMasterPurInfoList = response.data.supplierMasterPurInfoList;
        this.open = true;
        this.title = "修改供应商主数据基本信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.supplierMasterPurInfoList = this.supplierMasterPurInfoList;
          if (this.form.supplierMasterBasicId != null) {
            updateSupplierbasic(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupplierbasic(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const supplierMasterBasicIds = row.supplierMasterBasicId || this.ids;
      this.$modal.confirm('是否确认删除供应商主数据基本信息编号为"' + supplierMasterBasicIds + '"的数据项？').then(function() {
        return delSupplierbasic(supplierMasterBasicIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 供应商主数据采购信息序号 */
    rowSupplierMasterPurInfoIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 供应商主数据采购信息添加按钮操作 */
    handleAddSupplierMasterPurInfo() {
      let obj = {};
      obj.LIFNR = "";
      obj.EKORG = "";
      obj.ZGYSLB = "";
      obj.ZGYSLBTXT = "";
      obj.ZSQBS = "";
      obj.ZSQBSTXT = "";
      obj.ZJSBS = "";
      obj.ZJSBSTXT = "";
      obj.ZGYSL = "";
      obj.ZGYSLTXT = "";
      obj.ZXKBL = "";
      this.supplierMasterPurInfoList.push(obj);
    },
    /** 供应商主数据采购信息删除按钮操作 */
    handleDeleteSupplierMasterPurInfo() {
      if (this.checkedSupplierMasterPurInfo.length == 0) {
        this.$modal.msgError("请先选择要删除的供应商主数据采购信息数据");
      } else {
        const supplierMasterPurInfoList = this.supplierMasterPurInfoList;
        const checkedSupplierMasterPurInfo = this.checkedSupplierMasterPurInfo;
        this.supplierMasterPurInfoList = supplierMasterPurInfoList.filter(function(item) {
          return checkedSupplierMasterPurInfo.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleSupplierMasterPurInfoSelectionChange(selection) {
      this.checkedSupplierMasterPurInfo = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/supplierbasic/export', {
        ...this.queryParams
      }, `supplierbasic_.xlsx`)
    }
  }
};
</script>
