<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户编码" prop="KUNNR">
        <el-input
          v-model="queryParams.KUNNR"
          placeholder="请输入客户编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="BUKRS">
        <el-input
          v-model="queryParams.BUKRS"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售组织" prop="VKORG">
        <el-input
          v-model="queryParams.VKORG"
          placeholder="请输入销售组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分销渠道" prop="VTWEG">
        <el-input
          v-model="queryParams.VTWEG"
          placeholder="请输入分销渠道"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品组" prop="SPART">
        <el-input
          v-model="queryParams.SPART"
          placeholder="请输入产品组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="搜索项" prop="SORT1">
        <el-input
          v-model="queryParams.SORT1"
          placeholder="请输入搜索项"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:customerMaster:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:customerMaster:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:customerMaster:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:customerMaster:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="customerMasterList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="stomerMasterId" />
      <el-table-column label="客户账户组" align="center" prop="KTOKD" />
      <el-table-column label="客户编码" align="center" prop="KUNNR" />
      <el-table-column label="公司代码" align="center" prop="BUKRS" />
      <el-table-column label="销售组织" align="center" prop="VKORG" />
      <el-table-column label="分销渠道" align="center" prop="VTWEG" />
      <el-table-column label="产品组" align="center" prop="SPART" />
      <el-table-column label="称谓" align="center" prop="titleMedi" />
      <el-table-column label="客户名称(中)" align="center" prop="NAME1" />
      <el-table-column label="客户名称(英)" align="center" prop="NAME2" />
      <el-table-column label="客户名称" align="center" prop="NAME3" />
      <el-table-column label="客户名称" align="center" prop="NAME4" />
      <el-table-column label="搜索项" align="center" prop="SORT1" />
      <el-table-column label="客户名称缩写" align="center" prop="SORT2" />
      <el-table-column label="送货详细地址" align="center" prop="STREET" />
      <el-table-column label="客户详细地址(备用)" align="center" prop="strSuppl1" />
      <el-table-column label="客户详细地址(备用)" align="center" prop="strSuppl2" />
      <el-table-column label="客户详细地址(备用)" align="center" prop="strSuppl3" />
      <el-table-column label="客户所在地的邮政编码" align="center" prop="postCode1" />
      <el-table-column label="城市" align="center" prop="CITY1" />
      <el-table-column label="客户所属国家" align="center" prop="COUNTRY" />
      <el-table-column label="省/州" align="center" prop="REGION" />
      <el-table-column label="语言" align="center" prop="LANGU" />
      <el-table-column label="客户联系电话" align="center" prop="telNumber" />
      <el-table-column label="移动电话" align="center" prop="mobNumber" />
      <el-table-column label="客户传真号码" align="center" prop="faxNumber" />
      <el-table-column label="客户电子邮件地址" align="center" prop="smtpAddr" />
      <el-table-column label="注释" align="center" prop="REMARK" />
      <el-table-column label="供应商" align="center" prop="LIFNR" />
      <el-table-column label="客户增值税等记号" align="center" prop="STCEG" />
      <el-table-column label="客户评级结果记录" align="center" prop="KUKLA" />
      <el-table-column label="银行国家代码" align="center" prop="BANKS" />
      <el-table-column label="银行代码" align="center" prop="BANKL" />
      <el-table-column label="银行帐户号码" align="center" prop="BANKN" />
      <el-table-column label="帐户持有人姓名" align="center" prop="KOINH" />
      <el-table-column label="银行名称" align="center" prop="BANKA" />
      <el-table-column label="客户财务帐目" align="center" prop="AKONT" />
      <el-table-column label="排序标准" align="center" prop="ZUAWA" />
      <el-table-column label="旧客户编码" align="center" prop="ALTKN" />
      <el-table-column label="付款条件" align="center" prop="ZTERM" />
      <el-table-column label="国际贸易条款" align="center" prop="INCO1" />
      <el-table-column label="国际贸易条款" align="center" prop="INCO2" />
      <el-table-column label="客户使用的交易货币" align="center" prop="WAERS" />
      <el-table-column label="销售大区划分" align="center" prop="BZIRK" />
      <el-table-column label="客户所属的销售办公室" align="center" prop="VKBUR" />
      <el-table-column label="客户所属的销售小组" align="center" prop="VKGRP" />
      <el-table-column label="客户组" align="center" prop="KDGRP" />
      <el-table-column label="价格组" align="center" prop="KONDA" />
      <el-table-column label="客户定价组" align="center" prop="KALKS" />
      <el-table-column label="暂没有使用" align="center" prop="PLTYP" />
      <el-table-column label="客户统计组" align="center" prop="VERSG" />
      <el-table-column label="暂时不使用" align="center" prop="LPRIO" />
      <el-table-column label="出货条件" align="center" prop="VSBED" />
      <el-table-column label="交付客户货物的出货工厂" align="center" prop="VWERK" />
      <el-table-column label="客户的科目分配组" align="center" prop="KTGRD" />
      <el-table-column label="税分类" align="center" prop="TAXKD" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:customerMaster:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:customerMaster:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改客户主数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="客户账户组" prop="KTOKD">
          <el-input v-model="form.KTOKD" placeholder="请输入客户账户组" />
        </el-form-item>
        <el-form-item label="客户编码" prop="KUNNR">
          <el-input v-model="form.KUNNR" placeholder="请输入客户编码" />
        </el-form-item>
        <el-form-item label="公司代码" prop="BUKRS">
          <el-input v-model="form.BUKRS" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="销售组织" prop="VKORG">
          <el-input v-model="form.VKORG" placeholder="请输入销售组织" />
        </el-form-item>
        <el-form-item label="分销渠道" prop="VTWEG">
          <el-input v-model="form.VTWEG" placeholder="请输入分销渠道" />
        </el-form-item>
        <el-form-item label="产品组" prop="SPART">
          <el-input v-model="form.SPART" placeholder="请输入产品组" />
        </el-form-item>
        <el-form-item label="称谓" prop="titleMedi">
          <el-input v-model="form.titleMedi" placeholder="请输入称谓" />
        </el-form-item>
        <el-form-item label="客户名称(中)" prop="NAME1">
          <el-input v-model="form.NAME1" placeholder="请输入客户名称(中)" />
        </el-form-item>
        <el-form-item label="客户名称(英)" prop="NAME2">
          <el-input v-model="form.NAME2" placeholder="请输入客户名称(英)" />
        </el-form-item>
        <el-form-item label="客户名称" prop="NAME3">
          <el-input v-model="form.NAME3" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="客户名称" prop="NAME4">
          <el-input v-model="form.NAME4" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="搜索项" prop="SORT1">
          <el-input v-model="form.SORT1" placeholder="请输入搜索项" />
        </el-form-item>
        <el-form-item label="客户名称缩写" prop="SORT2">
          <el-input v-model="form.SORT2" placeholder="请输入客户名称缩写" />
        </el-form-item>
        <el-form-item label="送货详细地址" prop="STREET">
          <el-input v-model="form.STREET" placeholder="请输入送货详细地址" />
        </el-form-item>
        <el-form-item label="客户详细地址(备用)" prop="strSuppl1">
          <el-input v-model="form.strSuppl1" placeholder="请输入客户详细地址(备用)" />
        </el-form-item>
        <el-form-item label="客户详细地址(备用)" prop="strSuppl2">
          <el-input v-model="form.strSuppl2" placeholder="请输入客户详细地址(备用)" />
        </el-form-item>
        <el-form-item label="客户详细地址(备用)" prop="strSuppl3">
          <el-input v-model="form.strSuppl3" placeholder="请输入客户详细地址(备用)" />
        </el-form-item>
        <el-form-item label="客户所在地的邮政编码" prop="postCode1">
          <el-input v-model="form.postCode1" placeholder="请输入客户所在地的邮政编码" />
        </el-form-item>
        <el-form-item label="城市" prop="CITY1">
          <el-input v-model="form.CITY1" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="客户所属国家" prop="COUNTRY">
          <el-input v-model="form.COUNTRY" placeholder="请输入客户所属国家" />
        </el-form-item>
        <el-form-item label="省/州" prop="REGION">
          <el-input v-model="form.REGION" placeholder="请输入省/州" />
        </el-form-item>
        <el-form-item label="语言" prop="LANGU">
          <el-input v-model="form.LANGU" placeholder="请输入语言" />
        </el-form-item>
        <el-form-item label="客户联系电话" prop="telNumber">
          <el-input v-model="form.telNumber" placeholder="请输入客户联系电话" />
        </el-form-item>
        <el-form-item label="移动电话" prop="mobNumber">
          <el-input v-model="form.mobNumber" placeholder="请输入移动电话" />
        </el-form-item>
        <el-form-item label="客户传真号码" prop="faxNumber">
          <el-input v-model="form.faxNumber" placeholder="请输入客户传真号码" />
        </el-form-item>
        <el-form-item label="客户电子邮件地址" prop="smtpAddr">
          <el-input v-model="form.smtpAddr" placeholder="请输入客户电子邮件地址" />
        </el-form-item>
        <el-form-item label="注释" prop="REMARK">
          <el-input v-model="form.REMARK" placeholder="请输入注释" />
        </el-form-item>
        <el-form-item label="供应商" prop="LIFNR">
          <el-input v-model="form.LIFNR" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="客户增值税等记号" prop="STCEG">
          <el-input v-model="form.STCEG" placeholder="请输入客户增值税等记号" />
        </el-form-item>
        <el-form-item label="客户评级结果记录" prop="KUKLA">
          <el-input v-model="form.KUKLA" placeholder="请输入客户评级结果记录" />
        </el-form-item>
        <el-form-item label="银行国家代码" prop="BANKS">
          <el-input v-model="form.BANKS" placeholder="请输入银行国家代码" />
        </el-form-item>
        <el-form-item label="银行代码" prop="BANKL">
          <el-input v-model="form.BANKL" placeholder="请输入银行代码" />
        </el-form-item>
        <el-form-item label="银行帐户号码" prop="BANKN">
          <el-input v-model="form.BANKN" placeholder="请输入银行帐户号码" />
        </el-form-item>
        <el-form-item label="帐户持有人姓名" prop="KOINH">
          <el-input v-model="form.KOINH" placeholder="请输入帐户持有人姓名" />
        </el-form-item>
        <el-form-item label="银行名称" prop="BANKA">
          <el-input v-model="form.BANKA" placeholder="请输入银行名称" />
        </el-form-item>
        <el-form-item label="客户财务帐目" prop="AKONT">
          <el-input v-model="form.AKONT" placeholder="请输入客户财务帐目" />
        </el-form-item>
        <el-form-item label="排序标准" prop="ZUAWA">
          <el-input v-model="form.ZUAWA" placeholder="请输入排序标准" />
        </el-form-item>
        <el-form-item label="旧客户编码" prop="ALTKN">
          <el-input v-model="form.ALTKN" placeholder="请输入旧客户编码" />
        </el-form-item>
        <el-form-item label="付款条件" prop="ZTERM">
          <el-input v-model="form.ZTERM" placeholder="请输入付款条件" />
        </el-form-item>
        <el-form-item label="国际贸易条款" prop="INCO1">
          <el-input v-model="form.INCO1" placeholder="请输入国际贸易条款" />
        </el-form-item>
        <el-form-item label="国际贸易条款" prop="INCO2">
          <el-input v-model="form.INCO2" placeholder="请输入国际贸易条款" />
        </el-form-item>
        <el-form-item label="客户使用的交易货币" prop="WAERS">
          <el-input v-model="form.WAERS" placeholder="请输入客户使用的交易货币" />
        </el-form-item>
        <el-form-item label="销售大区划分" prop="BZIRK">
          <el-input v-model="form.BZIRK" placeholder="请输入销售大区划分" />
        </el-form-item>
        <el-form-item label="客户所属的销售办公室" prop="VKBUR">
          <el-input v-model="form.VKBUR" placeholder="请输入客户所属的销售办公室" />
        </el-form-item>
        <el-form-item label="客户所属的销售小组" prop="VKGRP">
          <el-input v-model="form.VKGRP" placeholder="请输入客户所属的销售小组" />
        </el-form-item>
        <el-form-item label="客户组" prop="KDGRP">
          <el-input v-model="form.KDGRP" placeholder="请输入客户组" />
        </el-form-item>
        <el-form-item label="价格组" prop="KONDA">
          <el-input v-model="form.KONDA" placeholder="请输入价格组" />
        </el-form-item>
        <el-form-item label="客户定价组" prop="KALKS">
          <el-input v-model="form.KALKS" placeholder="请输入客户定价组" />
        </el-form-item>
        <el-form-item label="暂没有使用" prop="PLTYP">
          <el-input v-model="form.PLTYP" placeholder="请输入暂没有使用" />
        </el-form-item>
        <el-form-item label="客户统计组" prop="VERSG">
          <el-input v-model="form.VERSG" placeholder="请输入客户统计组" />
        </el-form-item>
        <el-form-item label="暂时不使用" prop="LPRIO">
          <el-input v-model="form.LPRIO" placeholder="请输入暂时不使用" />
        </el-form-item>
        <el-form-item label="出货条件" prop="VSBED">
          <el-input v-model="form.VSBED" placeholder="请输入出货条件" />
        </el-form-item>
        <el-form-item label="交付客户货物的出货工厂" prop="VWERK">
          <el-input v-model="form.VWERK" placeholder="请输入交付客户货物的出货工厂" />
        </el-form-item>
        <el-form-item label="客户的科目分配组" prop="KTGRD">
          <el-input v-model="form.KTGRD" placeholder="请输入客户的科目分配组" />
        </el-form-item>
        <el-form-item label="税分类" prop="TAXKD">
          <el-input v-model="form.TAXKD" placeholder="请输入税分类" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCustomerMaster, getCustomerMaster, delCustomerMaster, addCustomerMaster, updateCustomerMaster } from "@/api/master/customerMaster";

export default {
  name: "CustomerMaster",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客户主数据表格数据
      customerMasterList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        KUNNR: null,
        BUKRS: null,
        VKORG: null,
        VTWEG: null,
        SPART: null,
        SORT1: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户主数据列表 */
    getList() {
      this.loading = true;
      listCustomerMaster(this.queryParams).then(response => {
        this.customerMasterList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        stomerMasterId: null,
        KTOKD: null,
        KUNNR: null,
        BUKRS: null,
        VKORG: null,
        VTWEG: null,
        SPART: null,
        titleMedi: null,
        NAME1: null,
        NAME2: null,
        NAME3: null,
        NAME4: null,
        SORT1: null,
        SORT2: null,
        STREET: null,
        strSuppl1: null,
        strSuppl2: null,
        strSuppl3: null,
        postCode1: null,
        CITY1: null,
        COUNTRY: null,
        REGION: null,
        LANGU: null,
        telNumber: null,
        mobNumber: null,
        faxNumber: null,
        smtpAddr: null,
        REMARK: null,
        LIFNR: null,
        STCEG: null,
        KUKLA: null,
        BANKS: null,
        BANKL: null,
        BANKN: null,
        KOINH: null,
        BANKA: null,
        AKONT: null,
        ZUAWA: null,
        ALTKN: null,
        ZTERM: null,
        INCO1: null,
        INCO2: null,
        WAERS: null,
        BZIRK: null,
        VKBUR: null,
        VKGRP: null,
        KDGRP: null,
        KONDA: null,
        KALKS: null,
        PLTYP: null,
        VERSG: null,
        LPRIO: null,
        VSBED: null,
        VWERK: null,
        KTGRD: null,
        TAXKD: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.stomerMasterId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客户主数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const stomerMasterId = row.stomerMasterId || this.ids
      getCustomerMaster(stomerMasterId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改客户主数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.stomerMasterId != null) {
            updateCustomerMaster(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCustomerMaster(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const customerMasterIds = row.stomerMasterId || this.ids;
      this.$modal.confirm('是否确认删除客户主数据编号为"' + customerMasterIds + '"的数据项？').then(function() {
        return delCustomerMaster(customerMasterIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/customerMaster/export', {
        ...this.queryParams
      }, `customerMaster_.xlsx`)
    }
  }
};
</script>
