<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="专卖物料号" prop="ZZMATNR">
        <el-input
          v-model="queryParams.ZZMATNR"
          placeholder="请输入专卖物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料类型编码" prop="MTART">
        <el-input
          v-model="queryParams.MTART"
          placeholder="请输入物料类型编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品组" prop="SPART">
        <el-input
          v-model="queryParams.SPART"
          placeholder="请输入产品组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="图号" prop="ZPARH">
        <el-input
          v-model="queryParams.ZPARH"
          placeholder="请输入图号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品类型" prop="ZSPLX">
        <el-input
          v-model="queryParams.ZSPLX"
          placeholder="请输入商品类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类别" prop="ZYWLB">
        <el-input
          v-model="queryParams.ZYWLB"
          placeholder="请输入业务类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="包装类型" prop="ZBZLX">
        <el-input
          v-model="queryParams.ZBZLX"
          placeholder="请输入包装类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品类型描述" prop="ZSPLXTXT">
        <el-input
          v-model="queryParams.ZSPLXTXT"
          placeholder="请输入商品类型描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:master:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:master:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:master:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:master:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="masterList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="${comment}" align="center" prop="masterHeadId" /> -->
      <el-table-column label="专卖物料号" align="center" prop="ZZMATNR" />
      <el-table-column label="物料类型编码" align="center" prop="MTART" />
      <el-table-column label="物料类型中文描述" align="center" prop="MTBEZ" />
      <el-table-column label="基本单位" align="center" prop="MEINS" />
      <el-table-column label="基本单位中文描述" align="center" prop="MSEHT" />
      <el-table-column label="物料组" align="center" prop="MATKL" />
      <el-table-column label="物料组中文描述" align="center" prop="WGBEZ" />
      <el-table-column label="产品组" align="center" prop="SPART" />
      <el-table-column label="旧物料号" align="center" prop="BISMT" />
      <el-table-column label="中文物料描述" align="center" prop="maktxZh" />
      <el-table-column label="英文物料描述" align="center" prop="maktxEn" />
      <el-table-column label="毛重" align="center" prop="BRGEW" />
      <el-table-column label="重量单位" align="center" prop="GEWEI" />
      <el-table-column label="体积" align="center" prop="VOLUM" />
      <el-table-column label="体积单位" align="center" prop="VOLEH" />
      <el-table-column label="图号" align="center" prop="ZPARH" />
      <el-table-column label="商品类型" align="center" prop="ZSPLX" />
      <el-table-column label="图纸重量" align="center" prop="ZTZZL" />
      <el-table-column label="业务类别" align="center" prop="ZYWLB" />
      <el-table-column label="标准包装量" align="center" prop="ZBZBZL" />
      <el-table-column label="销售包装量" align="center" prop="ZXSBZL" />
      <el-table-column label="销售包装量注解" align="center" prop="ZXSBZLZJ" />
      <el-table-column label="发货包装量" align="center" prop="ZFHBZL" />
      <el-table-column label="发货包装量注解" align="center" prop="ZFHBZLZJ" />
      <el-table-column label="三包期" align="center" prop="ZSBQ" />
      <el-table-column label="启用零件加工编号" align="center" prop="ZLJBH" />
      <el-table-column label="保外标识" align="center" prop="ZBWBS" />
      <el-table-column label="适用机型" align="center" prop="ZSYJX" />
      <el-table-column label="包装类型" align="center" prop="ZBZLX" />
      <el-table-column label="供货截止日期" align="center" prop="ZGHJZRQ" />
      <el-table-column label="是否专卖开发" align="center" prop="ZSFZMKF" />
      <el-table-column label="规格" align="center" prop="ZGGXH" />
      <el-table-column label="适用马力/设定摩擦系数" align="center" prop="ZMLMCXS" />
      <el-table-column label="花键参数" align="center" prop="ZHJCS" />
      <el-table-column label="物料大类" align="center" prop="ZWLDL" />
      <el-table-column label="物料小类" align="center" prop="ZWLXL" />
      <el-table-column label="专项分类" align="center" prop="ZZXFL" />
      <el-table-column label="状态码" align="center" prop="ZWLZTM" />
      <el-table-column label="是否关联防伪码" align="center" prop="ZGLFWM" />
      <el-table-column label="包装件数" align="center" prop="ZBZJS" />
      <el-table-column label="内腔尺寸" align="center" prop="ZNQCC" />
      <el-table-column label="刀横尺寸" align="center" prop="ZDHCO" />
      <el-table-column label="包装结构" align="center" prop="ZBZJG" />
      <el-table-column label="包装用材" align="center" prop="ZBZYO" />
      <el-table-column label="辅助包装物料号" align="center" prop="ZFZBCWL" />
      <el-table-column label="对象创建者的姓名" align="center" prop="ERNAM" />
      <el-table-column label="创建日期" align="center" prop="ERSDA" />
      <el-table-column label="创建的时间" align="center" prop="ERZZT" />
      <el-table-column label="更改对象用户的名称" align="center" prop="AENAM" />
      <el-table-column label="上次更改的日期" align="center" prop="LAEDA" />
      <el-table-column label="更新时间" align="center" prop="VELUZ" />
      <el-table-column label="商品类型描述" align="center" prop="ZSPLXTXT" />
      <el-table-column label="包装类型描述" align="center" prop="ZBZLXTXT" />
      <el-table-column label="物料大类" align="center" prop="ZWLDLTXT" />
      <el-table-column label="物料小类描述" align="center" prop="ZWLXLTXT" />
      <el-table-column label="专项分类描述" align="center" prop="ZZXFLTXT" />
      <el-table-column label="状态码描述" align="center" prop="ZWLZTMTXT" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:master:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:master:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改物料主数据头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="90%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="专卖物料号" prop="ZZMATNR">
          <el-input v-model="form.ZZMATNR" placeholder="请输入专卖物料号" />
        </el-form-item>
        <el-form-item label="物料类型编码" prop="MTART">
          <el-input v-model="form.MTART" placeholder="请输入物料类型编码" />
        </el-form-item>
        <el-form-item label="物料类型中文描述" prop="MTBEZ">
          <el-input v-model="form.MTBEZ" placeholder="请输入物料类型中文描述" />
        </el-form-item>
        <el-form-item label="基本单位" prop="MEINS">
          <el-input v-model="form.MEINS" placeholder="请输入基本单位" />
        </el-form-item>
        <el-form-item label="基本单位中文描述" prop="MSEHT">
          <el-input v-model="form.MSEHT" placeholder="请输入基本单位中文描述" />
        </el-form-item>
        <el-form-item label="物料组" prop="MATKL">
          <el-input v-model="form.MATKL" placeholder="请输入物料组" />
        </el-form-item>
        <el-form-item label="物料组中文描述" prop="WGBEZ">
          <el-input v-model="form.WGBEZ" placeholder="请输入物料组中文描述" />
        </el-form-item>
        <el-form-item label="产品组" prop="SPART">
          <el-input v-model="form.SPART" placeholder="请输入产品组" />
        </el-form-item>
        <el-form-item label="旧物料号" prop="BISMT">
          <el-input v-model="form.BISMT" placeholder="请输入旧物料号" />
        </el-form-item>
        <el-form-item label="中文物料描述" prop="maktxZh">
          <el-input v-model="form.maktxZh" placeholder="请输入中文物料描述" />
        </el-form-item>
        <el-form-item label="英文物料描述" prop="maktxEn">
          <el-input v-model="form.maktxEn" placeholder="请输入英文物料描述" />
        </el-form-item>
        <el-form-item label="毛重" prop="BRGEW">
          <el-input v-model="form.BRGEW" placeholder="请输入毛重" />
        </el-form-item>
        <el-form-item label="重量单位" prop="GEWEI">
          <el-input v-model="form.GEWEI" placeholder="请输入重量单位" />
        </el-form-item>
        <el-form-item label="体积" prop="VOLUM">
          <el-input v-model="form.VOLUM" placeholder="请输入体积" />
        </el-form-item>
        <el-form-item label="体积单位" prop="VOLEH">
          <el-input v-model="form.VOLEH" placeholder="请输入体积单位" />
        </el-form-item>
        <el-form-item label="图号" prop="ZPARH">
          <el-input v-model="form.ZPARH" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="商品类型" prop="ZSPLX">
          <el-input v-model="form.ZSPLX" placeholder="请输入商品类型" />
        </el-form-item>
        <el-form-item label="图纸重量" prop="ZTZZL">
          <el-input v-model="form.ZTZZL" placeholder="请输入图纸重量" />
        </el-form-item>
        <el-form-item label="业务类别" prop="ZYWLB">
          <el-input v-model="form.ZYWLB" placeholder="请输入业务类别" />
        </el-form-item>
        <el-form-item label="标准包装量" prop="ZBZBZL">
          <el-input v-model="form.ZBZBZL" placeholder="请输入标准包装量" />
        </el-form-item>
        <el-form-item label="销售包装量" prop="ZXSBZL">
          <el-input v-model="form.ZXSBZL" placeholder="请输入销售包装量" />
        </el-form-item>
        <el-form-item label="销售包装量注解" prop="ZXSBZLZJ">
          <el-input v-model="form.ZXSBZLZJ" placeholder="请输入销售包装量注解" />
        </el-form-item>
        <el-form-item label="发货包装量" prop="ZFHBZL">
          <el-input v-model="form.ZFHBZL" placeholder="请输入发货包装量" />
        </el-form-item>
        <el-form-item label="发货包装量注解" prop="ZFHBZLZJ">
          <el-input v-model="form.ZFHBZLZJ" placeholder="请输入发货包装量注解" />
        </el-form-item>
        <el-form-item label="三包期" prop="ZSBQ">
          <el-input v-model="form.ZSBQ" placeholder="请输入三包期" />
        </el-form-item>
        <el-form-item label="启用零件加工编号" prop="ZLJBH">
          <el-input v-model="form.ZLJBH" placeholder="请输入启用零件加工编号" />
        </el-form-item>
        <el-form-item label="保外标识" prop="ZBWBS">
          <el-input v-model="form.ZBWBS" placeholder="请输入保外标识" />
        </el-form-item>
        <el-form-item label="适用机型" prop="ZSYJX">
          <el-input v-model="form.ZSYJX" placeholder="请输入适用机型" />
        </el-form-item>
        <el-form-item label="包装类型" prop="ZBZLX">
          <el-input v-model="form.ZBZLX" placeholder="请输入包装类型" />
        </el-form-item>
        <el-form-item label="供货截止日期" prop="ZGHJZRQ">
          <el-input v-model="form.ZGHJZRQ" placeholder="请输入供货截止日期" />
        </el-form-item>
        <el-form-item label="是否专卖开发" prop="ZSFZMKF">
          <el-input v-model="form.ZSFZMKF" placeholder="请输入是否专卖开发" />
        </el-form-item>
        <el-form-item label="规格" prop="ZGGXH">
          <el-input v-model="form.ZGGXH" placeholder="请输入规格" />
        </el-form-item>
        <el-form-item label="适用马力/设定摩擦系数" prop="ZMLMCXS">
          <el-input v-model="form.ZMLMCXS" placeholder="请输入适用马力/设定摩擦系数" />
        </el-form-item>
        <el-form-item label="花键参数" prop="ZHJCS">
          <el-input v-model="form.ZHJCS" placeholder="请输入花键参数" />
        </el-form-item>
        <el-form-item label="物料大类" prop="ZWLDL">
          <el-input v-model="form.ZWLDL" placeholder="请输入物料大类" />
        </el-form-item>
        <el-form-item label="物料小类" prop="ZWLXL">
          <el-input v-model="form.ZWLXL" placeholder="请输入物料小类" />
        </el-form-item>
        <el-form-item label="专项分类" prop="ZZXFL">
          <el-input v-model="form.ZZXFL" placeholder="请输入专项分类" />
        </el-form-item>
        <el-form-item label="状态码" prop="ZWLZTM">
          <el-input v-model="form.ZWLZTM" placeholder="请输入状态码" />
        </el-form-item>
        <el-form-item label="是否关联防伪码" prop="ZGLFWM">
          <el-input v-model="form.ZGLFWM" placeholder="请输入是否关联防伪码" />
        </el-form-item>
        <el-form-item label="包装件数" prop="ZBZJS">
          <el-input v-model="form.ZBZJS" placeholder="请输入包装件数" />
        </el-form-item>
        <el-form-item label="内腔尺寸" prop="ZNQCC">
          <el-input v-model="form.ZNQCC" placeholder="请输入内腔尺寸" />
        </el-form-item>
        <el-form-item label="刀横尺寸" prop="ZDHCO">
          <el-input v-model="form.ZDHCO" placeholder="请输入刀横尺寸" />
        </el-form-item>
        <el-form-item label="包装结构" prop="ZBZJG">
          <el-input v-model="form.ZBZJG" placeholder="请输入包装结构" />
        </el-form-item>
        <el-form-item label="包装用材" prop="ZBZYO">
          <el-input v-model="form.ZBZYO" placeholder="请输入包装用材" />
        </el-form-item>
        <el-form-item label="辅助包装物料号" prop="ZFZBCWL">
          <el-input v-model="form.ZFZBCWL" placeholder="请输入辅助包装物料号" />
        </el-form-item>
        <el-form-item label="对象创建者的姓名" prop="ERNAM">
          <el-input v-model="form.ERNAM" placeholder="请输入对象创建者的姓名" />
        </el-form-item>
        <el-form-item label="创建日期" prop="ERSDA">
          <el-input v-model="form.ERSDA" placeholder="请输入创建日期" />
        </el-form-item>
        <el-form-item label="创建的时间" prop="ERZZT">
          <el-input v-model="form.ERZZT" placeholder="请输入创建的时间" />
        </el-form-item>
        <el-form-item label="更改对象用户的名称" prop="AENAM">
          <el-input v-model="form.AENAM" placeholder="请输入更改对象用户的名称" />
        </el-form-item>
        <el-form-item label="上次更改的日期" prop="LAEDA">
          <el-input v-model="form.LAEDA" placeholder="请输入上次更改的日期" />
        </el-form-item>
        <el-form-item label="更新时间" prop="VELUZ">
          <el-input v-model="form.VELUZ" placeholder="请输入更新时间" />
        </el-form-item>
        <el-form-item label="商品类型描述" prop="ZSPLXTXT">
          <el-input v-model="form.ZSPLXTXT" placeholder="请输入商品类型描述" />
        </el-form-item>
        <el-form-item label="包装类型描述" prop="ZBZLXTXT">
          <el-input v-model="form.ZBZLXTXT" placeholder="请输入包装类型描述" />
        </el-form-item>
        <el-form-item label="物料大类" prop="ZWLDLTXT">
          <el-input v-model="form.ZWLDLTXT" placeholder="请输入物料大类" />
        </el-form-item>
        <el-form-item label="物料小类描述" prop="ZWLXLTXT">
          <el-input v-model="form.ZWLXLTXT" placeholder="请输入物料小类描述" />
        </el-form-item>
        <el-form-item label="专项分类描述" prop="ZZXFLTXT">
          <el-input v-model="form.ZZXFLTXT" placeholder="请输入专项分类描述" />
        </el-form-item>
        <el-form-item label="状态码描述" prop="ZWLZTMTXT">
          <el-input v-model="form.ZWLZTMTXT" placeholder="请输入状态码描述" />
        </el-form-item>
        <!-- <el-form-item label="删除标识" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标识" />
        </el-form-item> -->
        <el-divider content-position="center">物料主数据行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddMasterLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteMasterLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="masterLineList" :row-class-name="rowMasterLineIndex" @selection-change="handleMasterLineSelectionChange" ref="masterLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="工厂" prop="WERKS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.WERKS" placeholder="请输入工厂" />
            </template>
          </el-table-column>
          <el-table-column label="销售组织" prop="VKORG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.VKORG" placeholder="请输入销售组织" />
            </template>
          </el-table-column>
          <el-table-column label="采购组" prop="EKGRP" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.EKGRP" placeholder="请输入采购组" />
            </template>
          </el-table-column>
          <el-table-column label="采购组描述" prop="EKNAM" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.EKNAM" placeholder="请输入采购组描述" />
            </template>
          </el-table-column>
          <el-table-column label="采购类型" prop="BESKZ" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.BESKZ" placeholder="请输入采购类型" />
            </template>
          </el-table-column>
          <el-table-column label="物料组1" prop="MVGR1" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MVGR1" placeholder="请输入物料组1" />
            </template>
          </el-table-column>
          <el-table-column label="物料组1描述" prop="WGBEZ1" width="150">
            <template slot-scope="scope">
              <el-input disabled v-model="scope.row.WGBEZ1" placeholder="请输入物料组1描述" />
            </template>
          </el-table-column>
          <el-table-column label="物料组2" prop="MVGR2" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MVGR2" placeholder="请输入物料组2" />
            </template>
          </el-table-column>
          <el-table-column label="物料组2描述" prop="WGBEZ2" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.WGBEZ2" placeholder="请输入物料组2描述" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMaster, getMaster, delMaster, addMaster, updateMaster } from "@/api/master/master";

export default {
  name: "Master",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedMasterLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料主数据头信息表格数据
      masterList: [],
      // 物料主数据行信息表格数据
      masterLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ZZMATNR: null,
        MTART: null,
        SPART: null,
        ZPARH: null,
        ZSPLX: null,
        ZYWLB: null,
        ZBZLX: null,
        ZSPLXTXT: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物料主数据头信息列表 */
    getList() {
      this.loading = true;
      listMaster(this.queryParams).then(response => {
        this.masterList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        masterHeadId: null,
        ZZMATNR: null,
        MTART: null,
        MTBEZ: null,
        MEINS: null,
        MSEHT: null,
        MATKL: null,
        WGBEZ: null,
        SPART: null,
        BISMT: null,
        maktxZh: null,
        maktxEn: null,
        BRGEW: null,
        GEWEI: null,
        VOLUM: null,
        VOLEH: null,
        ZPARH: null,
        ZSPLX: null,
        ZTZZL: null,
        ZYWLB: null,
        ZBZBZL: null,
        ZXSBZL: null,
        ZXSBZLZJ: null,
        ZFHBZL: null,
        ZFHBZLZJ: null,
        ZSBQ: null,
        ZLJBH: null,
        ZBWBS: null,
        ZSYJX: null,
        ZBZLX: null,
        ZGHJZRQ: null,
        ZSFZMKF: null,
        ZGGXH: null,
        ZMLMCXS: null,
        ZHJCS: null,
        ZWLDL: null,
        ZWLXL: null,
        ZZXFL: null,
        ZWLZTM: null,
        ZGLFWM: null,
        ZBZJS: null,
        ZNQCC: null,
        ZDHCO: null,
        ZBZJG: null,
        ZBZYO: null,
        ZFZBCWL: null,
        ERNAM: null,
        ERSDA: null,
        ERZZT: null,
        AENAM: null,
        LAEDA: null,
        VELUZ: null,
        ZSPLXTXT: null,
        ZBZLXTXT: null,
        ZWLDLTXT: null,
        ZWLXLTXT: null,
        ZZXFLTXT: null,
        ZWLZTMTXT: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.masterLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.masterHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料主数据头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const masterHeadId = row.masterHeadId || this.ids
      getMaster(masterHeadId).then(response => {
        this.form = response.data;
        this.masterLineList = response.data.masterLineList;
        this.open = true;
        this.title = "修改物料主数据头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.masterLineList = this.masterLineList;
          if (this.form.masterHeadId != null) {
            updateMaster(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaster(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const masterHeadIds = row.masterHeadId || this.ids;
      this.$modal.confirm('是否确认删除物料主数据头信息编号为"' + masterHeadIds + '"的数据项？').then(function() {
        return delMaster(masterHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 物料主数据行信息序号 */
    rowMasterLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 物料主数据行信息添加按钮操作 */
    handleAddMasterLine() {
      let obj = {};
      obj.ZZMATNR = "";
      obj.WERKS = "";
      obj.VKORG = "";
      obj.EKGRP = "";
      obj.EKNAM = "";
      obj.BESKZ = "";
      obj.MVGR1 = "";
      obj.WGBEZ1 = "";
      obj.MVGR2 = "";
      obj.WGBEZ2 = "";
      this.masterLineList.push(obj);
    },
    /** 物料主数据行信息删除按钮操作 */
    handleDeleteMasterLine() {
      if (this.checkedMasterLine.length == 0) {
        this.$modal.msgError("请先选择要删除的物料主数据行信息数据");
      } else {
        const masterLineList = this.masterLineList;
        const checkedMasterLine = this.checkedMasterLine;
        this.masterLineList = masterLineList.filter(function(item) {
          return checkedMasterLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleMasterLineSelectionChange(selection) {
      this.checkedMasterLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/master/export', {
        ...this.queryParams
      }, `master_.xlsx`)
    }
  }
};
</script>
