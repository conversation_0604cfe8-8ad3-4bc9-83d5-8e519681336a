<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="SAS组装单号" prop="VKORG">
        <el-input
          v-model="queryParams.VKORG"
          placeholder="请输入SAS组装单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="VTWEG">
        <el-input
          v-model="queryParams.VTWEG"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂" prop="KUNNR">
        <el-input
          v-model="queryParams.KUNNR"
          placeholder="请输入工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组装单状态" prop="KWMENG">
        <el-input
          v-model="queryParams.KWMENG"
          placeholder="请输入组装单状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组装单供应商" prop="KBETR">
        <el-input
          v-model="queryParams.KBETR"
          placeholder="请输入组装单供应商"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['master:CustomerMaterials:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['master:CustomerMaterials:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['master:CustomerMaterials:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['master:CustomerMaterials:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="pushCusMaterials"
          v-hasPermi="['master:CustomerMaterials:export']"
        >推送</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="CustomerMaterialsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="customerMaterialsId" />
      <el-table-column label="SAS组装单号" align="center" prop="VKORG" />
      <el-table-column label="公司代码" align="center" prop="VTWEG" />
      <el-table-column label="工厂" align="center" prop="KUNNR" />
      <el-table-column label="总成件入库库存地点" align="center" prop="ZZMATNR" />
      <el-table-column label="组装单状态" align="center" prop="KWMENG" />
      <el-table-column label="组装单供应商" align="center" prop="KBETR" />
      <el-table-column label="组装单行项目" align="center" prop="ZDFLAG" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['master:CustomerMaterials:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['master:CustomerMaterials:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改客户物料主数据-宇通对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="SAS组装单号" prop="VKORG">
          <el-input v-model="form.VKORG" placeholder="请输入SAS组装单号" />
        </el-form-item>
        <el-form-item label="公司代码" prop="VTWEG">
          <el-input v-model="form.VTWEG" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="工厂" prop="KUNNR">
          <el-input v-model="form.KUNNR" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="总成件入库库存地点" prop="ZZMATNR">
          <el-input v-model="form.ZZMATNR" placeholder="请输入总成件入库库存地点" />
        </el-form-item>
        <el-form-item label="组装单状态" prop="KWMENG">
          <el-input v-model="form.KWMENG" placeholder="请输入组装单状态" />
        </el-form-item>
        <el-form-item label="组装单供应商" prop="KBETR">
          <el-input v-model="form.KBETR" placeholder="请输入组装单供应商" />
        </el-form-item>
        <el-form-item label="组装单行项目" prop="ZDFLAG">
          <el-input v-model="form.ZDFLAG" placeholder="请输入组装单行项目" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCustomerMaterials, getCustomerMaterials, delCustomerMaterials, addCustomerMaterials, updateCustomerMaterials,pushCusMaterials } from "@/api/master/CustomerMaterials";

export default {
  name: "CustomerMaterials",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客户物料主数据-宇通表格数据
      CustomerMaterialsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        VKORG: null,
        VTWEG: null,
        KUNNR: null,
        KWMENG: null,
        KBETR: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户物料主数据-宇通列表 */
    getList() {
      this.loading = true;
      listCustomerMaterials(this.queryParams).then(response => {
        this.CustomerMaterialsList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(()=>{
         this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        customerMaterialsId: null,
        VKORG: null,
        VTWEG: null,
        KUNNR: null,
        ZZMATNR: null,
        KWMENG: null,
        KBETR: null,
        ZDFLAG: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.customerMaterialsId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客户物料主数据-宇通";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const customerMaterialsId = row.customerMaterialsId || this.ids
      getCustomerMaterials(customerMaterialsId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改客户物料主数据-宇通";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.customerMaterialsId != null) {
            updateCustomerMaterials(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCustomerMaterials(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const customerMaterialsIds = row.customerMaterialsId || this.ids;
      this.$modal.confirm('是否确认删除客户物料主数据-宇通编号为"' + customerMaterialsIds + '"的数据项？').then(function() {
        return delCustomerMaterials(customerMaterialsIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('master/CustomerMaterials/export', {
        ...this.queryParams
      }, `CustomerMaterials_.xlsx`)
    },
    pushCusMaterials(){
      pushCusMaterials().then((res)=>{

          this.$modal.msgSuccess(res?.data?.msg || "操作成功");
      })
    }
  }
};
</script>
