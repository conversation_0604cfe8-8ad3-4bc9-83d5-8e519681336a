<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="集团/成本控制范围" prop="kokes">
        <el-input
          v-model="queryParams.kokes"
          placeholder="请输入集团/成本控制范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input
          v-model="queryParams.company"
          placeholder="请输入公司"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂" prop="werks">
        <el-input
          v-model="queryParams.werks"
          placeholder="请输入工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂名称" prop="werksName">
        <el-input
          v-model="queryParams.werksName"
          placeholder="请输入工厂名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购组织" prop="purOrg">
        <el-input
          v-model="queryParams.purOrg"
          placeholder="请输入采购组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购组织名称" prop="purOrgName">
        <el-input
          v-model="queryParams.purOrgName"
          placeholder="请输入采购组织名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售组织" prop="salesOrg">
        <el-input
          v-model="queryParams.salesOrg"
          placeholder="请输入销售组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售组织名称" prop="salesOrgName">
        <el-input
          v-model="queryParams.salesOrgName"
          placeholder="请输入销售组织名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商编号" prop="lifnr">
        <el-input
          v-model="queryParams.lifnr"
          placeholder="请输入供应商编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户编号" prop="kunnr">
        <el-input
          v-model="queryParams.kunnr"
          placeholder="请输入客户编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['orgRelation:orgRelation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['orgRelation:orgRelation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['orgRelation:orgRelation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['orgRelation:orgRelation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orgRelationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="组织关系主键" align="center" prop="orgId" />
      <el-table-column label="集团/成本控制范围" align="center" prop="kokes" />
      <el-table-column label="公司" align="center" prop="company" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="工厂" align="center" prop="werks" />
      <el-table-column label="工厂名称" align="center" prop="werksName" />
      <el-table-column label="采购组织" align="center" prop="purOrg" />
      <el-table-column label="采购组织名称" align="center" prop="purOrgName" />
      <el-table-column label="销售组织" align="center" prop="salesOrg" />
      <el-table-column label="销售组织名称" align="center" prop="salesOrgName" />
      <el-table-column label="供应商编号" align="center" prop="lifnr" />
      <el-table-column label="客户编号" align="center" prop="kunnr" />
      <el-table-column label="是否专卖总部" align="center" prop="zmzbFlag" />
      <el-table-column label="地址" align="center" prop="sasAddress" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['orgRelation:orgRelation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['orgRelation:orgRelation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改组织关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="集团/成本控制范围" prop="kokes">
          <el-input v-model="form.kokes" placeholder="请输入集团/成本控制范围" />
        </el-form-item>
        <el-form-item label="公司" prop="company">
          <el-input v-model="form.company" placeholder="请输入公司" />
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="工厂" prop="werks">
          <el-input v-model="form.werks" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="工厂名称" prop="werksName">
          <el-input v-model="form.werksName" placeholder="请输入工厂名称" />
        </el-form-item>
        <el-form-item label="采购组织" prop="purOrg">
          <el-input v-model="form.purOrg" placeholder="请输入采购组织" />
        </el-form-item>
        <el-form-item label="采购组织名称" prop="purOrgName">
          <el-input v-model="form.purOrgName" placeholder="请输入采购组织名称" />
        </el-form-item>
        <el-form-item label="销售组织" prop="salesOrg">
          <el-input v-model="form.salesOrg" placeholder="请输入销售组织" />
        </el-form-item>
        <el-form-item label="销售组织名称" prop="salesOrgName">
          <el-input v-model="form.salesOrgName" placeholder="请输入销售组织名称" />
        </el-form-item>
        <el-form-item label="供应商编号" prop="lifnr">
          <el-input v-model="form.lifnr" placeholder="请输入供应商编号" />
        </el-form-item>
        <el-form-item label="客户编号" prop="kunnr">
          <el-input v-model="form.kunnr" placeholder="请输入客户编号" />
        </el-form-item>
        <el-form-item label="是否专卖总部" prop="zmzbFlag">
          <el-input v-model="form.zmzbFlag" placeholder="请输入是否专卖总部" />
        </el-form-item>
        <el-form-item label="地址" prop="sasAddress">
          <el-input v-model="form.sasAddress" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="${comment}" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入${comment}" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrgRelation, getOrgRelation, delOrgRelation, addOrgRelation, updateOrgRelation } from "@/api/sales/orgRelation/orgRelation";

export default {
  name: "OrgRelation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 组织关系表格数据
      orgRelationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        kokes: null,
        company: null,
        companyName: null,
        werks: null,
        werksName: null,
        purOrg: null,
        purOrgName: null,
        salesOrg: null,
        salesOrgName: null,
        lifnr: null,
        kunnr: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询组织关系列表 */
    getList() {
      this.loading = true;
      listOrgRelation(this.queryParams).then(response => {
        this.orgRelationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        orgId: null,
        kokes: null,
        company: null,
        companyName: null,
        werks: null,
        werksName: null,
        purOrg: null,
        purOrgName: null,
        salesOrg: null,
        salesOrgName: null,
        lifnr: null,
        kunnr: null,
        zmzbFlag: null,
        sasAddress: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orgId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加组织关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const orgId = row.orgId || this.ids
      getOrgRelation(orgId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改组织关系";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.orgId != null) {
            updateOrgRelation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrgRelation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const orgIds = row.orgId || this.ids;
      this.$modal.confirm('是否确认删除组织关系编号为"' + orgIds + '"的数据项？').then(function() {
        return delOrgRelation(orgIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('orgRelation/orgRelation/export', {
        ...this.queryParams
      }, `orgRelation_.xlsx`)
    }
  }
};
</script>
