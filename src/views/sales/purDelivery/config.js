export const columns = [
  {
    label: "SAS销售需求单号",
    type: "input",
    prop: "demandNo",
  },

  {
    label: "SAS销售订单号",
    type: "input",
    prop: "salesNo",
  },
  {
    prop: "docStatus",
    label: "单据状态",
    // hiddenSearch:true,
    type: "select",
    slotName: "docStatus",
    optionsCode: "demand_send_status",
    width: 140,
    fixed: "right",
    attr: {
      disabled: true,
    },
  },
  {
    label: "SAS销售交货单号",
    type: "input",
    prop: "salesDeliveryNo",
    slotName: "salesDeliveryNo",
  },
  {
    label: "SAP销售交货单号",
    type: "input",
    prop: "vbelnDr",
  },
  //  {
  //   label: "创建日期",
  //   type: "daterange",
  //   prop: "createTimeS,createTimeE",
  //   hiddenTable: true,
  // },

  {
    label: "销售组织",
    type: "input",
    prop: "VSTEL",
    dictOptionCode: "VKORG",
    // slotName:'vstel',
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VSTEL",
      },
    ],
  },
  {
    prop: "KUNNR",
    label: "客户名称",
    append: true,
    dictOptionCode: "KUNNR",
    backProps: [
      {
        backProp: "KUNNR",
        backLable: "NAME1",
        prop: "KUNNR",
      },
    ],
  },

  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
    dictOptionCode: "VKORG",
    // slotName:'vstel',
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "vstel",
      },
    ],
  },
  {
    label: "付款方式",
    type: "select",
    prop: "ZTERM",
    slotName: "ZTERM",
    optionsCode: "zterm",
  },

  {
    label: "单据总金额",
    type: "input",
    prop: "amount",
  },
  {
    label: "单据品种",
    type: "input",
    prop: "num",
  },
  {
    label: "发运方式",
    prop: "shipMethod",
    slotName: "shipMethod",
    optionsCode: "ship_method",
  },

  {
    label: "是否扫码",
    type: "select",
    prop: "scanFlag",
    optionsCode: "y_n",
    slotName:'scanFlag'
  },
  {
    label: "是否补单",
    type: "select",
    prop: "bdFlag",
    slotName:'bdFlag',
    optionsCode: "y_n",
  },

  {
    label: "计划发货日期",
    type: "input",
    prop: "LFDAT",
    type: "date",
  },
  {
    label: "仓储组",
    type: "input",
    prop: "MVGR2",
  },
  {
    prop: "fumigation",
    label: "是否熏蒸",
    width: 140,
    type: "select",
    optionsCode: "fumigation",
    slotName:'fumigation'
    
  },
  {
    prop: "creditLimit",
    label: "总信用额度",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "availableCreditLimit",
    label: "可用额度",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "hMemo",
    label: "抬头备注",
    width: 140,
    searchOut: true,
    // hiddenSearch: true,
  },

  {
    label: "创建日期",
    type: "date",
    prop: "createTime",
    hiddenSearch: true,
  },

  {
    prop: "caozuo",
    label: "详情",
    width: 200,
    hiddenSearch: true,
    slotName: "caozuo",
    fixed: "right",
  },
].map((item) => {
  item.width = item.width || "140px";

  return item;
});

export const searchData1 = JSON.parse(JSON.stringify(columns)).map((item) => {
    item.append = false
    item.backProps = undefined
    if(item.prop === 'VSTEL'){
      item.prop = 'salesOrgName'
    }
    if(item.prop === 'KUNNR'){
      item.prop = 'NAME1'
    }
    if(item.prop === 'WERKS'){
      item.prop = 'werksName'
    }
  if (item.prop === "docStatus") {
    item.otherBind = (item) => {
      let dis = !["ckSuccess", "ckError", "E"].includes(item.value);
      return {
        disabled: dis,
      };
    };
    item.attr = {
      disabled: false,
    };
  } else if (item.prop === "hMemo") {
    item.hiddenSearch = true;
  }
  return item;
}).slice(0,9);


export const columns2 = JSON.parse(JSON.stringify(columns))
  .map((item) => {
    if (item.prop === "docStatus") {
      item.attr = {
        disabled: false,
      };
    }

    if (["docStatus", "caozuo", "LFDAT", "hMemo",'bdFlag'].includes(item.prop)) {
      item.hiddenSearch = true;
    }
    return item;
  })
  .slice(0, -1);

export const keyList = columns.filter(({ optionsCode }) => optionsCode);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const searchData = columns.map((item) => {
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

export const getSearchData = (docStatus = "") => {
  const list = JSON.parse(JSON.stringify(columns)).map((item) => {
    item.attr = {
      disabled: true,
    };

    if (
      ["INIT", "E"].includes(docStatus) &&
      ["hMemo", "LFDAT", "ZTERM",'bdFlag'].includes(item.prop)
    ) {
      item.attr = {
        disabled: false,
      };
    }

    return item;
  });

  return list;
};

export const lineCloumn = [
  {
    label: "行号",
    type: "input",
    prop: "POSNR",
    attr: {
      disabled: true,
    },
  },
  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
    width: 300,
    attr: {
      disabled: true,
    },
  },
  {
    label: "物料描述",
    type: "input",
    prop: "zzmatnrName",
    width: 120,
    attr: {
      disabled: true,
    },
  },
  {
    label: "数量",
    type: "number",
      width: 200,
    prop: "KWMENG",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "KBETR",
    label: "含税单价",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "netpr",
    label: "净价",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "WERKS",
    label: "工厂",
    dictOptionCode: "VKORG",
    // slotName: "WERKS",
     width: 200,
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "VKORG",
      },
    ],
    attr: {
      disabled: true,
    },
  },

  {
    prop: "LGORT",
    label: "库存地点",
     width: 200,

    attr: {
      disabled: true,
    },
    dictOptionCode: "LGORT",
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
  },

  {
    prop: "fumigation",
    label: "是否熏蒸",
    width: 140,
    type: "select",
    optionsCode: "fumigation",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "iMemo",
    label: "行备注",
    attr: {
      disabled: true,
    },
      width: 200,
  },

  {
    prop: "ABGRU",
    label: "拒绝原因",
    width: 140,
    attr: {
      disabled: true,
    },
    optionsCode: "abgru",
    fixed: "right",
    // width:140
  },
];

export const getLineCloumn = (docStatus) => {
  let lineData = JSON.parse(JSON.stringify(lineCloumn));
  lineData = lineData.map((item) => {
    if (
      ["INIT", "E"].includes(docStatus) &&
      ["KWMENG", "iMemo"].includes(item.prop)
    ) {
      item.attr = {
        disabled: false,
      };

      if (item.prop === "KWMENG") {
        item.inputChange = (val, oldVal, self) => {
          const { oldKWMENG = 0 } = self?.row;
          const num = Number(oldKWMENG) || 0;
          if (val > num) {
            return num;
          } else if (val <= 0) {
            return 1;
          }
          return val;
        };
      }
    }
    return item;
  });

  return lineData;
};

export const getlineCloumnData = () => {
  const columnsObj = {};
  lineCloumn.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const deliverColums = JSON.parse(JSON.stringify(columns));
export const deliverLine = JSON.parse(JSON.stringify(lineCloumn));
export const deliverSearchData = JSON.parse(JSON.stringify(searchData));
