<template>
  <div class="add" v-loading="loading">
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="16"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      @submitEmits="submitEmits"
    >
      <template #searchButton v-if="pageType !== 'look'">
        <el-button
          v-if="['E', 'INIT','S','ckError'].includes(salesHeadDetail.docStatus)"
          type="danger"
          size="mini"
          @click="submitTip('huiTui')"
          v-hasPermi="['sales/purDelivery:purDelivery:submit']"
          :disabled="loading"
          >回退</el-button
        >

        <el-button
          v-if="['E', 'INIT'].includes(salesHeadDetail.docStatus)"
          v-hasPermi="['sales/purDelivery:purDelivery:edit']"
          type="primary"
          size="mini"
          @click="submitTip('upd')"
          :disabled="loading"
          >修改</el-button
        >
        <el-button
          v-if="['E', 'INIT'].includes(salesHeadDetail.docStatus)"
          type="primary"
          size="mini"
          @click="submitTip('submit')"
          v-hasPermi="['sales/purDelivery:purDelivery:submit']"
          :disabled="loading"
          >提交</el-button
        >

        <!-- <el-button
          v-if="['E'].includes(salesHeadDetail.docStatus)"
          type="primary"
          size="mini"
          @click="submitEmits('cancel')"
          v-hasPermi="['sales/purDelivery:purDelivery:submit']"
          >取消</el-button
        > -->

        <el-button
          v-if="
            ['S', 'ckSuccess', 'ckError'].includes(salesHeadDetail.docStatus)
          "
          type="primary"
          size="mini"
          @click="printInfo('money')"
          v-hasPermi="['sales/salesDelivery:print']"
          >打印金额</el-button
        >
        <el-button
          v-if="
            ['S', 'ckSuccess', 'ckError'].includes(salesHeadDetail.docStatus)
          "
          type="primary"
          size="mini"
          @click="printInfo('')"
          v-hasPermi="['sales/salesDelivery:print']"
          >打印不含金额</el-button
        >
      </template>
    </ComSearch>
    <LineTable
      :lineList="lineList"
      :lineCloumn="lineCloumn"
      :btnProps="btnProps"
      :isShowCancel="['E', 'INIT','S','ckError'].includes(salesHeadDetail.docStatus)"
      :cancelFuntion="cancelFuntion"
      @refreshList="getDetail"
      :showSelection="true"
      :keyId="'deliveryLineId'"
      ref="LineTable"
    >
    </LineTable>
    <printA ref="printA" />
  </div>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import { searchData, lineCloumn, getSearchData, getLineCloumn } from "./config";
import { btnProps } from "./unit";
import { listWerksInfo } from "@/api/sales/werksInfo/werksInfo";
import {
  getSalesDelivery,
  addSalesDelivery,
} from "@/api/sales/salesDelivery/salesDelivery";
import { dictOptionCodeKey, setTableDictValue } from "@/utils/index";
import { formatNowDate, nextFormatNowDate } from "@/utils";
import printA from "@/components/Print/printA.vue";
export default {
  name: "PurDeliveryDetail",
  dicts: ["demand_doc_status", "ship_method", "fumigation", "zterm",'y_n'],
  dictsKey: [...dictOptionCodeKey(lineCloumn), "KUNNR"],
  components: {
    ComSearch,
    LineTable,
    printA,
  },

  computed: {
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      btnProps,
      loading: false,
      searchInfo: {},
      rules: {},
      searchData: getSearchData(),
      pageType,
      lineCloumn,
      lineList: [], // getlineCloumnData(),
      salesHeadDetail: {},
      salesOrderLineList: [],
    };
  },

  created() {
    this.getDetail();
  },
  watch: {},
  methods: {
    getDetail() {
      if (!this.$route.params.id || this.loading) return;
      this.loading = true;
      getSalesDelivery(this.$route.params.id)
        .then((res) => {
          // console.log(res, "res");
          if (res.code === 200) {
            this.salesHeadDetail = res.data;
            this.searchInfo = {
              ...this.salesHeadDetail,
            };

            let _searchData = getSearchData(res.data?.docStatus);
            this.searchData = _searchData;
            let cloumn = getLineCloumn(res.data?.docStatus);
            this.lineCloumn = cloumn;

            const _rows = setTableDictValue(
              this.lineCloumn,
              res.data.salesDeliveryLineList,
              this.dictDataOptions
            );

            const data = _rows.map((item) => {
              item.oldKWMENG = item.KWMENG;
              return item;
            });

            this.lineList = data;
            // this.location(res?.data?.vstel)
          }
          this.loading = false;
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },

    submitTip(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },

    goBackData() {
      this.$confirm("是否回退该单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {

      });
    },

    submitEmits(data, type) {
      if (type === "回退") {
        this.goBackData();
        return;
      }

      if (this.loading) return;
      this.loading = true;
      const lineList = this.$refs.LineTable.newlineList;

      const params = {
        ...this.salesHeadDetail,
        ...data,
        antion: type === "upd" ? "upd" : undefined,
        VBELN: this.salesHeadDetail.VBELN,
        vbelnSo: this.salesHeadDetail.vbelnSo,
        salesNo: this.salesHeadDetail.salesNo,
        // VBELN_DR: "",
        delFlag: 0,
        VSTEL: this.salesHeadDetail.vstel,
        LFDAT: formatNowDate(),
        hMemo: this.salesHeadDetail.hMemo,
        deliveryHeadId: this.salesHeadDetail.deliveryHeadId,
        salesDeliveryNo: this.salesHeadDetail.salesDeliveryNo,
      };

      const paramsList = lineList.map((item, index) => {
        return {
          ...item,
          ZZMATNR: item.ZZMATNR,
          BNFPO: item.BNFPO,
          WERKS: item.WERKS,
          vbelnSo: item.vbelnSo,
          salesHeadId: item.salesHeadId,
          salesLineId: item.salesLineId,
          POSNR: item.POSNR,
          LGORT: item.LGORT,
          KWMENG: item.KWMENG,
          iMemo: item.iMemo,
        };
      });
      params.salesDeliveryLineList = paramsList;

      addSalesDelivery({
        ...params,
        // action: type,
      })
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.getDetail();
            this.$modal.msgSuccess(res?.msg || "操作成功");
          }
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    printInfo(type = "") {
      // window.open(`${location.origin}/inventory/print?ids=${this.$route.params.id}`)
      let ids = this.$route.params.id;
      this.$refs.printA.printInfo(ids, type);
      this.$refs.printA.printDialogVisible = true;
      // this.$refs.printA.type='money'
    },
    setlineList() {
      let list = this.$refs.lineTable.multipleSelection;
      if (list.length === 0) {
        list = this.$refs.lineTable.newlineList || [];
      }

      list = list.filter((item) => {
        return item?.ABGRU !== "12";
      });

      return list;
    },
    async cancelFuntion(data, rows) {

      try {
        const ids = rows
          .filter(({ ABGRU }) => ABGRU !== "12")
          .map(({ deliveryLineId }) => deliveryLineId);

        if (!ids || ids.length === 0) {
          this.$message({
            type: "error",
            message: `暂无取消的数据`,
          });
          return;
        }

        let list = this.$refs.LineTable.newlineList;
        list = [...list].map((item) => {
          item.ABGRU = ids.includes(item.deliveryLineId) ? "12" : undefined;
          return item;
        });

        const dataInfo = this.$refs.ComSearchInfo.changeInfoData();

        const params = {
          ...this.salesHeadDetail,
          ...dataInfo,
          antion: "upd",
          delFlag: 0,
          VSTEL: this.salesHeadDetail.vstel,
          LFDAT: formatNowDate(),
          salesDeliveryLineList: list,
        };

        if (this.loading) return;
        this.loading = true;

        await addSalesDelivery({
          ...params,
        });
        this.loading = false;
        this.getDetail();
      } catch (error) {
        this.loading = false;

        console.log(error, "error");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}
.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
