<template>
  <div :class="isSelfPage ? 'app-container' : 'app-container app-container2'">
    <ComTableList
      :searchData="searchData1"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 6 }"
      :tableProps="tableProps"
      :isShowRightBtn="!isSelfPage"
      :searchShow="isSelfPage"
      :isSelection="true"
      :selectable="selectable"
      :keyId="isSelfPage ? 'purDelivery' : 'purDelivery2'"
      @selection-change="handleSelectionChange"
      ref="ComTableList"
    >
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          :disabled="selectList.length === 0 ? true : false"
          @click="inventoryAccountPrint('money')"
          v-hasPermi="['sales/salesDelivery:print']"
          >批量打印金额</el-button
        >
         <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          :disabled="selectList.length === 0 ? true : false"
          @click="inventoryAccountPrint('')"
          v-hasPermi="['sales/salesDelivery:print']"
          >批量打印不含金额</el-button
        >
      </template>
      
      <template #salesDeliveryNo="{ row }">
         <a class="href" @click="handeFuntion('look', row)">{{ row.salesDeliveryNo }}</a>
      </template>

      <template v-for="(item, index) in keyList" #[item.slotName]="{ row }">
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>

      <!-- <template #searchButton>
        <el-button size="mini" :type="'primary'" icon="el-icon-plus"
          >导出</el-button
        >
      </template> -->
      <template #caozuo="{ row }">
        <el-button 
        v-hasPermi="['sales/purDelivery:purDelivery:listedit']"
        size="mini" type="primary" @click="handeFuntion('edit', row)"
          >修改</el-button
        >

        <el-button
          v-hasPermi="['sales/purDelivery:purDelivery:detail']"
          size="mini"
          type="primary"
          @click="handeFuntion('look', row)"
          >详情</el-button
        >
      </template>
    </ComTableList>

    <printA ref="printA" />
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import printA from "@/components/Print/printA.vue";
import { columns, keyList, searchData1, columns2 } from "./config";
import { listSalesDelivery } from "@/api/sales/salesDelivery/salesDelivery";
import { getDictKey, dictOptionCodeKey, param } from "@/utils/index";

export default {
  name: "PurDelivery",
  components: {
    ComTableList,
    printA,
  },
  dictsKey: [...dictOptionCodeKey(columns), "LGORT"],
  dicts: ["demand_send_status", "doct_status",'fumigation' ,"ship_method","zterm",'y_n'],
  data() {
    const as = location.href.indexOf("/sales/purDelivery") > -1;
    // console.log(as,columns,columns2,'as')
    return {
      columns: as ? columns : columns2,
      dictKey: getDictKey(columns),
      keyList,
      searchData1,
      selectList: [],
      tableProps: {
        lineBtnrightClass: "lineBtnright2",
        rowKey: "deliveryHeadId",
        // height: 460,
      },
    };
  },
  created() {
    // this.getList();
  },
  computed: {
    isSelfPage() {
      return location.href.indexOf("/sales/purDelivery") > -1;
    },
  },
  methods: {
    comitList(params) {
      this.$refs.ComTableList.searchComTableInfo = params;
      //  console.log(this.$refs,params,'0000')
      this.$refs.ComTableList.submitEmits(params);
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {
      // if (!param?.docStatus) {
      //   // params.docStatusList = ["S", "E", "ckSuccess", "ckError", "commit",'INIT'];
      // } else {
      //   // params.docStatusList = params?.docStatus ? [params.docStatus] : [];
      // }

      const res = await listSalesDelivery(params);
      return res;
    },
    handeFuntion(type, data) {
      if (type) {
        this.$store
          .dispatch("tagsView/delPage", { name: "PurDeliveryDetail" })
          .then(() => {
            this.$router.push(
              `/sales/purDelivery-detail/index/${type}/${data.deliveryHeadId}`
            );
          });
      }
    },
    selectable(row) {
      return ["S", "ckSuccess", "ckError",].includes(row.docStatus);
    },
    handleSelectionChange(val) {
      this.selectList = val;
    },
    inventoryAccountPrint(type = '') {
      let ids = this.selectList
        .map(({ deliveryHeadId }) => deliveryHeadId)
        .join(",");

      this.$refs.printA.printInfo(ids,type);
      this.$refs.printA.printDialogVisible = true;

      //  window.open(`${location.origin}/inventory/print?${ids}`)
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container2 .ComTableList {
  // margin-top: 30px;
}
.app-container2 {
  padding-left: 0px;
  padding-right: 0px;
  .lineBtnright2 {
    top: -42px;
  }
}
</style>
