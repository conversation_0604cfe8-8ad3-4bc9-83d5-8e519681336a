<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="SAP销售订单号" prop="vbelnSo">
        <el-input
          v-model="queryParams.vbelnSo"
          placeholder="请输入销售订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAP交货单号" prop="vbelnDr">
        <el-input
          v-model="queryParams.vbelnDr"
          placeholder="请输入销售交货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAS交货单号" prop="purchaseNo">
        <el-input
          v-model="queryParams.purchaseNo"
          placeholder="请输入采购内向交货单单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales/purDelivery:purDelivery:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales/purDelivery:purDelivery:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales/purDelivery:purDelivery:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales/purDelivery:purDelivery:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purDeliveryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="purchaseHeadId" />
      <el-table-column label="系统编码" align="center" prop="ivSysno" />
      <el-table-column label="接口编码" align="center" prop="ivItfno" />
      <el-table-column label="操作类型" align="center" prop="TYPE" />
      <el-table-column label="销售订单号" align="center" prop="vbelnSo" />
      <el-table-column label="销售交货单号" align="center" prop="vbelnDr" />
      <el-table-column label="装运点" align="center" prop="VSTEL" />
      <el-table-column label="计划发货日期" align="center" prop="LFDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.LFDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="抬头备注" align="center" prop="hMemo" />
      <el-table-column label="采购内向交货单单号" align="center" prop="purchaseNo" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales/purDelivery:purDelivery:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales/purDelivery:purDelivery:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购内向交货单头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="系统编码" prop="ivSysno">
          <el-input v-model="form.ivSysno" placeholder="请输入系统编码" />
        </el-form-item>
        <el-form-item label="接口编码" prop="ivItfno">
          <el-input v-model="form.ivItfno" placeholder="请输入接口编码" />
        </el-form-item>
        <el-form-item label="销售订单号" prop="vbelnSo">
          <el-input v-model="form.vbelnSo" placeholder="请输入销售订单号" />
        </el-form-item>
        <el-form-item label="销售交货单号" prop="vbelnDr">
          <el-input v-model="form.vbelnDr" placeholder="请输入销售交货单号" />
        </el-form-item>
        <el-form-item label="装运点" prop="VSTEL">
          <el-input v-model="form.VSTEL" placeholder="请输入装运点" />
        </el-form-item>
        <el-form-item label="计划发货日期" prop="LFDAT">
          <el-date-picker clearable
            v-model="form.LFDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划发货日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="抬头备注" prop="hMemo">
          <el-input v-model="form.hMemo" placeholder="请输入抬头备注" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="采购内向交货单单号" prop="purchaseNo">
          <el-input v-model="form.purchaseNo" placeholder="请输入采购内向交货单单号" />
        </el-form-item>
        <el-divider content-position="center">采购内向交货单行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddPruchaseDeliveryLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeletePruchaseDeliveryLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="pruchaseDeliveryLineList" :row-class-name="rowPruchaseDeliveryLineIndex" @selection-change="handlePruchaseDeliveryLineSelectionChange" ref="pruchaseDeliveryLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="行号" prop="POSNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.POSNR" placeholder="请输入行号" />
            </template>
          </el-table-column>
          <el-table-column label="物料号" prop="MATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MATNR" placeholder="请输入物料号" />
            </template>
          </el-table-column>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="KWMENG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KWMENG" placeholder="请输入数量" />
            </template>
          </el-table-column>
          <el-table-column label="工厂" prop="WERKS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.WERKS" placeholder="请输入工厂" />
            </template>
          </el-table-column>
          <el-table-column label="库存地点" prop="LGORT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.LGORT" placeholder="请输入库存地点" />
            </template>
          </el-table-column>
          <el-table-column label="行备注" prop="iMemo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.iMemo" placeholder="请输入行备注" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurDelivery, getPurDelivery, delPurDelivery, addPurDelivery, updatePurDelivery } from "@/api/sales/purDelivery/purDelivery";

export default {
  name: "PurDelivery",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPruchaseDeliveryLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购内向交货单头信息表格数据
      purDeliveryList: [],
      // 采购内向交货单行信息表格数据
      pruchaseDeliveryLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vbelnSo: null,
        vbelnDr: null,
        purchaseNo: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购内向交货单头信息列表 */
    getList() {
      this.loading = true;
      listPurDelivery(this.queryParams).then(response => {
        this.purDeliveryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purchaseHeadId: null,
        ivSysno: null,
        ivItfno: null,
        TYPE: null,
        vbelnSo: null,
        vbelnDr: null,
        VSTEL: null,
        LFDAT: null,
        hMemo: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        purchaseNo: null
      };
      this.pruchaseDeliveryLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.purchaseHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购内向交货单头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const purchaseHeadId = row.purchaseHeadId || this.ids
      getPurDelivery(purchaseHeadId).then(response => {
        this.form = response.data;
        this.pruchaseDeliveryLineList = response.data.pruchaseDeliveryLineList;
        this.open = true;
        this.title = "修改采购内向交货单头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.pruchaseDeliveryLineList = this.pruchaseDeliveryLineList;
          if (this.form.purchaseHeadId != null) {
            updatePurDelivery(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurDelivery(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const purchaseHeadIds = row.purchaseHeadId || this.ids;
      this.$modal.confirm('是否确认删除采购内向交货单头信息编号为"' + purchaseHeadIds + '"的数据项？').then(function() {
        return delPurDelivery(purchaseHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 采购内向交货单行信息序号 */
    rowPruchaseDeliveryLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 采购内向交货单行信息添加按钮操作 */
    handleAddPruchaseDeliveryLine() {
      let obj = {};
      obj.POSNR = "";
      obj.MATNR = "";
      obj.ZZMATNR = "";
      obj.KWMENG = "";
      obj.WERKS = "";
      obj.LGORT = "";
      obj.iMemo = "";
      this.pruchaseDeliveryLineList.push(obj);
    },
    /** 采购内向交货单行信息删除按钮操作 */
    handleDeletePruchaseDeliveryLine() {
      if (this.checkedPruchaseDeliveryLine.length == 0) {
        this.$modal.msgError("请先选择要删除的采购内向交货单行信息数据");
      } else {
        const pruchaseDeliveryLineList = this.pruchaseDeliveryLineList;
        const checkedPruchaseDeliveryLine = this.checkedPruchaseDeliveryLine;
        this.pruchaseDeliveryLineList = pruchaseDeliveryLineList.filter(function(item) {
          return checkedPruchaseDeliveryLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handlePruchaseDeliveryLineSelectionChange(selection) {
      this.checkedPruchaseDeliveryLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/purDelivery/purDelivery/export', {
        ...this.queryParams
      }, `purDelivery_.xlsx`)
    }
  }
};
</script>
