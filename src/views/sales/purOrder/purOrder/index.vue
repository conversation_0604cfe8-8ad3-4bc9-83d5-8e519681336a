<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="SAP采购申请编号" prop="EBELN">
        <el-input
          v-model="queryParams.EBELN"
          placeholder="请输入SAP采购申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购订单凭证类型" prop="BSART">
        <el-input
          v-model="queryParams.BSART"
          placeholder="请输入采购订单凭证类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="BUKRS">
        <el-input
          v-model="queryParams.BUKRS"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购组织" prop="EKORG">
        <el-input
          v-model="queryParams.EKORG"
          placeholder="请输入采购组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购组" prop="EKGRP">
        <el-input
          v-model="queryParams.EKGRP"
          placeholder="请输入采购组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商编码" prop="LIFNR">
        <el-input
          v-model="queryParams.LIFNR"
          placeholder="请输入供应商编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAS采购申请编号" prop="purOrderNo">
        <el-input
          v-model="queryParams.purOrderNo"
          placeholder="请输入SAS采购申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales/purOrder:purOrder:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales/purOrder:purOrder:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales/purOrder:purOrder:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales/purOrder:purOrder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="purOrderHeadId" />
      <el-table-column label="SAP采购申请编号" align="center" prop="EBELN" />
      <el-table-column label="采购订单凭证类型" align="center" prop="BSART" />
      <el-table-column label="公司代码" align="center" prop="BUKRS" />
      <el-table-column label="采购组织" align="center" prop="EKORG" />
      <el-table-column label="采购组" align="center" prop="EKGRP" />
      <el-table-column label="供应商编码" align="center" prop="LIFNR" />
      <el-table-column label="SAS采购申请编号" align="center" prop="purOrderNo" />
      <el-table-column label="单据状态" align="center" prop="docStatus" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales/purOrder:purOrder:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales/purOrder:purOrder:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购订单头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="SAP采购申请编号" prop="EBELN">
          <el-input v-model="form.EBELN" placeholder="请输入SAP采购申请编号" />
        </el-form-item>
        <el-form-item label="采购订单凭证类型" prop="BSART">
          <el-input v-model="form.BSART" placeholder="请输入采购订单凭证类型" />
        </el-form-item>
        <el-form-item label="公司代码" prop="BUKRS">
          <el-input v-model="form.BUKRS" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="采购组织" prop="EKORG">
          <el-input v-model="form.EKORG" placeholder="请输入采购组织" />
        </el-form-item>
        <el-form-item label="采购组" prop="EKGRP">
          <el-input v-model="form.EKGRP" placeholder="请输入采购组" />
        </el-form-item>
        <el-form-item label="供应商编码" prop="LIFNR">
          <el-input v-model="form.LIFNR" placeholder="请输入供应商编码" />
        </el-form-item>
        <el-form-item label="SAS采购申请编号" prop="purOrderNo">
          <el-input v-model="form.purOrderNo" placeholder="请输入SAS采购申请编号" />
        </el-form-item>
        <el-form-item label="${comment}" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入${comment}" />
        </el-form-item>
        <el-divider content-position="center">采购订单行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddPurchaseOrderLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeletePurchaseOrderLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="purchaseOrderLineList" :row-class-name="rowPurchaseOrderLineIndex" @selection-change="handlePurchaseOrderLineSelectionChange" ref="purchaseOrderLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="采购申请编号" prop="EBELN" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.EBELN" placeholder="请输入采购申请编号" />
            </template>
          </el-table-column>
          <el-table-column label="采购申请行项目" prop="EBELP" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.EBELP" placeholder="请输入采购申请行项目" />
            </template>
          </el-table-column>
          <el-table-column label="物料号" prop="MATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MATNR" placeholder="请输入物料号" />
            </template>
          </el-table-column>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="KWMENG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KWMENG" placeholder="请输入数量" />
            </template>
          </el-table-column>
          <el-table-column label="工厂" prop="WERKS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.WERKS" placeholder="请输入工厂" />
            </template>
          </el-table-column>
          <el-table-column label="库存地点" prop="LGORT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.LGORT" placeholder="请输入库存地点" />
            </template>
          </el-table-column>
          <el-table-column label="行备注" prop="iMemo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.iMemo" placeholder="请输入行备注" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurOrder, getPurOrder, delPurOrder, addPurOrder, updatePurOrder } from "@/api/sales/purOrder/purOrder";

export default {
  name: "PurOrder",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedPurchaseOrderLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购订单头信息表格数据
      purOrderList: [],
      // 采购订单行信息表格数据
      purchaseOrderLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        EBELN: null,
        BSART: null,
        BUKRS: null,
        EKORG: null,
        EKGRP: null,
        LIFNR: null,
        purOrderNo: null,
        docStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购订单头信息列表 */
    getList() {
      this.loading = true;
      listPurOrder(this.queryParams).then(response => {
        this.purOrderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purOrderHeadId: null,
        EBELN: null,
        BSART: null,
        BUKRS: null,
        EKORG: null,
        EKGRP: null,
        LIFNR: null,
        purOrderNo: null,
        docStatus: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.purchaseOrderLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.purOrderHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购订单头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const purOrderHeadId = row.purOrderHeadId || this.ids
      getPurOrder(purOrderHeadId).then(response => {
        this.form = response.data;
        this.purchaseOrderLineList = response.data.purchaseOrderLineList;
        this.open = true;
        this.title = "修改采购订单头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.purchaseOrderLineList = this.purchaseOrderLineList;
          if (this.form.purOrderHeadId != null) {
            updatePurOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const purOrderHeadIds = row.purOrderHeadId || this.ids;
      this.$modal.confirm('是否确认删除采购订单头信息编号为"' + purOrderHeadIds + '"的数据项？').then(function() {
        return delPurOrder(purOrderHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 采购订单行信息序号 */
    rowPurchaseOrderLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 采购订单行信息添加按钮操作 */
    handleAddPurchaseOrderLine() {
      let obj = {};
      obj.EBELN = "";
      obj.EBELP = "";
      obj.MATNR = "";
      obj.ZZMATNR = "";
      obj.KWMENG = "";
      obj.WERKS = "";
      obj.LGORT = "";
      obj.iMemo = "";
      this.purchaseOrderLineList.push(obj);
    },
    /** 采购订单行信息删除按钮操作 */
    handleDeletePurchaseOrderLine() {
      if (this.checkedPurchaseOrderLine.length == 0) {
        this.$modal.msgError("请先选择要删除的采购订单行信息数据");
      } else {
        const purchaseOrderLineList = this.purchaseOrderLineList;
        const checkedPurchaseOrderLine = this.checkedPurchaseOrderLine;
        this.purchaseOrderLineList = purchaseOrderLineList.filter(function(item) {
          return checkedPurchaseOrderLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handlePurchaseOrderLineSelectionChange(selection) {
      this.checkedPurchaseOrderLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/purOrder/purOrder/export', {
        ...this.queryParams
      }, `purOrder_.xlsx`)
    }
  }
};
</script>
