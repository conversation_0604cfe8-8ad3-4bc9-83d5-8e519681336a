<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 3 }"
      :tableProps="tableProps"
      :searchInfo="searchInfo"
      ref="ComTableList"
     
    >
      <template #searchButton>
        <FileUpload 
        :fileType="['xls', 'xlsx']"
        :title="'导入'"
        :action="'/sales/newConfig/importData'" :isShowTip="false"
        @getUploadExcel="getUploadExcel"
        >
      </FileUpload>
         <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:newConfig:importTemplate']"
        >导入模板</el-button>
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="gotoAdd({},'add')"
          v-hasPermi="['system:newConfig:add']"
        >新增</el-button>
      </template>
       <template
         v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
        />
      </template>
       <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd(row,'edit')"
            v-hasPermi="['system:newConfig:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="gotoAdd(row,'look')"
            v-hasPermi="['system:newConfig:detail']"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComSearch from "@/components/ComSearch/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import FileUpload from '@/components/FileUpload/index.vue'

import { listNewConfig } from "@/api/sales/newConfig";

import {
  columns,
  optionsCodeList,
  dictOptionCodeList
} from "./config";

export default {
  name: "Launch",
  dicts: ['zsas_ybbk','del_flag'],
  dictsKey: ['VKORG'],
  components: {
    ComSearch,
    ComTableList,
    FileUpload
  },
  data() {
    return {
      searchInfo:{
        docStatus:'',
      },
      optionsCodeList,
      columns:columns,//columns.filter(({prop})=>(!['zsasStartdate,zsasEnddate'].includes(prop))),
      searchData: columns.slice(0,3),
      dictOptionCodeList,
      tableProps: {
        // height: 600,
      },
    };
  },
  computed:{
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  created() {
    // console.log(optionsCodeList,'optionsCodeList')
  },
  methods: {
   
    /** 查询销售订单头信息列表 */
    async getList(params) {
      const res = await listNewConfig(params);
      return res;
     
    },
     /** 导出按钮操作 */
    handleExport() {
      this.download('/sales/newConfig/importTemplate', {
      }, `新品投放_.xlsx`)
    },
    getUploadExcel(){
       this.$refs.ComTableList.submitEmits({})
    },
    gotoAdd(data = {},type){
       this.$store
        .dispatch("tagsView/delPage", { name: "LaunchDetail" })
        .then(() => {
          this.$router.push(`/sales/newproduct/launch-detail/index/${type}/${data?.stationNewConfigId || undefined}`);
        });
    }
  },
};
</script>

<style lang="scss" scoped>

.app-container{
  ::v-deep .upload-file{
    display: inline-block;
    margin-right: 10px;
  }
}

</style>
