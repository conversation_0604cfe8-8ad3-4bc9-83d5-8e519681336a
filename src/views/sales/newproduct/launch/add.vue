<template>
  <div class="salesAdd" v-loading="loading">
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="columns.length"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      @submitEmits="submitEmits"
    >
      <template #searchButton v-if="pageType !== 'look'">
        <el-button
          v-hasPermi="['system:newConfig:save']"
          type="primary"
          size="mini"
          @click="submitTip('save')"
        >
          {{ detail.stationNewConfigId ? "修改" : "保存" }}
        </el-button>
      </template>
    </ComSearch>
  </div>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import { columns,rules } from "./config";
import { getNewConfig,addNewConfig, updateNewConfig } from "@/api/sales/newConfig";

export default {
  name: "LaunchDetail",
  dicts: ['zsas_ybbk','del_flag'],
  dictsKey: ['VKORG'],
  components: {
    ComSearch,
  },
  props: {},
  computed: {
    searchData(){
      let data = JSON.parse(JSON.stringify(columns)).map((item)=>{
        if(this.pageType === 'add' && ['werks','zzmatnr'].includes(item.prop)){
          item.attr= {
            disabled: false,
            }
        }else if(['werks','zzmatnr'].includes(item.prop)){
           item.attr= {
            disabled: true,
            }
        }
        return item
      })
      return data
    }
  },
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      rules: rules,
      pageType,
      columns: columns,
      searchInfo: {
        delFlag:'0'
      },
      detail: {
        stationNewConfigId:''
      },
    };
  },
  created() {
    this.getDetail();
  },
  watch: {},
  methods: {
    submitTip(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    getDetail() {
      if (this.pageType === 'add' || this.loading) return;
      this.loading = true;
      getNewConfig(this.$route.params.id)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.detail = res?.data;
            this.searchInfo = {
              ...res?.data,
            };
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
          this.detail = {};
        });
    },
    submitEmits(data) {
      if (this.loading) return;
      this.loading = true;
      let fun = addNewConfig
      if(this.detail.stationNewConfigId){
        fun = updateNewConfig
      }
      fun({
        ...this.detail,
        ...data,
      })
        .then((response) => {
          this.loading = false;
          this.$modal.msgSuccess("修改成功");
          if(response?.stationNewConfigId && !this?.detail?.stationNewConfigId){
             this.$store
              .dispatch("tagsView/delPage", { name: "LaunchDetail" })
              .then(() => {
                this.$router.replace(`/sales/newproduct/launch-detail/index/edit/${response.stationNewConfigId}`);
              });
          }else{
            this.getDetail()
          }
          
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.salesAdd {
  padding: 20px;
}
</style>
