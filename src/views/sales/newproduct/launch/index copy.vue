<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工厂" prop="werks">
        <el-input
          v-model="queryParams.werks"
          placeholder="请输入工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专卖物料号" prop="zzmatnr">
        <el-input
          v-model="queryParams.zzmatnr"
          placeholder="请输入专卖物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务板块" prop="zsasYbbk">
        <el-input
          v-model="queryParams.zsasYbbk"
          placeholder="请输入业务板块"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始日期" prop="zsasStartdate">
        <el-input
          v-model="queryParams.zsasStartdate"
          placeholder="请输入开始日期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结束日期" prop="zsasEnddate">
        <el-input
          v-model="queryParams.zsasEnddate"
          placeholder="请输入结束日期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:newConfig:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:newConfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:newConfig:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:newConfig:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="newConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="stationNewConfigId" />
      <el-table-column label="工厂" align="center" prop="werks" />
      <el-table-column label="专卖物料号" align="center" prop="zzmatnr" />
      <el-table-column label="业务板块" align="center" prop="zsasYbbk" />
      <el-table-column label="产品子系列" align="center" prop="zsasCpzxl" />
      <el-table-column label="用途一" align="center" prop="zsasYt1" />
      <el-table-column label="排放描述" align="center" prop="zsasPfms" />
      <el-table-column label="储备类别" align="center" prop="zsasCblb" />
      <el-table-column label="备注" align="center" prop="zsasBz" />
      <el-table-column label="开始日期" align="center" prop="zsasStartdate" />
      <el-table-column label="结束日期" align="center" prop="zsasEnddate" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:newConfig:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:newConfig:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改服务站新品投放清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工厂" prop="werks">
          <el-input v-model="form.werks" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="专卖物料号" prop="zzmatnr">
          <el-input v-model="form.zzmatnr" placeholder="请输入专卖物料号" />
        </el-form-item>
        <el-form-item label="业务板块" prop="zsasYbbk">
          <el-input v-model="form.zsasYbbk" placeholder="请输入业务板块" />
        </el-form-item>
        <el-form-item label="产品子系列" prop="zsasCpzxl">
          <el-input v-model="form.zsasCpzxl" placeholder="请输入产品子系列" />
        </el-form-item>
        <el-form-item label="用途一" prop="zsasYt1">
          <el-input v-model="form.zsasYt1" placeholder="请输入用途一" />
        </el-form-item>
        <el-form-item label="排放描述" prop="zsasPfms">
          <el-input v-model="form.zsasPfms" placeholder="请输入排放描述" />
        </el-form-item>
        <el-form-item label="储备类别" prop="zsasCblb">
          <el-input v-model="form.zsasCblb" placeholder="请输入储备类别" />
        </el-form-item>
        <el-form-item label="备注" prop="zsasBz">
          <el-input v-model="form.zsasBz" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="开始日期" prop="zsasStartdate">
          <el-input v-model="form.zsasStartdate" placeholder="请输入开始日期" />
        </el-form-item>
        <el-form-item label="结束日期" prop="zsasEnddate">
          <el-input v-model="form.zsasEnddate" placeholder="请输入结束日期" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNewConfig, getNewConfig, delNewConfig, addNewConfig, updateNewConfig } from "@/api/sales/newConfig";

export default {
  name: "NewConfig",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 服务站新品投放清单表格数据
      newConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        werks: null,
        zzmatnr: null,
        zsasYbbk: null,
        zsasStartdate: null,
        zsasEnddate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询服务站新品投放清单列表 */
    getList() {
      this.loading = true;
      listNewConfig(this.queryParams).then(response => {
        this.newConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(()=>{
        this.loading = false
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        stationNewConfigId: null,
        werks: null,
        zzmatnr: null,
        zsasYbbk: null,
        zsasCpzxl: null,
        zsasYt1: null,
        zsasPfms: null,
        zsasCblb: null,
        zsasBz: null,
        zsasStartdate: null,
        zsasEnddate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.stationNewConfigId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加服务站新品投放清单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const stationNewConfigId = row.stationNewConfigId || this.ids
      getNewConfig(stationNewConfigId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改服务站新品投放清单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.stationNewConfigId != null) {
            updateNewConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNewConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const stationNewConfigIds = row.stationNewConfigId || this.ids;
      this.$modal.confirm('是否确认删除服务站新品投放清单编号为"' + stationNewConfigIds + '"的数据项？').then(function() {
        return delNewConfig(stationNewConfigIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/newConfig/export', {
        ...this.queryParams
      }, `newConfig_.xlsx`)
    }
  }
};
</script>
