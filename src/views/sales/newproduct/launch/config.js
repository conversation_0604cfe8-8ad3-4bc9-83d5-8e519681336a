export const columns = [
  {
    label: "工厂",
    type: "input",
    prop: "werks",
    dictOptionCode: "VKORG",
    append: true,
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
    attr: {
      // disabled: true,
    },
  },
  {
    label: "专卖物料号",
    type: "input",
    prop: "zzmatnr",
    append: true,
    attr: {
      // disabled: true,
    },
    backProps: [
      {
        backProp: "ZZMATNR",
        backLable: "ZZMATNR",
        prop: "zzmatnr",
      },
    ],
  },
  {
    label: "业务板块",
    type: "select",
    prop: "zsasYbbk",
    slotName:'zsasYbbk',
    optionsCode:'zsas_ybbk',
  },

    {
    label: "产品子系列",
    type: "input",
    prop: "zsasCpzxl",
  },

  
    {
    label: "用途一",
    type: "input",
    prop: "zsasYt1",
  },

   {
    label: "排放描述",
    type: "input",
    prop: "zsasPfms",
  },

   {
    label: "储备类别",
    type: "input",
    prop: "zsasCblb",
  },


   {
    label: "备注",
    type: "input",
    prop: "zsasBz",
    searchOut:true
  },


  {
    label: "开始日期",
    type: "input",
    prop: "zsasStartdate",
    hiddenSearch: true,
  },
  {
    label: "结束日期",
    type: "input",
    prop: "zsasEnddate",
    hiddenSearch: true,
  },
   {
    prop: "zsasStartdate,zsasEnddate",
    label: "开始/结束日期",
    width: 140,
    type: "daterange",
  },
  {
    label: "生效标识",
    type: "select",
    prop: "delFlag",
    optionsCode:'del_flag',
    fixed: "right",
    backProps: [
    {
      backProp: "value",
      backLable: "label",
      prop: "delFlag",
    },
  ],
  },
   {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
];


export const rules = {
  werks: [{ required: true, message: "请选择工厂", trigger: "change" }],
  zzmatnr: [{ required: true, message: "请选择专卖物料号", trigger: "change" }],
  zsasYbbk: [{ required: true, message: "请选择业务板块", trigger: "change" }],
  zsasCpzxl: [{ required: true, message: "请选择产品子系列", trigger: "change" }],
};


// 需要接口请求的数据
export const dictOptionCodeList = columns.filter(
  ({ dictOptionCode }) => dictOptionCode
);

// 映射code
export const optionsCodeList = columns.filter(
  ({ optionsCode }) => optionsCode
);

const ListKeys = [];
optionsCodeList.forEach((item) => {
  ListKeys.push(item.optionsCode);
});

export const optionsCodeListKeys = ListKeys;