<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="SAP销售订单号" prop="VBELN">
        <el-input
          v-model="queryParams.VBELN"
          placeholder="请输入SAP销售订单单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报价单类型" prop="AUART">
        <el-input
          v-model="queryParams.AUART"
          placeholder="请输入报价单类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="销售组织" prop="VKORG">
        <el-input
          v-model="queryParams.VKORG"
          placeholder="请输入销售组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales/salesOrder:head:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales/salesOrder:head:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales/salesOrder:head:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales/salesOrder:head:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="headList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="salesHeadId" />
      <el-table-column label="系统编码" align="center" prop="ivSysno" />
      <el-table-column label="接口编码" align="center" prop="ivItfno" />
      <el-table-column label="操作类型" align="center" prop="TYPE" />
      <el-table-column label="SAP销售订单单号" align="center" prop="VBELN" />
      <el-table-column label="报价单类型" align="center" prop="AUART" />
      <el-table-column label="订单类型" align="center" prop="ordertype" />
      <el-table-column label="销售组织" align="center" prop="VKORG" />
      <el-table-column label="分销渠道" align="center" prop="VTWEG" />
      <el-table-column label="产品组" align="center" prop="SPART" />
      <el-table-column label="销售办公室" align="center" prop="VKBUR" />
      <el-table-column label="销售组" align="center" prop="VKGRP" />
      <el-table-column label="工厂" align="center" prop="WERKS" />
      <el-table-column label="采购订单编号" align="center" prop="BSTKD" />
      <el-table-column label="创建人" align="center" prop="ERNAM" />
      <el-table-column label="单据日期" align="center" prop="AUDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.AUDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="ERDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ERDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="定价日期" align="center" prop="PRSDT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.PRSDT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="需求有效日期从" align="center" prop="ANGDT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.ANGDT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="需求有效日期至" align="center" prop="BNDDT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.BNDDT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户需求时间" align="center" prop="BSTDK" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.BSTDK, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否急件" align="center" prop="ZZSFFK" />
      <el-table-column label="紧急类型" align="center" prop="zzcustomerL2" />
      <el-table-column label="申请原因" align="center" prop="AUGRU" />
      <el-table-column label="要求到货时间" align="center" prop="VDATU" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.VDATU, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用用途" align="center" prop="ABRVW" />
      <el-table-column label="凭证货币" align="center" prop="WAERK" />
      <el-table-column label="价格组" align="center" prop="KONDA" />
      <el-table-column label="客户编号" align="center" prop="KUNNR" />
      <el-table-column label="销售订单来源系统" align="center" prop="ihrezE" />
      <el-table-column label="发运方式" align="center" prop="shipMethod" />
      <el-table-column label="收货地址" align="center" prop="harvestAddress" />
      <el-table-column label="整改文件号" align="center" prop="recDocNo" />
      <el-table-column label="客户联系人" align="center" prop="IHREZ" />
      <el-table-column label="客户联系人电话" align="center" prop="TELF1" />
      <el-table-column label="收货省" align="center" prop="province" />
      <el-table-column label="收货市" align="center" prop="city" />
      <el-table-column label="收货县" align="center" prop="region" />
      <el-table-column label="抬头备注" align="center" prop="hMemo" />
      <el-table-column label="SAS销售订单单号" align="center" prop="salesNo" />
      <el-table-column label="单据状态" align="center" prop="docStatus" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales/salesOrder:head:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales/salesOrder:head:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改销售订单头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="系统编码" prop="ivSysno">
          <el-input v-model="form.ivSysno" placeholder="请输入系统编码" />
        </el-form-item>
        <el-form-item label="接口编码" prop="ivItfno">
          <el-input v-model="form.ivItfno" placeholder="请输入接口编码" />
        </el-form-item>
        <el-form-item label="SAP销售订单单号" prop="VBELN">
          <el-input v-model="form.VBELN" placeholder="请输入SAP销售订单单号" />
        </el-form-item>
        <el-form-item label="报价单类型" prop="AUART">
          <el-input v-model="form.AUART" placeholder="请输入报价单类型" />
        </el-form-item>
        <el-form-item label="销售组织" prop="VKORG">
          <el-input v-model="form.VKORG" placeholder="请输入销售组织" />
        </el-form-item>
        <el-form-item label="分销渠道" prop="VTWEG">
          <el-input v-model="form.VTWEG" placeholder="请输入分销渠道" />
        </el-form-item>
        <el-form-item label="产品组" prop="SPART">
          <el-input v-model="form.SPART" placeholder="请输入产品组" />
        </el-form-item>
        <el-form-item label="销售办公室" prop="VKBUR">
          <el-input v-model="form.VKBUR" placeholder="请输入销售办公室" />
        </el-form-item>
        <el-form-item label="销售组" prop="VKGRP">
          <el-input v-model="form.VKGRP" placeholder="请输入销售组" />
        </el-form-item>
        <el-form-item label="工厂" prop="WERKS">
          <el-input v-model="form.WERKS" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="采购订单编号" prop="BSTKD">
          <el-input v-model="form.BSTKD" placeholder="请输入采购订单编号" />
        </el-form-item>
        <el-form-item label="创建人" prop="ERNAM">
          <el-input v-model="form.ERNAM" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="单据日期" prop="AUDAT">
          <el-date-picker clearable
            v-model="form.AUDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择单据日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建日期" prop="ERDAT">
          <el-date-picker clearable
            v-model="form.ERDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="定价日期" prop="PRSDT">
          <el-date-picker clearable
            v-model="form.PRSDT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择定价日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="需求有效日期从" prop="ANGDT">
          <el-date-picker clearable
            v-model="form.ANGDT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择需求有效日期从">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="需求有效日期至" prop="BNDDT">
          <el-date-picker clearable
            v-model="form.BNDDT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择需求有效日期至">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户需求时间" prop="BSTDK">
          <el-date-picker clearable
            v-model="form.BSTDK"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择客户需求时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否急件" prop="ZZSFFK">
          <el-input v-model="form.ZZSFFK" placeholder="请输入是否急件" />
        </el-form-item>
        <el-form-item label="紧急类型" prop="zzcustomerL2">
          <el-input v-model="form.zzcustomerL2" placeholder="请输入紧急类型" />
        </el-form-item>
        <el-form-item label="申请原因" prop="AUGRU">
          <el-input v-model="form.AUGRU" placeholder="请输入申请原因" />
        </el-form-item>
        <el-form-item label="要求到货时间" prop="VDATU">
          <el-date-picker clearable
            v-model="form.VDATU"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择要求到货时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="使用用途" prop="ABRVW">
          <el-input v-model="form.ABRVW" placeholder="请输入使用用途" />
        </el-form-item>
        <el-form-item label="凭证货币" prop="WAERK">
          <el-input v-model="form.WAERK" placeholder="请输入凭证货币" />
        </el-form-item>
        <el-form-item label="价格组" prop="KONDA">
          <el-input v-model="form.KONDA" placeholder="请输入价格组" />
        </el-form-item>
        <el-form-item label="客户编号" prop="KUNNR">
          <el-input v-model="form.KUNNR" placeholder="请输入客户编号" />
        </el-form-item>
        <el-form-item label="销售订单来源系统" prop="ihrezE">
          <el-input v-model="form.ihrezE" placeholder="请输入销售订单来源系统" />
        </el-form-item>
        <el-form-item label="发运方式" prop="shipMethod">
          <el-input v-model="form.shipMethod" placeholder="请输入发运方式" />
        </el-form-item>
        <el-form-item label="收货地址" prop="harvestAddress">
          <el-input v-model="form.harvestAddress" placeholder="请输入收货地址" />
        </el-form-item>
        <el-form-item label="整改文件号" prop="recDocNo">
          <el-input v-model="form.recDocNo" placeholder="请输入整改文件号" />
        </el-form-item>
        <el-form-item label="客户联系人" prop="IHREZ">
          <el-input v-model="form.IHREZ" placeholder="请输入客户联系人" />
        </el-form-item>
        <el-form-item label="客户联系人电话" prop="TELF1">
          <el-input v-model="form.TELF1" placeholder="请输入客户联系人电话" />
        </el-form-item>
        <el-form-item label="收货省" prop="province">
          <el-input v-model="form.province" placeholder="请输入收货省" />
        </el-form-item>
        <el-form-item label="收货市" prop="city">
          <el-input v-model="form.city" placeholder="请输入收货市" />
        </el-form-item>
        <el-form-item label="收货县" prop="region">
          <el-input v-model="form.region" placeholder="请输入收货县" />
        </el-form-item>
        <el-form-item label="抬头备注" prop="hMemo">
          <el-input v-model="form.hMemo" placeholder="请输入抬头备注" />
        </el-form-item>
        <el-form-item label="SAS销售订单单号" prop="salesNo">
          <el-input v-model="form.salesNo" placeholder="请输入SAS销售订单单号" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-divider content-position="center">销售订单行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddSalesOrderLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteSalesOrderLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="salesOrderLineList" :row-class-name="rowSalesOrderLineIndex" @selection-change="handleSalesOrderLineSelectionChange" ref="salesOrderLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="SAP销售订单单号" prop="VBELN" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.VBELN" placeholder="请输入SAP销售订单单号" />
            </template>
          </el-table-column>
          <el-table-column label="行号" prop="POSNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.POSNR" placeholder="请输入行号" />
            </template>
          </el-table-column>
          <el-table-column label="物料号" prop="MATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MATNR" placeholder="请输入物料号" />
            </template>
          </el-table-column>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="申请数量" prop="KWMENG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KWMENG" placeholder="请输入申请数量" />
            </template>
          </el-table-column>
          <el-table-column label="含税单价" prop="KBETR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KBETR" placeholder="请输入含税单价" />
            </template>
          </el-table-column>
          <el-table-column label="不含税单价" prop="KBETR1" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KBETR1" placeholder="请输入不含税单价" />
            </template>
          </el-table-column>
          <el-table-column label="发动机编号" prop="bstkdE" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.bstkdE" placeholder="请输入发动机编号" />
            </template>
          </el-table-column>
          <el-table-column label="行备注" prop="iMemo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.iMemo" placeholder="请输入行备注" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHead, getHead, delHead, addHead, updateHead } from "@/api/sales/salesOrder/head";

export default {
  name: "Head",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedSalesOrderLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销售订单头信息表格数据
      headList: [],
      // 销售订单行信息表格数据
      salesOrderLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        VBELN: null,
        AUART: null,
        ordertype: null,
        VKORG: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    getList() {
      this.loading = true;
      listHead(this.queryParams).then(response => {
        this.headList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        salesHeadId: null,
        ivSysno: null,
        ivItfno: null,
        TYPE: null,
        VBELN: null,
        AUART: null,
        ordertype: null,
        VKORG: null,
        VTWEG: null,
        SPART: null,
        VKBUR: null,
        VKGRP: null,
        WERKS: null,
        BSTKD: null,
        ERNAM: null,
        AUDAT: null,
        ERDAT: null,
        PRSDT: null,
        ANGDT: null,
        BNDDT: null,
        BSTDK: null,
        ZZSFFK: null,
        zzcustomerL2: null,
        AUGRU: null,
        VDATU: null,
        ABRVW: null,
        WAERK: null,
        KONDA: null,
        KUNNR: null,
        ihrezE: null,
        shipMethod: null,
        harvestAddress: null,
        recDocNo: null,
        IHREZ: null,
        TELF1: null,
        province: null,
        city: null,
        region: null,
        hMemo: null,
        salesNo: null,
        docStatus: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.salesOrderLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.salesHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加销售订单头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const salesHeadId = row.salesHeadId || this.ids
      getHead(salesHeadId).then(response => {
        this.form = response.data;
        this.salesOrderLineList = response.data.salesOrderLineList;
        this.open = true;
        this.title = "修改销售订单头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.salesOrderLineList = this.salesOrderLineList;
          if (this.form.salesHeadId != null) {
            updateHead(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHead(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const salesHeadIds = row.salesHeadId || this.ids;
      this.$modal.confirm('是否确认删除销售订单头信息编号为"' + salesHeadIds + '"的数据项？').then(function() {
        return delHead(salesHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 销售订单行信息序号 */
    rowSalesOrderLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 销售订单行信息添加按钮操作 */
    handleAddSalesOrderLine() {
      let obj = {};
      obj.VBELN = "";
      obj.POSNR = "";
      obj.MATNR = "";
      obj.ZZMATNR = "";
      obj.KWMENG = "";
      obj.KBETR = "";
      obj.KBETR1 = "";
      obj.bstkdE = "";
      obj.iMemo = "";
      this.salesOrderLineList.push(obj);
    },
    /** 销售订单行信息删除按钮操作 */
    handleDeleteSalesOrderLine() {
      if (this.checkedSalesOrderLine.length == 0) {
        this.$modal.msgError("请先选择要删除的销售订单行信息数据");
      } else {
        const salesOrderLineList = this.salesOrderLineList;
        const checkedSalesOrderLine = this.checkedSalesOrderLine;
        this.salesOrderLineList = salesOrderLineList.filter(function(item) {
          return checkedSalesOrderLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleSalesOrderLineSelectionChange(selection) {
      this.checkedSalesOrderLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/salesOrder/head/export', {
        ...this.queryParams
      }, `head_.xlsx`)
    }
  }
};
</script>
