<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="90%"
    class="ComModalSearch"
    @close="dialogVisible = false"
  >
    <div v-loading="loading">
      <ComSearch
        :searchData="caigouLineColumn"
        labelWidth="100px"
        :showBtn="false"
        :showHeader="false"
        :searchInfo="searchInfo"
        :rules="rules"
        ref="addBugComSearchInfo"
        :getSearchcallBack="getSearchcallBack"
        :checkSelectMsg="checkSelectMsg"
        @submitEmits="handenOk"
      >
      </ComSearch>

      <LineTable
        keyId="salesLineId"
        :searchDetail="searchDetail"
        :isShowBtnRight="false"
        :btnProps="bugbtnProps"
        :showSelection="false"
        :lineList="salesOrderLineList"
        :lineCloumn="bugColoumsList"
        :requiredKeys="bugrequiredKeys"
        ref="lineTable"
        :dataProps="dataProps"
        :checkNum="checkNum"
        @changeLineList="changeLineList"
      >
      </LineTable>
    </div>

    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: right; margin-top: 20px"
    >
      <el-button
        type="primary"
        size="mini"
        @click="checkData"
        :disabled="loading || salesOrderLineList.length === 0"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import LineTable from "@/components/LineTable/index.vue";
import { addModalObj, caigouLineColumn } from "./config";
import { bugbtnProps, bugbtnProps2 } from "./units";
import { listWerksInfo } from "@/api/sales/werksInfo/werksInfo";
import { addPurApply } from "@/api/purchase/purApply/purApply";
import { formatNowDate, nextFormatNowDate } from "@/utils";
import ComSearch from "@/components/ComSearch/index.vue";
import { changeInfoData } from "@/components/LineTable/units";
import lineLgort from "../../purchase/purApply/lineLgort";
export default {
  name: "AddBug",
  components: {
    LineTable,
    ComSearch,
  },
  mixins: [lineLgort],
  dicts: ["ekgrp"],
  props: {
    lineList: {
      type: Array,
      default() {
        return [];
      },
    },
    WERKS: {
      type: String,
      default() {
        return "";
      },
    },
    // detail: {
    //   type: Object,
    //   default() {
    //     return {};
    //   },
    // },
  },
  data() {
    const type = "bug";
    return {
      // title: '采购申请',
      backList: ["LGORT", "EKGRP"],
      type: type,
      dialogVisible: false,
      caigouLineColumn,
      werksOptions: [],
      searchInfo: {},
      bugColoumsList: addModalObj[type].coloums,
      bugbtnProps: bugbtnProps2,
      salesOrderLineList: this.$props.lineList,
      bugrequiredKeys: addModalObj[type].requiredKeys,
      dataProps: {
        initLineData: addModalObj[type].initLineData,
      },
      detail: {},
      searchDetail: {},
      loading: false,
      rules: {
        werks: [{ required: true, message: "请选择工厂", trigger: "change" }],
        VKORG: [
          { required: true, message: "请选择采购销售组织", trigger: "change" },
        ],
        LGORT: [
          { required: true, message: "请选择收货地点", trigger: "change" },
        ],
        EKGRP: [{ required: true, message: "请选择采购组", trigger: "change" }],
      },
    };
  },
  watch: {
    type(val) {
      // this.title = addModalObj[val].title;
      // console.log(this.werksList,this.VKORGOptions, "werksList");
      this.bugColoumsList = addModalObj[val].coloums;
      this.bugrequiredKeys = addModalObj[val].requiredKeys;
      this.dataProps = {
        initLineData: addModalObj[val].initLineData,
      };
    },
  },
  computed: {
    title() {
      return addModalObj[this.type].title;
    },
  },
  created() {
    // this.location();
    // console.log(this.detail,'detail')
  },
  mounted() {
    //  console.log(this.detail,'detail')
  },
  methods: {
    checkSelectMsg(row, self) {
      const key = self?.$parent?.activeProps?.prop;
      if (key === "LGORT") return;
      try {
        const params = changeInfoData(row);
        const params2 = changeInfoData(this.detail);
        if (params.werks === params2.WERKS) {
          this.$message({
            type: "error",
            message: "选择的工厂和组织不能与原工厂和组织相同",
          });
          return true;
        }

        return false;
      } catch (error) {
        console.log(error, "error");
        return false;
      }
    },
    visibleFuntions(options) {
      this.salesOrderLineList = options?.salesOrderLineList?.map(
        (item, index) => {
          item.id = `${index}`;
          return item;
        }
      );
      this.detail = options?.detail || {};
      this.$refs.lineTable.initProjict();
      this.type = options.type;
      this.WERKS = options.WERKS;
      this.detail = options.detail;

      this.searchInfo = {
        ...this.searchInfo,
        WERKS:options?.WERKS,
        shouWERKS: options?.WERKS,
      };
    },
    checkData() {
      this.$refs.addBugComSearchInfo.onSubmit("add");
    },
    checkNum(item, editItem) {
      if (item.prop === "KWMENG_SQ2") {
        const num1 = editItem.KWMENG ? editItem.KWMENG * 1 : 0;
        const num2 = editItem.KWMENG_SQ ? editItem.KWMENG_SQ * 1 : 0;
        const num = Number(editItem[item.prop]);
        const newNumber = num1 - num2;

        if (`${num}` === "NaN" || num === 0 || num > newNumber) {
          editItem[item.prop] = newNumber;
          this.$message({
            type: "error",
            message: "请输入采购数量大于0、小于等于'申请数量-已采购数量'",
          });
        }
      }
    },
    handenOk(dataInfo) {
      const require = this.$refs.lineTable.checkList();
      if (this.loading || require) return;
      this.loading = true;
      if (!require && this.type === "bug") {
        let list = [...this.$refs.lineTable.newlineList];
        const paramsList = list.map((item, index) => {
          return {
            ...item,
            salesNo: item.salesNo,
            VBELN_SO: item.salesNo,
            ZZMATNR: item.ZZMATNR,
            VBELN: item.VBELN,
            NFPO: `${index * 10 + 10}`,
            BNFPO: `${index * 10 + 10}`,
            // lifnr: "",
            // KWMENG_SQ: item.KWMENG_SQ2 * 1,
            EKGRP: item.EKGRP,
            KWMENG: item.KWMENG_SQ2 * 1,
            delFlag: "0",
            iMemo: item.iMemo,
            salesLineId: item.salesLineId,
            LPEIN: formatNowDate(),
          };
        });

        const params = {
          type: "salesOrder",
          action: "submit",
          // purchaseApplyInfos: paramsList,
          purchaseApplyInfos: {
            ...this.detail,
            ...dataInfo,
            BSART: "NB",
            EKORG: this.detail.VKORG,
            WERKS: this.detail.WERKS,
            AFNAM: this.$store.state.user.name,
            BSTYP: "NB",
            delFlag: "0",
            // VKORG: this.ruleForm.VKORG,
            demandNo:undefined,
            purApplyLineList: paramsList,
            purApplyLineInfoList: paramsList,
          },
        };

        // 采购申请
        addPurApply(params)
          .then((res) => {
            this.dialogVisible = false;
            this.loading = false;
            if (res.code === 200) {
              this.$emit("handenOk", "bugScuess");
              this.$modal.msgSuccess(res.data.msg || "操作成功");
            }
          })
          .catch((error) => {
            console.log(error, "error");
            this.dialogVisible = false;
            this.loading = false;
          });
      } else if (!require && this.type === "delivery") {
        const params = {
          VBELN_SO: this.detail.salesNo,
          salesNo: this.detail.salesNo,
          VBELN_DR: "",
          VSTEL: this.detail.WERKS,
          LFDAT: formatNowDate(),
          hMemo: this.detail.hMemo,
        };

        const list = [...this.salesOrderLineList];
        const paramsList = list.map((item, index) => {
          return {
            BNFPO: `${index * 10 + 10}`,
            ...item,
            EKORG: this.detail.VKORG,
            WERKS: this.detail.WERKS,
            VBELN_SO: this.detail.salesNo,
          };
        });
        params.pruchaseDeliveryLineList = paramsList;

        addSalesDelivery(params);
      }
    },
    changeLineList(val) {
      this.salesOrderLineList = val;
    },
    submitEmits() {},
    // getSearchcallBack(params, activeProps) {
    //   // 根据库存地点回显行库存地点
    //   this.searchDetail = params;
    //   if (activeProps.prop === "LGORT" && params[activeProps.prop]) {
    //     const lineList = [...this.$refs.lineTable.salesOrderLineList];
    //     if (lineList && lineList.length) {
    //       const list = lineList.map((item) => {
    //         let [key = "", value = ""] = params["LGORT,lgortName"];
    //         let label = `${value}`;
    //         if (!key) {
    //           return item;
    //         }
    //         return {
    //           ...item,
    //           LGORT: item?.LGORT || params.LGORT,
    //           "LGORT,lgortName": item?.LGORT
    //             ? item["LGORT,lgortName"]
    //             : params["LGORT,lgortName"],
    //         };
    //       });
    //       this.$refs.lineTable.salesOrderLineList = list;
    //     }
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>
.ComModalSearch {
  ::v-deep .headerLine{
    display: none;
  }
  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }
  ::v-deep .el-dialog__body {
    padding: 0px 20px;
    min-height: 558px;
  }

  ::v-deep .el-table--fluid-height {
    height: calc(100vh - 300px) !important;
  }
}
</style>
