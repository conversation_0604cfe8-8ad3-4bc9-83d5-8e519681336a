<template>
  <div class="salesAdd" v-loading="loading">
    <div @click="closeEdit">
      <ComSearch
        collapseTitle="更多内容"
        :searchData="columns"
        labelWidth="140px"
        :showBtn="false"
        :showNum="9"
        :cancelShow="salesHeadDetail.docStatus ? true : false"
        :handFuntion="handFuntion"
        :searchInfo="searchInfo"
        :rules="rules"
        ref="ComSearchInfo"
        @submitEmits="handSales"
      >
        <template #searchButton v-if="pageType !== 'look'">
          <el-button type="primary" size="mini" @click="submitTip('add')">{{
            salesHeadDetail.docStatus ? "修改" : "保存"
          }}</el-button>

          <el-button
            v-if="['E', 'draft'].includes(salesHeadDetail.docStatus)"
            type="primary"
            size="mini"
            @click="submitTip('submit')"
            >提交</el-button
          >
        </template>
      </ComSearch>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="销售明细" name="sales"> </el-tab-pane>
      <el-tab-pane
        label="交货订单"
        name="pur"
        v-if="salesHeadDetail.docStatus === 'S'"
      >
      </el-tab-pane>
      <el-tab-pane
        label="采购申请"
        name="third"
        v-if="salesHeadDetail.docStatus === 'S'"
      >
      </el-tab-pane>
    </el-tabs>

    <PurDelivery v-show="activeName === 'pur'" ref="PurDelivery" />

    <PurchasePurApply v-show="activeName === 'third'" ref="PurchasePurApply" />

    <LineTable
      v-show="activeName === 'sales'"
      :dataProps="dataProps"
      keyId="salesLineId"
      :isShowRightBtn="true"
      @refreshList="getDetail"
      :btnProps="btnProps"
      :lineList="salesOrderLineList"
      :lineCloumn="salesOrderLineCloumn"
      :cancelFuntion="handFuntion"
      :requiredKeys="['KWMENG', 'ZZMATNR']"
      :checkNum="checkNum"
      ref="lineTable"
      @changeLineList="(val) => (salesOrderLineList = val)"
    >
      <template #lineTableHeader v-if="pageType !== 'look'">
        <el-button
          v-if="salesHeadDetail.docStatus"
          type="danger"
          size="mini"
          @click="getconfirmFuntion('取消数量', 'cancelNum')"
          v-hasPermi="['sales/salesOrder:cancelNum']"
          >批量取消数量</el-button
        >
        <el-button
          v-if="salesHeadDetail.docStatus"
          type="danger"
          size="mini"
          @click="getconfirmFuntion('批量取消', 'cancel')"
          v-hasPermi="['sales/salesOrder:cancelNum']"
          >批量取消</el-button
        >
        <template v-if="salesHeadDetail.docStatus === 'S'">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="getconfirmFuntion('库存预占', 'yuzhan')"
            v-hasPermi="['sales/salesOrder:YZ']"
            >库存预占</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-minus"
            size="mini"
            @click="getconfirmFuntion('库存释放', 'shifnag')"
            v-hasPermi="['sales/salesOrder:SF']"
            >库存释放</el-button
          >

          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="getconfirmFuntion('创建交货单', 'delivery')"
            v-hasPermi="['sales/salesOrder:addJH']"
            >创建交货单</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.native="getconfirmFuntion('转采购申请', 'bug')"
            v-hasPermi="['sales/salesOrder:CG']"
            >转采购申请</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="getconfirmFuntion('转授权采购申请', 'authSalesOrder')"
            v-hasPermi="['sales/salesOrder:ZSQCG']"
            >转授权采购申请</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="getconfirmFuntion('转非授权采购申请', '_authSalesOrder')"
            v-hasPermi="['sales/salesOrder:ZSQCG']"
            >转非授权采购申请</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="getconfirmFuntion('自定义库存预占', 'zidingyi')"
            v-hasPermi="['sales/salesOrder:ZDYKCYZ']"
            >自定义库存预占</el-button
          >

          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="exportData"
            >导出</el-button
          >
        </template>
      </template>
      <template #lineBtn="row">
        <el-popover
          placement="top"
          width="160"
          trigger="hover"
          v-if="btnProps.deleteLine.isShow"
        >
          <div style="display: flex">
            <el-button
              type="danger"
              size="mini"
              @click="getconfirmFuntion('取消', 'cancel', row)"
              v-hasPermi="['sales/salesOrder:cancelNum']"
              >取消</el-button
            >
            <el-button
              type="danger"
              size="mini"
              @click="getconfirmFuntion('取消数量', 'cancelNum', row)"
              v-hasPermi="['sales/salesOrder:cancelNum']"
              >取消数量</el-button
            >
          </div>
          <el-button
            slot="reference"
            size="mini"
            style="margin-left: 10px;color: #1890ff;"
            v-if="checkCancel(row)"
          >
            更多 >></el-button
          >
        </el-popover>
        <template v-else>
          <el-button
            type="danger"
            size="mini"
            @click="getconfirmFuntion('取消', 'cancel', row)"
            v-hasPermi="['sales/salesOrder:cancelNum']"
            >取消</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="getconfirmFuntion('取消数量', 'cancelNum', row)"
            v-hasPermi="['sales/salesOrder:cancelNum']"
            >取消数量</el-button
          >
        </template>
      </template>
    </LineTable>

    <AddBug
      ref="AddBug"
      :lineList="[]"
      :WERKS="searchInfo.WERKS"
      :detail="salesHeadDetail"
      @handenOk="handenOk('bumaddPurApply')"
    />
    <ComModalSearch
      v-if="['LGORT'].findIndex((prop) => prop === item.key) > -1"
      v-for="(item, index) in componentsModal"
      :key="index"
      :ref="'ComModalSearch' + item.key"
      v-bind="item"
      :searchData="ColumnsObj[item.key]"
      :column="ColumnsObj[item.key]"
      @handenOk="handenOk('yuzhan', $event)"
      :getTableList="getTableListLGORT"
    />
  </div>
</template>
<script>
import LineTable from "@/components/LineTable/index.vue";
import ComSearch from "@/components/ComSearch/index.vue";
import PurDelivery from "../purDelivery/index.vue";
import PurchasePurApply from "../../purchase/purApply/index.vue";
import { addPurApply } from "@/api/purchase/purApply/purApply";
import AddBug from "./addBug.vue";
import { formatNowDate, confirmFuntion } from "@/utils";
import { changeInfoData } from "@/components/LineTable/units";
import {
  columnsSales,
  lookSalesOrderLineCloumn,
  salseNumColumn,
  getSearchData,
  getlineColums,
  getParams,
} from "./config";
import {
  getHead,
  addHead,
  queryInventory,
  queryInventoryyuzhan,
  inventoryLockCancel,
} from "@/api/sales/salesOrder/head";
import { dictOptionCodeList, optionsCodeListKeys } from "../demand/config";
// import { addPurDelivery } from "@/api/sales/purDelivery/purDelivery.js";
import { addSalesDelivery } from "@/api/sales/salesDelivery/salesDelivery";
import ComModalSearch from "@/components/ComModalSearch/index.vue";
import {
  componentsModal,
  getRequestData,
  ColumnsObj,
} from "@/components/ComSearch/config.js";
import { addmixin } from "./add";

import {
  changeKeys,
  btnProps,
  changeauthSalesOrder,
  initSearchInfo,
  lookBtnProps,
  rules,
  getDeliveryParams,
  getYuzhanparams,
} from "./units";

const titleName = {
  "": "新增",
  edit: "修改",
  detail: "",
};

export default {
  name: "SalesOrderDetail",
  components: {
    ComSearch,
    LineTable,
    AddBug,
    ComModalSearch,
    PurDelivery,
    PurchasePurApply,
  },
  dicts: [
    "demand_line_status",
    "abgru",
    "ekgrp",
    "fumigation",
    ...optionsCodeListKeys,
  ],
  dictsKey: dictOptionCodeList.map(({ dictOptionCode }) => dictOptionCode),
  mixins: [addmixin],
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      rules,
      salseNumColumn,
      columns: getSearchData({}),
      salesOrderLineCloumn: getlineColums({}),
      salesOrderLineList: [],
      title: titleName[this.$route.query.type] || "",
      activeName: "sales",
      salesHeadDetail: {
        docStatus: "",
      },
      searchInfo: {
        ERNAM: this.$store.state.user.name,
        ...initSearchInfo,
      },
      btnProps,
      pageType,
      loading: false,
      componentsModal,
      getRequestData,
      ColumnsObj,
      dataProps: {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
          fumigation: "N",
          // ZZMLYLX: "1",
          // BESKZ: "E",
        },
      },
    };
  },
  computed: {
    VKORGOptions() {
      return this.$store.state.dictData.dictDataOptions.VKORG;
    },
  },
  created() {
    // console.log(this.columns,'columns')
    this.getDetail();
  },
  methods: {
    getconfirmFuntion(text, type, row) {
      confirmFuntion(this, text, () => {
        if (type === "cancel") {
          this.handFuntion(row);
        } else {
          this.handenOk(type, row);
        }
      });
    },
    async getTableListLGORT(params) {
      const fun = getRequestData["LGORT"];
      try {
        const res = await fun({
          ...params,
          werks: this.salesHeadDetail.WERKS,
        });
        return res;
      } catch (error) {
        console.log(error, "rrrrrrrrrr");
      }
    },
    closeEdit() {
      this.$refs.lineTable.closeEdit();
    },
    queryInventory(params) {
      return queryInventory(params);
    },
    getDetail() {
      if (!this.$route.params.id || this.$route.params.type === "add") return;
      if (this.loading) return;
      this.loading = true;
      getHead(this.$route.params.id)
        .then((res) => {
          this.loading = false;
          this.$refs.lineTable.initProjict();
          if (res.code === 200) {
            // this.salesHeadDetail = changeKeys(res?.data);
            // console.log(this.salesHeadDetail,'00000')
            this.salesHeadDetail = JSON.parse(
              JSON.stringify(changeKeys(res.data))
            );

            this.columns = getSearchData(res.data);
            this.salesOrderLineCloumn = getlineColums(res.data);

            this.btnProps =
              res.data.docStatus === "S" || res?.data?.demandNo
                ? lookBtnProps
                : this.btnProps;

            if (this.salesHeadDetail?.VBELN && res?.data?.docStatus === "S") {
              this.$nextTick(() => {
                this.$refs.PurDelivery.comitList({
                  vbelnSo: this.salesHeadDetail.VBELN,
                });
              });
            }
            if (this.salesHeadDetail.salesNo && res?.data?.docStatus === "S") {
              this.$nextTick(() => {
                this.$refs.PurchasePurApply.comitList({
                  salesNo: this.salesHeadDetail.salesNo,
                });
              });
            }

            this.searchInfo = this.salesHeadDetail;
            this.salesOrderLineList = res.data.salesOrderLineList.map(
              (item, index) => {
                // 可操作数量
                const num = Number(item.KWMENG_SD);
                return {
                  ...item,
                  salesNo: res?.data?.salesNo,
                  KWMENG_SD: item.KWMENG_SD || 0,
                  KWMENG_SD2: num,
                  id:
                    res.data?.docStatus === "draft"
                      ? `${item.salesLineId}${index}`
                      : undefined,
                };
              }
            );
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
          this.$refs.lineTable.initProjict();
        });
    },
    handleClick(val, event) {
      this.activeName = val.name;
    },

    submitTip(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },

    handSales(searchInfoData = {}, type) {
      const list = this.$refs.lineTable.newlineList || [];
      if (list.length === 0 && type === "submit") {
        this.$modal.msgError("请添加行信息");
        if (this.$refs.lineTable.checkList()) return;
        return;
      }

      if (this.loading) return;

      this.loading = true;
      const delLineIdList = this.$refs.lineTable.delLineIdList || [];

      const detail = {
        ...this.salesHeadDetail,
        ...searchInfoData,
        orderTypes: searchInfoData?.ordertype
          ? [searchInfoData?.ordertype]
          : [],
      };
      const params = getParams(detail, list, delLineIdList, type);

      // console.log(params, "params");

      addHead({
        ...params,
      })
        .then((res) => {
          if (res.code === 200) {
            // this.getDetail();
            this.loading = false;
            if (this.salesHeadDetail.salesHeadId) {
              this.getDetail();
            } else if (res.data.salesHeadId) {
              this.$store
                .dispatch("tagsView/delPage", { name: "SalesOrderDetail" })
                .then(() => {
                  this.$router.replace(
                    `/sales/SalesOrder-detail/index/edit/${res.data?.salesHeadId}`
                  );
                });
            }
            this.$modal.msgSuccess(res?.data?.msg || "操作成功");
          }
        })
        .catch((error) => {
          this.loading = false;
          this.getDetail();
        });
    },

    checkNum(item, editItem) {
      // console.log(item,editItem,'0000')
      if (item.prop == "KWMENG_QX") {
        // 取消数量<= 申请数量-锁定未提货数量-提货未出库数量
        const KWMENG = Number(editItem.KWMENG) || 0;
        const KWMENG_SD = Number(editItem.KWMENG_SD) || 0;
        const KWMENG_TH = Number(editItem.KWMENG_TH) || 0;
        const KWMENG_QX = Number(editItem.KWMENG_QX) || 0;
        const num = KWMENG - KWMENG_SD - KWMENG_TH;
        if (KWMENG_QX > num) {
          editItem.KWMENG_QX = 0;
          this.$message({
            type: "error",
            message: `输入的取消数量,要小于等于${num}`,
          });

          return;
        }
      } // 和操作数量
      if (item.prop === "KWMENG_SD2") {
        if (!editItem[item.prop]) {
          editItem[item.prop] = 1;
        }
      }
    },

    handenOk(type, rowData = {}) {
      this.closeEdit();
      if (type === "cancel") {
        // 取消数量
        if (!rowData?.KWMENG_QX) {
          this.$message({
            type: "error",
            message: `请输入取消数量`,
          });
          this.loading = false;
          return;
        }

        return;
      }
      let list = this.$refs.lineTable.multipleSelection;
      if (list.length === 0) {
        list = this.$refs.lineTable.newlineList || [];
      }

      if (list && list.length === 0) {
        this.$message({
          type: "error",
          message: `请选择数据`,
        });
        return;
      }

      if (this.isEqualData()) {
        const newlineList = this.$refs.lineTable.newlineList || [];
        const searchInfoData = changeInfoData({
          ...this.$refs.ComSearchInfo.dataInfo,
        });
        const detail = {
          ...this.salesHeadDetail,
          ...searchInfoData,
          orderTypes: searchInfoData?.ordertype
            ? [searchInfoData?.ordertype]
            : [],
        };
        const params = getParams(detail, newlineList, [], "add");

        try {
          addHead({
            ...params,
          });
        } catch (error) {
          console.log(error, "error");
        }
      }

      if (type === "bug") {
        // 转采购申请
        this.setCaigouBug(type);
        return;
      } else if (["bumaddPurApply"].includes(type)) {
        this.getDetail();
        return;
      } else if (type === "zidingyi") {
        const ref = this.$refs.ComModalSearchLGORT[0];
        ref.dialogVisible = true;
        ref.$refs.mainrList.submitEmits();
        return;
      }

      if (this.loading) return;
      this.loading = true;
      if (type === "yuzhan") {
        this.setYuzhanData(rowData);
      } else if (type === "shifnag") {
        this.setShifnag();
      } else if (type === "delivery") {
        this.setDelivery();
      } else if (type === "authSalesOrder" || type === "_authSalesOrder") {
        this.setAuthSalesOrder(type);
      }
    },
    exportData() {
      this.download(
        `sales/salesOrder/export/${this.salesHeadDetail.salesHeadId}`,
        {
          // ...this.queryParams
        },
        `销售订单_.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.salesAdd {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
}

.lineBtn {
  display: flex;
  justify-content: space-between;
}
</style>
