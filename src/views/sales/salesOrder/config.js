import { demandColumns, demandSearchData } from "../demand/config";

const _demandColumns = demandColumns.filter(
  ({ prop }) => !["pushStatus", "ANGDT,BNDDT"].includes(prop)
);

const indexSearchData = [
  {
    prop: "demandNo",
    label: "SAS需求单号",
    width: 140,
    attr: {
      disabled: true,
    },
  },
];

const objpurCode = {
  prop: "purCode",
  label: "采购订单编号",
  width: 140,
};

const demandColumns2 = [
  {
    prop: "creditLimit",
    label: "总信用额度",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "availableCreditLimit",
    label: "可用额度",
    width: 140,
    attr: {
      disabled: true,
    },
  },
];

_demandColumns.splice(-10, 0, objpurCode);

export const _demandColumns1 = [..._demandColumns, ...demandColumns2].map(
  (item) => {
    if (item.prop === "demandNo") {
      item.prop = "salesNo";
      item.label = "SAS销售单号";
      // item.attr
    }
    if (item.prop === "VBELN") {
      // item.slotName = undefined;
      item.label = "SAP销售单号";
    }
    if (item.prop === "docStatus") {
      item.optionsCode = "demand_send_status";
      // item.slotName=undefined
      item.attr = {
        disabled: true,
      };
    }

    if (item.prop === "VKORG") {
      item.slotName = "VKORG";
    }
    if (item.prop === "WERKS") {
      item.slotName = "WERKS";
    }
    if (item.searchOut) {
      item.searchOut = false;
    }

    return item;
  }
);

export const columns = [...indexSearchData, ..._demandColumns1].filter(
  ({ prop }) => prop !== "purApplyNo"
);

const newcolumns = JSON.parse(JSON.stringify([...columns]));
const _columnsData = [];
newcolumns.forEach((item) => {
  if (
    ["VBELN", "demandNo", "ordertype", "hMemo", "ERDAT", "docStatus"].includes(
      item.prop
    )
  ) {
    const obj = { ...item };
    if (item.prop === "docStatus") {
      obj.optionsCode = "demand_doc_status";
      obj.type = "select";
      obj.attr = {
        disabled: true,
      };
    }
    if (item.prop === "ordertype") {
      obj.type = "select";
    }
    obj.attr = {};
    if (obj.prop === "hMemo") {
      obj.width = undefined;
    } else {
      obj.width = 200;
    }
    _columnsData.push(obj);
  }
});

export const salaDamend = _columnsData;

export const demandcolumns = JSON.parse(JSON.stringify(columns));

export const optionsCodeListKeys = columns
  .filter(({ optionsCode }) => optionsCode)
  .map(({ optionsCode }) => optionsCode);

export const dictOptionCodeList = columns.filter(
  ({ dictOptionCode }) => dictOptionCode
);

// console.log(optionsCodeListKeys,'optionsCodeListKeys')
export const searchData = JSON.parse(JSON.stringify(columns))
  .map((item) => {
    item.attr = {};
    return item;
  })
  .slice(0, 9);

export const salesOrderLineCloumn = [
  // {
  //   prop: "VBELN",
  //   label: "SAP销售单号",
  //   width: 140,
  // },
  // {
  //   prop: "POSNR",
  //   label: "行号",
  //   width: 140,
  // },
  // {
  //   prop: "MATNR",
  //   label: "物料号",
  //   width: 140,
  // },
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
    append: true,
    backProps: [
      {
        backProp: "ZZMATNR",
        backLable: "ZZMATNR",
        prop: "ZZMATNR",
        propLabels: ["ZZMATNR", "ZZMATNR"],
      },
      {
        backProp: "maktxZh",
        backLable: "MAKTX",
        prop: "MAKTX",
        propLabels: ["MAKTX", "MAKTX"],
      },
      {
        backProp: "MEINS",
        backLable: "MSEHT",
        prop: "MEINS",
        propLabels: ["MEINS", "MEINS"],
      },

      {
        backProp: "ZZMLYLX",
        backLable: "ZZMLYLX",
        prop: "ZZMLYLX",
        propLabels: ["ZZMLYLX", "ZZMLYLX"],
      },

      {
        backProp: "BESKZ",
        backLable: "BESKZ",
        prop: "BESKZ",
        propLabels: ["BESKZ", "BESKZ"],
      },

      {
        backProp: "MVGR2",
        backLable: "MVGR2",
        prop: "MVGR2",
        propLabels: ["MVGR2", "MVGR2"],
      },

      {
        backProp: "goodsLocation",
        backLable: "goodsLocation",
        prop: "storageLocation",
        propLabels: ["storageLocation", "goodsLocation"],
      },
      {
        backProp: "ZZMSQBZ",
        backLable: "ZZMSQBZ",
        prop: "ZZMSQBZ",
        propLabels: ["ZZMSQBZ", "ZZMSQBZ"],
      },
    ],
  },

  {
    prop: "availableQty",
    label: "库存可用量",
    width: 140,
    disabled: true,
  },

  {
    prop: "KWMENG",
    label: "申请数量",
    width: 140,
  },

  {
    prop: "ZZMLYLX",
    label: "来源类型",
    width: 140,
    type: "select",
    optionsCode: "zzmlylx",
    attr: {
      disabled: false,
    },
    // selectotherBind: (item, row) => {
    //   if (row.salesNo) {
    //     return {

    //     };
    //   }
    // },
  },
  {
    prop: "ZZMSQBZ",
    label: "是否授权",
    width: 140,
    optionsCode: "y_n",
    isShowPrebtn:true,
    type: "select",
    // disabled: true,
    attr: {
      disabled: false,
    },
  },
  // {
  //   prop: "BESKZ",
  //   label: "采购类型",
  //   width: 140,
  //   optionsCode: "beskz",
  //   type: "select",
  //   disabled: true,
  //   attr: {
  //     disabled: true,
  //   },
  // },
  {
    prop: "KBETR",
    label: "含税单价",
    width: 140,
  },
  {
    prop: "netpr",
    label: "净价",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  // {
  //   prop: "OrderType",
  //   label: "订单类型",
  //   width: 140,
  // },
  {
    prop: "KWMENG_SD",
    label: "锁定未提货数量",
    width: 140,
    type: "number",
  },
  {
    prop: "KWMENG_TH",
    label: "提货未出库数量",
    width: 140,
    type: "number",
  },

  {
    prop: "KWMENG_SD2",
    label: "可操作数量",
    width: 140,
    type: "number",
    inputChange: (val, oldVal, self) => {
      const row = self.row || {};
      // console.log(row, val,oldVal, "val11");

      if (val * 1 > row?.KWMENG_SD * 1) {
        // row.KWMENG_SD2 = row.KWMENG_SD;
        return row.KWMENG_SD;
      } else if (val * 1 < 0) {
        return 1;
      }
      return val;
    },
    otherBind: (item, row) => {
      // console.log(item,row,'00000')
      const attr = item?.attr || {};
      const obj = {
        ...attr,
        disabled: row.KWMENG_SD === 0 || !row.KWMENG_SD,
      };
      // console.log(obj, "obj");
      return obj;
    },
  },
  {
    prop: "KWMENG_CK",
    label: "已出库数量",
    width: 160,
    type: "number",
  },
  {
    prop: "KWMENG_QX",
    label: "取消数量",
    type: "number",
    fixed: "right",
    width: 120,
    inputChange: (val, oldVal, self) => {
      // 取消数量<= 申请数量-锁定未提货数量-提货未出库数量
      const editItem = self?.row || {};
      // console.log(editItem,'0000')
      const KWMENG = Number(editItem.KWMENG) || 0;
      const KWMENG_SD = Number(editItem.KWMENG_SD) || 0;
      const KWMENG_TH = Number(editItem.KWMENG_TH) || 0;
      const KWMENG_QX = Number(editItem.KWMENG_QX) || 0;
      const num = KWMENG - KWMENG_SD - KWMENG_TH;
      if (val * 1 < 0) {
        return 1;
      } else if (val * 1 > num) {
        return KWMENG_QX;
      }
      return val;
    },
     otherBind: (item, row) => {
    
     const flg = row?.ABGRU !== "12" && row.salesHeadId&&
        Number(row?.KWMENG_SQ || 0) === 0 &&
        Number(row?.KWMENG_TH || 0) === 0 &&
        Number(row?.KWMENG_CK || 0) === 0
      console.log(flg, "obj");
      return {
        disabled:!flg
      };
    },
    // width:140
  },

  {
    prop: "KWMENG_SQ",
    label: "采购申请数量",
    width: 140,
    type: "number",
  },

  {
    prop: "fumigation",
    label: "是否熏蒸",
    width: 140,
    type: "select",
    optionsCode: "fumigation",
  },

  {
    prop: "iMemo",
    label: "行备注",
    // width:140
  },

  {
    prop: "MVGR2",
    label: "仓储组",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "storageLocation",
    label: "货位",
    width: 140,
    attr: {
      disabled: true,
    },
  },

   {
    prop: "ABGRU",
    label: "拒绝原因",
    width: 140,
    attr: {
      disabled: true,
    },
    optionsCode:'abgru',
    fixed: "right",
    // width:140
  },
];

export const lookSalesOrderLineCloumn = salesOrderLineCloumn.map((item) => {
  const obj = {
    ...item,
    isShowPrebtn:false,
    attr: {
      disabled: item.prop === "KWMENG_SD2" ? false : true,
    },
  };

  if (["KWMENG_SD2", "KWMENG_QX", "ZZMLYLX",'ZZMSQBZ'].includes(item.prop)) {
    obj.attr = {
      disabled: false,
    };
  }

  return obj;
});

export const getlineColums = (detail = {}) => {
  const { docStatus = "", demandNo = "" } = detail;

  if (docStatus !== "S" && !demandNo) {
    const propsList = [
      "POSNR",
      "ZZMATNR",
      "KWMENG",
      "ZZMLYLX",
      "ZZMSQBZ",
      "iMemo",
      "fumigation",
      "ABGRU",
    ];
    return JSON.parse(JSON.stringify(salesOrderLineCloumn))
      .filter(({ prop }) => propsList.includes(prop))
      .map((item) => {
        item.attr = {};
        if (item.prop === "POSNR") {
          item.attr = {
            disabled: true,
          };
        }
        if (item.prop === "BESKZ") {
          item.attr = {
            disabled: true,
          };
        }
        // if (item.prop === "ZZMSQBZ") {
        //   item.attr = {
        //     disabled: true,
        //   };
        // }

        // if(item.prop === '')

        return item;
      });
  } else {
    return lookSalesOrderLineCloumn;
  }
};

export const _salesOrderLineCloumn = JSON.parse(
  JSON.stringify(lookSalesOrderLineCloumn)
).map((item) => {
  item.attr = {
    disabled: true,
  };
  return item;
});

const columns2 = JSON.parse(JSON.stringify(columns));

export const columnsSales = columns2.map((item) => {
  if (item.prop === "hMemo") {
    item.searchOut = true;
  }
  return {
    ...item,
    attr: {
      disabled: true,
    },
  };
});

export const getSearchData = (detail) => {
  const { docStatus = "", demandNo } = detail;
  const data = JSON.parse(JSON.stringify(columns2)).map((item) => {
    if (item.prop === "docStatus") {
      item.attr = {
        disabled: true,
      };
    }
    if (docStatus === "S" || demandNo) {
      item.attr = {
        disabled: true,
      };
    }

    if (item.prop === "hMemo") {
      item.attr = {
        disabled: false,
      };
    }
    // if (
    //   ["shipMethod"].includes(item.prop) &&
    //   ['', 'E', 'draft'].includes(docStatus)
    // ) {
    //   item.attr = {
    //     ...item.attr,
    //     disabled: false,
    //   };
    // } else if (["shipMethod"].includes(item.prop)) {
    //   item.attr = {
    //     ...item.attr,
    //     disabled: true,
    //   };
    // }

    // if(["shipMethod"].includes(item.prop))

    return {
      ...item,
    };
  });

  return data;
};

const salseNum = {
  物料号: "MATNR",
  专卖物料号: "ZZMATNR",
  物料描述: "MAKTX",
  工厂: "WERKS",
  库存地点: "LGORT",
  "基本单位	": "MEINS",
  销售订单库存总数: "LABST",
  销售订单号: "VBELN",
  销售订单行项目: "POSNR",
  单个销售订单库存: "LABST1",
  客户编码: "KUNNR",
  客户描述: "NAME",
};

export const salseNumColumn = Object.keys(salseNum).map((key) => {
  return {
    label: key,
    prop: salseNum[key],
  };
});

const bugColoums = {
  // '采购申请编号':	'BANFN',
  // '采购申请行项目':	'BANFN',
  专卖物料号: "ZZMATNR",
  // '采购申请凭证类型':	'BSART',
  // '采购组织':	'EKORG',
  申请的数量: "KWMENG",
  已采购的数量: "KWMENG_SQ",

  采购数量: "KWMENG_SQ2",
  采购组: "EKGRP",
  // '工厂':	'WERKS',

  // '库存地点'	:'LGORT',
  行备注: "iMemo",
  // '供应商'	:'lifnr'
};

const bugColoumsKey = {};
let requiredKeys = [];
export const bugColoumsList = Object.keys(bugColoums).map((key) => {
  let item = {};
  bugColoumsKey[bugColoums[key]] = undefined;
  if (bugColoums[key] !== "iMemo") {
    requiredKeys.push(bugColoums[key]);
  }

  item.type = "input";
  if (!["KWMENG_SQ2", "EKGRP", "iMemo"].includes(bugColoums[key])) {
    item.attr = {
      disabled: true,
    };
  }
  if (["LGORT"].includes(bugColoums[key])) {
    item.type = "select";
  }

  if (bugColoums[key] === "KWMENG_SQ2") {
    item.type = "number";
    item.inputChange = (val, oldVal, self) => {
      const editItem = self.row || {};
      const num1 = editItem.KWMENG ? editItem.KWMENG * 1 : 0;
      const num2 = editItem.KWMENG_SQ ? editItem.KWMENG_SQ * 1 : 0;
      const num = Number(val);
      const newNumber = num1 - num2;
      // console.log(newNumber,num,'0000')
      if (num > newNumber) {
        return newNumber;
      }
      if (`${num}` === "NaN" || num === 0) {
        self.$message({
          type: "error",
          message: "采购数量大于0、小于等于'申请数量-已采购数量'",
        });
        return 1;
      }
      return val;
    };
  }

  if (bugColoums[key] === "EKGRP") {
    item.type = "select";
    item.optionsCode = "ekgrp";
  }

  return {
    label: key,
    prop: bugColoums[key],
    ...item,
    // width:140
  };
});

export const bugColoumsKeyInit = bugColoumsKey;
export const bugrequiredKeys = requiredKeys;

export const deliveryColoums = JSON.parse(
  JSON.stringify(bugColoumsList)
).filter(({ prop }) => prop !== "EKGRP");

export const deliveryrequiredKeys = ["KWMENG", "LGORT"];

const obj2 = {
  prop: "LGORT",
  label: "收货地点",
  width: 200,
  dictOptionCode: "LGORT",
  append: true,
  attr: {},
  backProps: [
    {
      backProp: "lgort",
      backLable: "lgortName",
      prop: "LGORT",
    },
  ],
  setPropParams: (activeProps, dataInfo, params) => {
    return {
      werks: dataInfo?.shouWERKS,
    };
  },
};

bugColoumsList.splice(-2, 0, obj2);
export const addModalObj = {
  delivery: {
    coloums: deliveryColoums,
    requiredKeys: deliveryrequiredKeys,
    initLineData: bugColoumsKeyInit,
    title: "销售交货单",
  },
  bug: {
    coloums: bugColoumsList,
    requiredKeys: ["KWMENG_SQ2", "LGORT", "EKGRP"],
    initLineData: requiredKeys,
    title: "采购申请",
  },
};

export const getParams = (searchData, lineList, delLineIdList, type) => {
  let POSNR = 0;
  const newlineList = [...lineList].map((item) => {
    POSNR = item.POSNR * 1 || (POSNR + 10) * 1;
    item.salesLineId = item.salesHeadId ? item.salesLineId : undefined;
    return {
      POSNR: POSNR,
      ...item,
      BESKZ: item?.BESKZ !== "X" ? item?.BESKZ : "",
    };
  });

  const salesOrderLineList = [...newlineList].filter(
    ({ salesHeadId }) => salesHeadId
  );
  const AddSalesOrderLineList = [...newlineList].filter(
    ({ salesHeadId = "" }) => !salesHeadId
  );

  const params = {
    type: "salesOrder",
    action: type === "add" && searchData.salesHeadId ? "upd" : type,
    ...searchData,
    salesOrderLineList: [...salesOrderLineList, ...delLineIdList],
    addSalesOrderLineList: AddSalesOrderLineList,
  };

  return params;
};

export const mainrSearchData = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    // width:140
  },
];

export const mainrColums = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    // width:140
  },
  {
    prop: "MAKTX",
    label: "物料描述",
    // width:140
  },
  {
    prop: "WERKS",
    label: "工厂",
    // width:140
  },
  {
    prop: "LGORT",
    label: "库存地点",
    // width:140
  },
  {
    prop: "LIFNR",
    label: "供应商",
    // width:140
  },
  {
    prop: "MEINS",
    label: "基本单位",
    // width:140
  },

  {
    prop: "SPEME",
    label: "已冻结库存",
    // width:140
  },
  {
    prop: "LABST",
    label: "非限制库存",
    // width:140
  },

  {
    prop: "EINME",
    label: "限制库存",
    // width:140
  },
  {
    prop: "UMLME",
    label: "在途库存",
    // width:140
  },
  {
    prop: "LABST1",
    label: "销售订单库存",
    // width:140
  },
];

// 库存预占，库存释放，创建提货单，转采购申请，转授权采购申请

export const caigouLineColumn = [
  {
    prop: "werks",
    propName: "werksName",
    label: "工厂",
    dictOptionCode: "VKORG",
    // slotName: "WERKS",
    append: true,
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
      },
    ],
    attr: {},
  },
  {
    prop: "VKORG",
    label: "销售组织",
    propName: "salesOrgName",
    dictOptionCode: "VKORG",
    // slotName:'VKORG',
    append: true,
    attr: {
      // readonly: true,
    },
    // 查询条件
    params: {
      pageSize: 10000,
      pageNum: 1,
    },
    //  返回的字段映射
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },

  {
    prop: "LGORT",
    label: "收货地点",
    width: 200,
    dictOptionCode: "LGORT",
    append: true,
    attr: {},
    backProps: [
      {
        backProp: "lgort",
        backLable: "lgortName",
        prop: "LGORT",
      },
    ],
    setPropParams: (activeProps, dataInfo, params) => {
      // console.log(activeProps,dataInfo,params,'params')
      return {
        werks: dataInfo?.shouWERKS,
      };
    },
  },
  {
    label: "采购组",
    type: "select",
    prop: "EKGRP",
    optionsCode: "ekgrp",
  },
];
