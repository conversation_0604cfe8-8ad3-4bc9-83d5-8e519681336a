<template>
  <div :class="isSelfPage ? 'app-container' : 'app-container app-container2'">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :searchInfo="searchInfo"
      :getTableList="getList"
      :searchShow="isSelfPage"
      :searchProps="{ showNum: 6 }"
      :tableProps="{ lineBtnrightClass: 'lineBtnright2' }"
      :isShowRightBtn="!isSelfPage"
      :keyId="isSelfPage ? 'salesOrder' : 'salesOrder2'"
      ref="salesOrder"
    >
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          v-hasPermi="['sales/salesOrder:head:add']"
          @click="handeFuntion('add')"
          >新增</el-button
        >
      </template>

      <template v-for="item in optionsCodeList" #[item.slotName]="{ row }">
        <dict-tag
          :key="item.prop"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
        />
      </template>

      <template #VBELN="{ row }">
        <a class="href" @click="handeFuntion('look', row)">{{ row.VBELN }}</a>
      </template>

      <template #docStatus="{ row }">
        <dict-tag
          :options="dict.type.demand_send_status"
          :value="row.docStatus"
          :prop="'docStatus'"
          :msg="row.errMsg"
        />
      </template>
      <template v-for="item in dictOptionCodeList" #[item.prop]="{ row }">
        <dict-text
          :key="item.prop"
          :backProps="item.backProps[0]"
          :value="row[item.prop]"
        />
      </template>

      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
            @click="handeFuntion('edit', row)"
            v-hasPermi="['sales/salesOrder:head:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="handeFuntion('look', row)"
            v-hasPermi="['sales/salesOrder:detail']"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComSearch from "@/components/ComSearch/index.vue";
import ComTableList from "@/components/ComTable/list.vue";

import { listHead } from "@/api/sales/salesOrder/head";

import {
  searchData,
  columns,
  optionsCodeListKeys,
  dictOptionCodeList,
  salaDamend,
} from "./config";
import { optionsCodeList } from "../demand/config";
import { returnValue } from "@/utils/index";

export default {
  name: "SalesOrder",
  components: {
    ComSearch,
    ComTableList,
  },
  dicts: optionsCodeListKeys,
  dictsKey: dictOptionCodeList.map(({ dictOptionCode }) => dictOptionCode),
  data() {
    const as = location.href.indexOf("/sales/salesOrder") > -1;
    return {
      columns: as ? columns : salaDamend,
      searchData: as ? searchData : salaDamend,
      searchInfo:{},
      innerVisible: false,
      optionsCodeList,
      dictOptionCodeList,
      dictOptionCodeListKey: dictOptionCodeList.map(
        ({ dictOptionCode }) => dictOptionCode
      ),
      keyId:'salesOrder'
    };
  },
  computed: {
    isSelfPage() {
      return location.href.indexOf("/sales/salesOrder") > -1;
    },
  },
  activated(){
    // console.log(location.href.indexOf("/sales/salesOrder"),'location.href.indexOf("/sales/salesOrder")')
    if(!location.href.indexOf("/sales/salesOrder") > -1){
      this.keyId = 'salesOrder2'
    }else{
      this.keyId = 'salesOrder'
    }
  },
  created() {
    if(!location.href.indexOf("/sales/salesOrder") > -1){
      this.keyId = 'salesOrder2'
    }else{
      this.keyId = 'salesOrder'
    }
  },
  methods: {
    comitList(params) {
      this.searchInfo = params;
      this.$refs.salesOrder.submitEmits(params);
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {
      const res = await listHead(params);
      res.rows = res.rows.map((item) => {
        item.createTime = item?.createTime
          ? item?.createTime.split(" ")[0]
          : "";
        item.PRSDT = item?.PRSDT ? item?.PRSDT.split("T")[0] : "";
        return item;
      });
      return res;
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "sales/demand/demand/export",
        {
          ...this.queryParams,
        },
        `demand_.xlsx`
      );
    },

    handeFuntion(type, data) {
      // if (type === "edit" || type === "look") {
      this.$store
        .dispatch("tagsView/delPage", { name: "SalesOrderDetail" })
        .then(() => {
          this.$router.push(
            `/sales/SalesOrder-detail/index/${type}/${data?.salesHeadId}`
          );
        });

      // }
    },
    returnValue(...options) {
      return returnValue(options);
    },
  },
};
</script>

<style lang="scss">
.app-container2 {
  margin-top: 20px;
  padding-left:0px ;
  padding-right: 0px;
}
.app-container2 {
  .lineBtnright2 {
    top: -42px;
  }
}
</style>
