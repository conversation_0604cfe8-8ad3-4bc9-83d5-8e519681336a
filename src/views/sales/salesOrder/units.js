import { formatNowDate, nextFormatNowDate } from "@/utils";

export const initSearchInfo = {
  AUART: "ZMQT",
  ihrezE: "",
  PRSDT: formatNowDate(),
  ERDAT: formatNowDate(),
  "ANGDT,BNDDT": [formatNowDate(), nextFormatNowDate()],
  WAERK: "CNY",
  BSTDK: formatNowDate(),
  AUDAT: formatNowDate(),
  VKBUR: "ZM00",
  SPART: "B0",
  VTWEG: "B2",
  fumigation: "N",
  ordertype: "ZMOR",
};

export const rules = {
  VTWEG: [{ required: true, message: "请选择分销渠道", trigger: "change" }],
  SPART: [{ required: true, message: "请选择产品组", trigger: "change" }],
  WERKS: [{ required: true, message: "请选择工厂", trigger: "change" }],

  // BSTDK: [{ required: true, message: "请选择客户需求时间", trigger: "blur" }],
  KUNNR: [{ required: true, message: "请选择客户编号", trigger: "change" }],
  KUNWE: [{ required: true, message: "请选择地址编号", trigger: "change" }],

  IHREZ: [{ required: true, message: "请输入客户联系人", trigger: "change" }],
  TELF1: [
    { required: true, message: "请输入客户联系人电话", trigger: "change" },
  ],
  harvestAddress: [
    { required: true, message: "请输入收货地址", trigger: "change" },
  ],
  VKORG: [{ required: true, message: "请输入销售组", trigger: "change" }],
  // AUDAT: [{ required: true, message: "请选择单据日期", trigger: "change" }],
  VKBUR: [{ required: true, message: "请选择销售办公室", trigger: "change" }],
  WAERK: [{ required: true, message: "请选择凭证货币", trigger: "change" }],
  shipMethod: [
    { required: true, message: "请选择发运方式", trigger: "change" },
  ],
  // PLTYP:[{ required: true, message: "请选择价格清单", trigger: "change" }],
};

export const demandKeys = ["province,city,region", "ANGDT,BNDDT"];
export const changeKeys = (data) => {
  const obj = { ...data };
  demandKeys.forEach((item) => {
    const itemKey = item.split(",");
    const arr = [];
    itemKey.forEach((k) => {
      const value = data[k] || "";
      if (value) {
        arr.push(value);
      }
    });
    obj[item] = arr;
  });

  return obj;
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["sales/salesOrder:head:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
  uploadExcel: {
    isShow: true,
    hasPermi: ["sales/salesOrder:line:UploadExcel"],
  },
};

export const lookBtnProps = {
  add: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:add"],
  },
  batchDelete: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
  deleteLine: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
};

export const bugbtnProps = {
  add: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
};

export const bugbtnProps2 = {
  add: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:add"],
  },
  batchDelete: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
  deleteLine: {
    isShow: false,
    hasPermi: ["sales/salesOrder:head:remove"],
  },
};

export const changeauthSalesOrder = (detail, salesOrderLineList, name) => {
  const list = [...salesOrderLineList];
  const paramsList = list.map((item, index) => {
    const num = item.KWMENG ? item.KWMENG * 1 : 0;
    const num2 = item.KWMENG_SQ ? item.KWMENG_SQ * 1 : 0;
    return {
      ...item,
      salesNo: item.salesNo,
      VBELN_SO: item.salesNo,
      ZZMATNR: item.ZZMATNR,
      VBELN: item.VBELN,
      NFPO: `${index * 10 + 10}`,
      BNFPO: `${index * 10 + 10}`,
      salesPosnr: item.POSNR,
      lifnr: "",
      EKGRP: "",
      KWMENG: num - num2,
      delFlag: "0",
      iMemo: item.iMemo,
      salesLineId: item.salesLineId,
      LPEIN: formatNowDate(),
    };
  });

  const params = {
    type: "authSalesOrder",
    action: "submit",
    sapNo: detail.VBELN,

    // purchaseApplyInfos: paramsList,
    purchaseApplyInfos: {
      ...detail,
      salesOrderLineList:[],
      BSART: "NB",
      EKORG: detail.VKORG,
      WERKS: detail.WERKS,
      AFNAM: name,
      BSTYP: "B",
      delFlag: "0",
      VKORG: detail.VKORG,
      demandNo:undefined,
      purApplyLineList: paramsList,
      purApplyLineInfoList: paramsList,
    },
  };

  return params;
};

// 交货单
export const getDeliveryParams = (list, salesHeadDetail) => {
  const newList = [...list].filter(
    ({ KWMENG_SD, KWMENG_SD2 }) =>
      !(!KWMENG_SD || Number(KWMENG_SD) === 0) &&
      !(!KWMENG_SD2 || Number(KWMENG_SD2) === 0)
  );
  const params = {
    ...salesHeadDetail,
    VBELN: salesHeadDetail.VBELN,
    vbelnSo: salesHeadDetail.VBELN,
    salesNo: salesHeadDetail.salesNo,
    // VBELN_DR: "",
    delFlag: 0,
    VSTEL: salesHeadDetail.VKORG,
    LFDAT: formatNowDate(),
    hMemo: salesHeadDetail.hMemo,
    salesOrderLineList:[]
  };

  let MVGR2TYpe = new Set();
  const paramsList = newList.map((item, index) => {
    MVGR2TYpe.add(item.MVGR2);
    return {
      ...item,
      ZZMATNR: item.ZZMATNR,
      BNFPO: item.POSNR,
      WERKS: salesHeadDetail.WERKS,
      vbelnSo: salesHeadDetail.VBELN,
      salesHeadId: item.salesHeadId,
      salesLineId: item.salesLineId,
      POSNR: item.POSNR,
      KWMENG: item.KWMENG_SD,
      iMemo: item.iMemo,
      VBELN: salesHeadDetail.VBELN,
    };
  });


  let _MVGR2TYpe = [...MVGR2TYpe];

  // console.log(_MVGR2TYpe,paramsList,'_MVGR2TYpe')
  let query = [{
        ...params,
        salesDeliveryLineList: paramsList,
      }
  ];
  // if (paramsList && paramsList.length) {
  //   if (_MVGR2TYpe && _MVGR2TYpe.length) {
  //     query = [...MVGR2TYpe].map((key) => {
  //       return {
  //         ...params,
  //         MVGR2: key,
  //         salesDeliveryLineList: paramsList.filter(
  //           ({ MVGR2 = "" }) => MVGR2 === key
  //         ),
  //       };
  //     });
  //   } else {
  //     query = {
  //       ...params,
  //       MVGR2: "",
  //       salesDeliveryLineList: paramsList,
  //     };
  //   }
  // }

  return query;
};
// 预占
export const getYuzhanparams = (yuzhanList, salesHeadDetail, rowData) => {
  const params = {
    IS_HEADER: {
      BLDAT: formatNowDate(),
      BUDAT: formatNowDate(),
      MTSNR: "",
      BKTXT: salesHeadDetail.hMemo,
      // BWARTWA: "343",
      PSONR: "",
      SOBKZ: "",
      VBELN: salesHeadDetail.VBELN,
      LGORT: rowData?.lgort || undefined,
      ITEMS: yuzhanList.map((item, index) => {
        // let KWMENG = item?.KWMENG * 1 || 0;
        // let KWMENG_TH = item?.KWMENG_TH * 1 || 0;
        // let KWMENG_CK = item?.KWMENG_CK * 1 || 0;
        let yuNum = item.KWMENG; // KWMENG - KWMENG_TH - KWMENG_CK;

        return {
          ...item,
          POSNR: item.POSNR,
          KDPOS: item.VBELN,
          WERKS: salesHeadDetail.WERKS,
          VBELN: salesHeadDetail.VBELN,
          KWMENG: yuNum,
          KDAUF: item.VBELN,
          LGORT: rowData?.lgort || undefined,
        };
      }),
    },
  };

  return params;
};
