import {
  changeKeys,
  btnProps,
  changeauthSalesOrder,
  initSearchInfo,
  lookBtnProps,
  rules,
  getDeliveryParams,
  getYuzhanparams,
} from "./units";
import {
  getHead,
  addHead,
  queryInventory,
  queryInventoryyuzhan,
  inventoryLockCancel,
} from "@/api/sales/salesOrder/head";
import { getParams } from "./config";
import { addSalesDelivery } from "@/api/sales/salesDelivery/salesDelivery";
import { formatNowDate } from "@/utils";
import { addPurApply } from "@/api/purchase/purApply/purApply";
import { changeInfoData } from "@/components/LineTable/units";
import _ from "lodash";
export const addmixin = {
  data() {
    return {};
  },
  methods: {
    setlineList() {
      let list = this.$refs.lineTable.multipleSelection;
      if (list.length === 0) {
        list = this.$refs.lineTable.newlineList || [];
      }

      list = list.filter((item) => {
        let flg = this.checkCancel(item);

        return flg;
      });

      return list;
    },
    // 预占
    setYuzhanData(rowData) {
      try {
        let list = this.setlineList();
        let yuzhanList = list.filter(
          ({ ZZMSQBZ = "", ZZMLYLX = "" }) =>
            !(ZZMSQBZ === "Y" && ZZMLYLX === "外部")
        );

        if (yuzhanList && yuzhanList.length === 0) {
          this.$message({
            type: "error",
            message: `暂无预占数据`,
          });
          this.loading = false;
          return;
        }

        const params = getYuzhanparams(
          yuzhanList,
          this.salesHeadDetail,
          rowData
        );

        queryInventoryyuzhan(params)
          .then((res) => {
            this.loading = false;
            if (res.code === 200) {
              this.getDetail();
              this.$modal.msgSuccess(res?.data?.msg || "操作成功");
            }
          })
          .catch((error) => {
            this.loading = false;
            this.getDetail();
          });
      } catch (error) {
        this.loading = false;
        console.log(error, "error");
      }
    },
    // 转采购申请

    setCaigouBug(type) {
      let list = this.setlineList();
      if (list.length === 0) {
        this.$message({
          type: "error",
          message: `暂无数据转采购申请`,
        });
        this.loading = false;
        return;
      }

      this.$refs.AddBug.salesOrderLineList = [];
      let caigouList = list;
      const bugList = [];
      caigouList.forEach((item, index) => {
        const num = item.KWMENG ? item.KWMENG * 1 : 0;
        const num2 = item.KWMENG_SQ ? item.KWMENG_SQ * 1 : 0;
        const obj = {
          ...item,
          ZZMATNR: item.ZZMATNR,
          KWMENG: num,
          KWMENG_SQ: num2,
          id: `${index}`,
          KWMENG_SQ2: num - num2,
        };
        if (num - num2 > 0) {
          bugList.push(obj);
        }
      });

      if (bugList.length === 0) {
        this.$message({
          type: "error",
          message: `所选行采购申请数量已等于申请数量，不能再转采购申请`,
        });
        return;
      }

      this.$refs.AddBug.dialogVisible = true;
      const dataInfo = this.$refs.ComSearchInfo.changeInfoData();
      this.$nextTick(() => {
        this.$refs.AddBug.visibleFuntions({
          salesOrderLineList: bugList,
          VKORGOptions: [...this.VKORGOptions].filter(
            ({ werks }) => werks !== this.salesHeadDetail.WERKS
          ),
          type,
          WERKS: this.salesHeadDetail.WERKS,
          detail: {
            ...this.salesHeadDetail,
            ...dataInfo,
          },
        });
      });
    },
    // 释放
    async setShifnag() {
      try {
        let params = {};
        let list = this.setlineList();
        const suodingList = [...list].filter(
          ({ KWMENG_SD2 }) => !(!KWMENG_SD2 || Number(KWMENG_SD2) === 0)
        );

        if (suodingList.length === 0) {
          this.$message({
            type: "error",
            message: `暂无释放数据`,
          });
          this.loading = false;
          return;
        }

        params = {
          IS_HEADER: {
            BLDAT: formatNowDate().replaceAll("-", ""),
            BUDAT: formatNowDate().replaceAll("-", ""),
            MTSNR: "070701",
            BKTXT: this.salesHeadDetail.hMemo,
            BWARTWA: "414", //冻结344
            SOBKZ: "",
            VBELN: this.salesHeadDetail.VBELN,
            WERKS: this.salesHeadDetail.WERKS,
            ITEMS: suodingList.map((item, index) => {
              return {
                // ...item,
                // LGORT: "YL10",
                MATNR: "",
                ZZMATNR: item.ZZMATNR,
                CHARG: "",
                LGORT1: "",
                KDPOS: "",
                SGTXT: item.iMemo,
                WEMPF: "",
                POSNR: item.POSNR,
                KDPOS: item.POSNR,
                WERKS: this.salesHeadDetail.WERKS,
                VBELN: this.salesHeadDetail.VBELN,
                KWMENG: item.KWMENG_SD2,
                KDAUF: item.VBELN || this.salesHeadDetail.VBELN,
                UMWRK: this.salesHeadDetail.WERKS,
              };
            }),
          },
        };
        const res = await inventoryLockCancel(params);
        this.loading = false;
        if (res.code === 200) {
          this.$modal.msgSuccess("操作成功");
        }
        this.getDetail();
      } catch (error) {
        console.log(error, "error");
        this.loading = false;
        this.getDetail();
      }
    },
    // 转授权，非转授权采购
    async setAuthSalesOrder(type) {
      try {
        //  1内部，2外部，E 自制生产/F 外部采购
        //销售订单转授权采购申请逻辑变更：来源类型=‘外部’ + 是否授权 = ‘Y’，只有这部分可以转授权采购申请（勾选全部物料或者不勾选时，要筛选授权的物料出转授权采购申请，不需要报哪些不是授权的）
        // 转授权采购申请
        let list = this.setlineList();

        let caigouList1 = list.filter(
          ({ ZZMSQBZ = "", ZZMLYLX = "" }) =>
            ZZMSQBZ === "Y" && ZZMLYLX === "外部"
        );

        let caigouList2 = list.filter(
          ({ ZZMSQBZ = "", ZZMLYLX = "" }) =>
            !(ZZMSQBZ === "Y" && ZZMLYLX === "外部")
        );

        let caigouList = caigouList1;

        // 非授权
        if (type === "_authSalesOrder") {
          caigouList = caigouList2;
        }
        if (caigouList && caigouList.length === 0) {
          this.$message({
            type: "error",
            message: `无授权明细，无法转授权采购申请`,
          });
          this.loading = false;
          return;
        }

        caigouList = caigouList.filter((item) => {
          const num = item.KWMENG ? item.KWMENG * 1 : 0;
          const num2 = item.KWMENG_SQ ? item.KWMENG_SQ * 1 : 0;
          return num - num2 > 0;
        });

        if (caigouList.length === 0) {
          this.$message({
            type: "error",
            message: `所选行采购申请数量已等于申请数量，不能再转授权采购申请`,
          });
          this.loading = false;
          return;
        }

        const params = changeauthSalesOrder(
          this.salesHeadDetail,
          caigouList,
          this.$store.state.user.name
        );
        const res = await addPurApply(params);
        this.loading = false;
        if (res.code === 200) {
          this.$modal.msgSuccess(res?.msg || res?.data?.msg || "操作成功");
        }
        this.getDetail();
      } catch (error) {
        console.log(error, "error");
        this.loading = false;
      }
    },
    // 交货单
    setDelivery() {
      let list = this.setlineList();


      if (!list || list.length === 0) {
        this.loading = false;
        this.$message({
          type: "error",
          message: `暂无数据创建交货单`,
        });
        return;
      }

      // // 交货单
      let paramsList = getDeliveryParams(list, this.salesHeadDetail);


      if (!paramsList[0]?.salesDeliveryLineList || paramsList && paramsList[0]?.salesDeliveryLineList?.length === 0) {
        this.loading = false;
        this.$message({
          type: "error",
          message: `暂无数据创建交货单`,
        });
        return;
      }


      let getFun = paramsList.map((item) => {
        return addSalesDelivery(item);
      });

      Promise.all(getFun)
        .then((res) => {
          this.$modal.msgSuccess("操作成功");
          this.loading = false;
          this.getDetail();
        })
        .catch((error) => {
          this.loading = false;
          this.getDetail();
          console.log(error);
        });
    },
    // 列出所有的键，接着遍历数组
    ifCompare(object1, object2) {
      var o1keys = Object.keys(object1);
      var o2keys = Object.keys(object2);
      // if (o2keys.length !== o1keys.length) return false;
      for (let i = 0; i <= o1keys.length - 1; i++) {
        let key = o1keys[i];
        // if (!o2keys.includes(key)) return false;
        // console.log(object2[key],object1[key])
        if (object2[key] !== object1[key]) return false;
      }
      return true;
    },
    isEqualData() {
      try {
        const data = changeInfoData({
          ...this.$refs.ComSearchInfo.dataInfo,
          salesOrderLineList: undefined,
        });
        const data2 = changeInfoData({
          ...this.salesHeadDetail,
          salesOrderLineList: undefined,
        });

        const line1 = [
          ...JSON.parse(JSON.stringify(this.$refs.lineTable.newlineList)),
        ];
        const line2 = [
          ...JSON.parse(
            JSON.stringify(this.salesHeadDetail.salesOrderLineList)
          ),
        ];
        if (data?.hMemo !== data2.hMemo) {
          return true;
        } else {
          line1.forEach((item, index) => {
            if (item?.ZZMLYLX !== line2[index]?.ZZMLYLX) {
              return true;
            }
          });
        }
        return false;
      } catch (error) {
        return false;
        console.log(error, "error");
      }
    },
    checkCancel(row) {
      let flg =
        this.salesHeadDetail.docStatus &&
        row?.ABGRU !== "12" &&
        row.salesHeadId &&
        Number(row?.KWMENG_SQ || 0) === 0 &&
        Number(row?.KWMENG_TH || 0) === 0 &&
        Number(row?.KWMENG_CK || 0) === 0;

      return flg;
    },
    // 取消
    async handFuntion(data) {
      const alist = this.setlineList();
      let ids = alist
        .filter(({ salesHeadId }) => salesHeadId)
        .map(({ salesLineId }) => salesLineId);

      if (data?.salesLineId) {
        ids = [data.salesLineId];
      }

      if (!ids || ids.length === 0) {
        this.$message({
          type: "error",
          message: `暂无取消的数据`,
        });

        return;
      }
      if (this.loading) return;
      this.loading = true;
      let newlineList = this.$refs.lineTable.newlineList || [];

      newlineList = newlineList.map((item) => {
        item.ABGRU = ids.includes(item.salesLineId) ? "12" : undefined;
        return item;
      });
      const searchInfoData = changeInfoData({
        ...this.$refs.ComSearchInfo.dataInfo,
      });
      const detail = {
        ...this.salesHeadDetail,
        ...searchInfoData,
        orderTypes: searchInfoData?.ordertype
          ? [searchInfoData?.ordertype]
          : [],
      };
      const params = getParams(detail, newlineList, [], "add");

      try {
        await addHead({
          ...params,
        });
        this.loading = false;
        this.getDetail();
      } catch (error) {
        this.loading = false;
        this.getDetail();

        console.log(error, "error");
      }
    },
  },
};
