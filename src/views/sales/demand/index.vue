<template>
  <div class="app-container">
    <ComTableList
      :searchData="columnsSearch"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 3 }"
      :tableProps="tableProps"
      :searchInfo="searchInfo"
      ref="demand"
    >
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          v-hasPermi="['sales/demand:demand:add']"
          @click="gotoAdd"
          >新增</el-button
        >
      </template>
      <template #default>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="待确认" name="INIT"></el-tab-pane>
          <el-tab-pane label="已确认" name="CONF"></el-tab-pane>
          <el-tab-pane label="已完成" name="TROD"></el-tab-pane>
          <el-tab-pane label="全部" name="All"></el-tab-pane>
        </el-tabs>
      </template>

      <template #VBELN="{ row }">
        <a class="href" @click="handeFuntion('look', row)">{{ row.VBELN }}</a>
      </template>

      <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
          :prop="item.prop"
          :msg="row.errMsg"
        />
      </template>
      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="primary"
            @click="handeFuntion('edit', row)"
            v-hasPermi="['sales/demand:demand:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="primary"
            @click="handeFuntion('look', row)"
            v-hasPermi="['sales/demand:detail']"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComSearch from "@/components/ComSearch/index.vue";
// import ComTable from "@/components/ComTable/index.vue";
import ComTableList from "@/components/ComTable/list.vue";
import AddSales from "./add.vue";

import { listDemand, delDemand } from "@/api/sales/demand/demand";

import {
  searchData,
  columns,
  optionsCodeListKeys,
  optionsCodeList,
  columnsSearch,
  dictOptionCodeList
} from "./config";

export default {
  name: "Demand",
  dicts: optionsCodeListKeys.filter((key)=>(key !== 'meins')),
  dictsKey:["VKORG", "KUNNR"],
  components: {
    ComSearch,
    ComTableList,
    AddSales,
  },
  data() {
    return {
      searchInfo:{
        docStatus:'INIT',
      },
      columnsSearch,
      optionsCodeList,
      activeName: "INIT",
      columns,
      searchData: searchData,
      innerVisible: false,
      dictOptionCodeList,
      fixHeader:'',
      tableProps: {
        // height: 600,
      },
    };
  },
  computed:{
    dictDataOptions() {
      return this.$store.state.dictData.dictDataOptions;
    },
  },
  created() {
    // console.log(optionsCodeList,'optionsCodeList')
  },
  mounted(){
   
  },
  methods: {
    gotoAdd() {
      const demandHeadId = "null";
      this.$store
        .dispatch("tagsView/delPage", { name: "DemandDetail" })
        .then(() => {
          this.$router.push(`/sales/demand-detail/index/add/${demandHeadId}`);
        });
    },
    /** 查询销售订单头信息列表 */
    async getList(params) {
      if(this.activeName === 'TROD'){
        params.docStatusList = ['TROD','QTFC','ZCRM']
      }

      if(this.activeName === 'All'){
        params.docStatusList = []
      }

      if(`${Number(params.KUNNR)}` === 'NaN' ){
        params.NAME1 = params.KUNNR
        params.KUNNR = undefined
      }
     
      const res = await listDemand(params);
      res.rows = res.rows.map((item) => {
        item.createTime = item?.createTime
          ? item?.createTime.split(" ")[0]
          : "";
        item.PRSDT = item?.PRSDT ? item?.PRSDT.split("T")[0] : "";
       
        return item;
      });
      return res;
     
    },

    handleClick(val, event) {
      this.activeName = val.name;
      const docStatus = !['All'].includes(val.name) ? val.name : ''
      const data ={
        ...this.$refs.demand.$refs.searchComTableInfo.dataInfo,
         docStatus:docStatus
      }
      this.searchInfo = {
        ...data 
        
      }
      this.$refs.demand.submitEmits({
        ...data
      });
    },
    handeFuntion(type, data) {
      if (type === "edit"|| type === "look") {
        this.$store.dispatch("tagsView/delPage", { name: "DemandDetail" }).then(()=>{
          this.$router.push(
            `/sales/demand-detail/index/${type}/${data.demandHeadId}`
          );
        });
      
      } else if (type === "del") {
        const demandHeadIds = data.demandHeadId;
        this.$modal
          .confirm(
            '是否确认删除需求单头信息编号为"' + demandHeadIds + '"的数据项？'
          )
          .then(function () {
            return delDemand(demandHeadIds);
          })
          .then(() => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.$refs.demand.getList();
          })
          .catch(() => {});
      } else if (type === "cancel") {
        const demandHeadIds = data.demandHeadId;
        this.$modal
          .confirm(
            '是否确认取消需求单头信息编号为"' + demandHeadIds + '"的数据项？'
          )
          .then(function () {
            return delDemand(demandHeadIds);
          })
          .then(() => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.$refs.demand.getList();
          })
          .catch(() => {});
      }
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
