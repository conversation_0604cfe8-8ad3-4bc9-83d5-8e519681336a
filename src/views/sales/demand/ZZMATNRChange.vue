<template>
  <div class="comTableChange">
    <ComTable
      :column="tableColumn"
      :typeIndex="false"
      :tableData="tableData"
      :tableProps="tableProps"
      @current-change="currentChange"
    >
      <template #aSelect="scope">
        <el-checkbox
          v-if="scope.treeNode && scope.treeNode.level !== 0"
          :value="selectIdlist.includes(scope.row[tableProps.rowKey])"
          @change="clickRadio(scope.row, $event)"
        ></el-checkbox>
      </template>
      <template #iMemo="{ row }">
        <el-input
          v-if="editId === row[tableProps.rowKey]"
          size="mini"
          clearable
          v-model="editItem.iMemo"
          placeholder="请输入"
        >
        </el-input>
        <span v-else>{{ row.iMemo }}</span>
      </template>
    </ComTable>
  </div>
</template>

<script>
import ComTable from "@/components/ComTable/index.vue";
import { ablist } from "./config";
export default {
  name: "ZZMATNRChange",
  components: {
    ComTable,
  },
  props: {
    isSelection: {
      type: Boolean,
      default() {
        return false;
      },
    },
    column: {
      type: Array,
      default() {
        return [];
      },
    },
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      radio: "",
      tableColumn: [
        {
          prop: "aSelect",
          label: "选择",
          slotName: "aSelect",
        },
        ...this.column,
      ],
      list: ablist,
      selectObj: {},
      tableProps: {
        treeProps: { children: "children", hasChildren: "hasChildren" },
        rowKey: "id",
        defaultExpandAll: true,
        height: 600,
        highlightCurrentRow: true,
      },
      selectIds: {},
      selectIdlist: [],
      editId: "",
      editItem: {},
    };
  },
  watch: {
    tableData(data) {
      this.list = data;
    },
    imo(val) {
      // console.log(val, "val11111111111");
      const data = this.editItem;
      if (data.children && data.children.length && this.editItem[this.tableProps.rowKey] === this.editId) {
        data.children = data.children.map((item) => {
          item.parant.iMemo = val;
          return item;
        });
      }
      this.editItem = data;
    },
  },
  computed: {
    imo() {
      return this.editItem.iMemo;
    },
  },
  mounted() {},
  created() {},
  methods: {
    clickRadio(row, event) {
      // console.log(row, event, "event");
      const id = row.id;
      const { id: pid = "" } = row.parant;
      const arrList = Object.values(this.selectIds);
      const aindex = arrList.findIndex(({ parant = {} }) => parant.id === pid);
      if (aindex > -1 && event) {
        let oldId = arrList[aindex].id;
        delete this.selectIds[oldId];
        this.selectIds[id] = row;
      } else if (event) {
        this.selectIds[id] = row;
      } else if (!event) {
        delete this.selectIds[id];
      }
      this.selectIdlist = Object.keys(this.selectIds).map((a) => a);
      // console.log(this.selectIdlist, "selectIdlist");
    },
    currentChange(data) {
      this.editId = data[this.tableProps.rowKey];
      this.editItem = data;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-pagination {
  display: flex;
  justify-content: flex-end;
  margin: 10px;
}

::v-deep .el-radio__label,
::v-deep .el-table__placeholder {
  display: none;
}
</style>
