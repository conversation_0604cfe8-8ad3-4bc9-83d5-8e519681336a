import { formatNowDate, nextFormatNowDate } from "@/utils";

export const initSearchInfo = {
  AUART: "ZMQT",
  ihrezE: "SAS",
  PRSDT: formatNowDate(),
  ERDAT: formatNowDate(),
  "ANGDT,BNDDT": [formatNowDate(), nextFormatNowDate()],
  ANGDT:formatNowDate(),
  BNDDT:nextFormatNowDate(),
  WAERK:'CNY',
  BSTDK:formatNowDate(),
  AUDAT:formatNowDate(),
  VKBUR:'ZM00',
  SPART:'B0',
  VTWEG:'B2',
};

export const getaddSearch = (type, data, backProps) => {
   let obj = {}
  backProps.forEach((item) => {
    const {prop, backProp, backLable,propLabels = []} = item;
    const key = `${prop},${backLable}`;
    // 单个
    if(propLabels && propLabels.length){
    const [ keyProp,label] = propLabels
       obj = {
        ...obj,
        [keyProp]:data[label]
      }
    }else{

      obj = {
      ...obj,
      [key]: [data[backProp],data[backLable]],
      [prop]: data[backLable],
    };

    }
  });
  return obj
};


export const demandKeys = ["province,city,region", "ANGDT,BNDDT"];
export const changeKeys = (data) => {
  const obj = { ...data };
  demandKeys.forEach((item) => {
    const itemKey = item.split(",");
    const arr = [];
    itemKey.forEach((k) => {
      const value = data[k] || "";
      if (value) {
        arr.push(value);
      }
    });
    obj[item] = arr;
  });

  return obj;
};

export const demandHandRules = {
  VTWEG: [{ required: true, message: "请选择分销渠道", trigger: "change" }],
  SPART: [{ required: true, message: "请选择产品组", trigger: "change" }],
  WERKS: [{ required: true, message: "请选择工厂", trigger: "change" }],

  // BSTDK: [{ required: true, message: "请选择客户需求时间", trigger: "blur" }],
  KUNNR: [{ required: true, message: "请选择客户编号", trigger: "change" }],
  KUNWE: [{ required: true, message: "请选择地址编号", trigger: "blur" }],

  IHREZ: [{ required: true, message: "请输入客户联系人", trigger: "blur" }],
  TELF1: [{ required: true, message: "请输入客户联系人电话", trigger: "blur" }],
  harvestAddress: [
    { required: true, message: "请输入收货地址", trigger: "blur" },
  ],
  VKORG: [{ required: true, message: "请输入销售组", trigger: "change" }],
  // AUDAT: [{ required: true, message: "请选择单据日期", trigger: "change" }],
  VKBUR: [{ required: true, message: "请选择销售办公室", trigger: "change" }],
  WAERK: [{ required: true, message: "请选择凭证货币", trigger: "change" }],
  shipMethod:[{ required: true, message: "请选择发运方式", trigger: "change" }],
  // PLTYP:[{ required: true, message: "请选择价格清单", trigger: "change" }],

  
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["sales/demand/line:add"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["sales/demand/line:del"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["sales/demand/line:del"],
  },
  uploadExcel:{
    isShow: true,
    hasPermi: ["sales/demand/line:push"],
  }
};
const _btnProps = {};
Object.keys(btnProps).forEach((key) => {
  _btnProps[key] = {
    ...btnProps[key],
    isShow: false,
  };
});

export const lookBtnProps = _btnProps;

export const optionsCodeList = [
  "auart",
  "vtweg",
  "spart",
  "demand_doc_status",
  "demand_send_status",
];

export const lookLineData = (list,detail) => {
  if (!Array.isArray(list)) return [];

  let _list = list.map((item,index) => {
    return {
      ...item,
      id:detail?.pushStatus === 'draft' || !item?.lineStatus ? `${index}` + `10` : undefined,
      disabled: ['TROD','PTOD'].includes(item?.lineStatus) || item.ABGRU,
    };
  });

  // _list = _list.sort((a, b) => b.POSNR - a.POSNR);

  return _list;
};

export const changeLineData = (data) => {
  const newdata = Object.keys(data).map((key) => {
    const name = data[key][0].startMatName;
    const list = data[key];
    const obj = {
      ZZMATNR: key,
      ZZMATNRName: name,
      id: key,
    };
    return {
      ...obj,
      children: list.map((item) => {
        return {
          parant: {
            ...obj,
          },
          id: item.endMatDrawNum,
          ZZMATNR: item.endMatDrawNum,
          ZZMATNRName: item.endMatName,
        };
      }),
    };
  });
  return newdata;
};

// 匹配物料内容
export const changePipeiList = (salesOrderLineList, data) => {
  // console.log(data,'data111111')
  const list = [...salesOrderLineList];
  // 查询旧的数据
  const oldData = [];
  // 新的数据
  const selectIdsList = Object.values(data).map((item) => {
    const obj = {
      ...item,
    };
    oldData.push({
      ...item.parant,
    });
    delete obj.parant;
    return obj;
  });

  const newSelect = [];
  const _list = list.map((item) => {
    const oldindex = oldData.findIndex(
      ({ ZZMATNR }) => ZZMATNR === item.ZZMATNR
    );
    // console.log(oldData,'oldData')
    if (oldindex > -1) {
      const obj = selectIdsList[oldindex];
      newSelect.push({
        ...item,
        ...obj,
        POSNR:undefined,
        demandLineId:undefined,
        iMemo: obj?.iMemo || item.iMemo,
      });
    }

    if (oldindex > -1) {
      item.ABGRU = 12;
      item.iMemo = oldData[oldindex].iMemo;
    }

    return item;
  });
  const conList = [..._list, ...newSelect];
  // console.log(conList,newSelect, "conList");

  return conList;
};

export const initParams = {
  pushStatus: "draft",
  ERNAM: "admin",
  AUART: "ZMQT",
  ihrezE: "SAS",
  PRSDT: "2025-06-18",
  ERDAT: "2025-06-18",
  VTWEG: "B2",
  SPART: "B0",
  VKBUR: "ZM10",
  WERKS: "6001",
  AUDAT: "2025-06-18",
  KUNNR: "0000600001",
  BSTDK: "2025-06-18",
  KUNWE: "0000600001",
  IHREZ: "1",
  TELF1: "2",
  VKORG: "6001",
  harvestAddress: "2",
  ZZSFFK: "0",
  zzcustomerL2: "0",
  KONDA: "A5",
  shipMethod: "01",
  ABRVW: "101",
  VDATU: "2025-06-18",
  WAERK: "CNY",
  recDocNo: "3",
  hMemo: "3",
  ANGDT: "2025-06-18",
  BNDDT: "2026-06-18",
  province: "guangxi",
  city: "南宁",
  region: "yizhi",
  action: "add",
  demandLineList: [
    {
      delFlag: "0",
      KWMENG: 1,
      KBETR: 0,
      KBETR1: 0,
      ZZMATNR: "620000003-TN",
      POSNR: "10",
    },
    {
      delFlag: "0",
      KWMENG: 1,
      KBETR: 0,
      KBETR1: 0,
      ZZMATNR: "620000002-ZM",
      POSNR: "20",
    },
  ],
  delFlag: "0",
};
