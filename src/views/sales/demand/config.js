import { getaddSearch } from "@/components/ComSearch/utils";
export const searchData = [
  {
    label: "公司",
    type: "input",
    prop: "bukrs",
  },
  {
    label: "工厂",
    type: "input",
    prop: "WERKS",
  },
  {
    label: "需求单号",
    type: "input",
    prop: "demandNo",
  },
  {
    label: "SAP需求单号",
    type: "input",
    prop: "VBELN",
  },
  {
    label: "来源单号",
    type: "input",
    prop: "BSTKD",
  },
  {
    label: "需求单来源",
    type: "input",
    prop: "ihrezE",
  },

  {
    label: "需求单状态",
    type: "input",
    prop: "docStatus",
    type: "select",
    optionsCode: "demand_doc_status",
  },
  {
    label: "销售人员",
    type: "input",
    prop: "salesPerson",
  },
  {
    label: "紧急类型",
    type: "select",
    prop: "zzcustomerL2",
    optionsCode: "sys_normal_disable",
  },

  {
    label: "客户名称",
    type: "input",
    prop: "KUNNR",
  },
  {
    label: "物料编码",
    type: "input",
    prop: "",
  },
  {
    label: "创建日期",
    type: "input",
    prop: "ERDAT",
  },
];

export const demandSearchData = searchData;

const options = [
  {
    value: "guangxi",
    label: "广西",
    children: [
      {
        value: "南宁",
        label: "南宁",
        children: [
          {
            value: "yizhi",
            label: "青秀区",
          },
          {
            value: "fankui",
            label: "江南区",
          },
        ],
      },
      {
        value: "玉林",
        label: "玉林",
        children: [
          {
            value: "玉州区",
            label: "玉州区",
          },
          {
            value: "dingbudaohang",
            label: "北流市",
          },
        ],
      },
    ],
  },
  {
    value: "zujian",
    label: "广东",
    children: [
      {
        value: "basic",
        label: "广州",
        children: [
          {
            value: "layout",
            label: "天河",
          },
          {
            value: "color",
            label: "番禺",
          },
        ],
      },
      {
        value: "form",
        label: "深圳",
        children: [
          {
            value: "radio",
            label: "龙岗",
          },
          {
            value: "checkbox",
            label: "福田",
          },
        ],
      },
    ],
  },
];

const KONDAOptions = {
  A0: "专卖-电商客户",
  A1: "专卖-服务站",
  A2: "专卖-服务站关联单位",
  A3: "专卖-公交公司",
  A4: "专卖-供应商",
  A5: "专卖-海外发动机经销商",
  A6: "专卖-海外服务站",
  A7: "专卖-海外终端大客户",
  A8: "专卖-海外主机厂客户",
  A9: "专卖-海外零星客户",
  AA: "专卖-海外配件经销商",
  AB: "专卖-经销商",
  AC: "专卖-零售客户",
  AD: "专卖-批发客户",
  AE: "专卖-普通经销商",
  AF: "专卖-普通经销商-专项产品",
  AG: "专卖-旗舰店",
  AH: "专卖-汽车厂",
  AI: "专卖-授权经销点",
  AJ: "专卖-玉柴集团内部客户",
  AK: "专卖-玉柴专卖内部客户",
  AL: "专卖-专卖店",
};

const KONDAOptionsList = Object.keys(KONDAOptions).map((k) => {
  return {
    label: KONDAOptions[k],
    value: k,
  };
});

const shipMethodOnj = {
  "01": "汽车",
  "02": "邮件",
  "03": "火车",
  "04": "海",
  "05": "自提",
  "06": "卡车",
  "07": "空运",
  "08": "铁路",
  "09": "顺丰",
};

const shipMethoOptions = Object.keys(shipMethodOnj).map((k) => {
  return {
    label: shipMethodOnj[k],
    value: k,
  };
});

export const WERKSOPtions = [
  {
    value: "6001",
    label: "玉柴专卖总部",
  },
  {
    value: "6010",
    label: "包头专卖工厂",
  },
  {
    value: "6020",
    label: "北京京都工厂",
  },
  {
    value: "6030",
    label: "广州通聚工厂",
  },
  {
    value: "6040",
    label: "贵阳专卖工厂",
  },
];

export const columns = [
  {
    prop: "VBELN",
    label: "SAP需求单号",
    width: 140,
    slotName: "VBELN",
    attr: {
      disabled: true,
    },
  },
  {
    prop: "demandNo",
    label: "SAS需求单号",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "purApplyNo",
    label: "SAS采购申请号",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "VTWEG",
    label: "分销渠道",
    type: "select",
    optionsCode: "vtweg",
    slotName: "vtweg",
  },

  {
    prop: "SPART",
    label: "产品组",
    type: "select",
    optionsCode: "spart",
    slotName: "spart",
  },

  {
    prop: "VKORG",
    label: "销售组织",
    propName: "salesOrgName",
    dictOptionCode: "VKORG",
    // slotName:'VKORG',
    append: true,
    attr: {
      // readonly: true,
    },
    // 查询条件
    params: {
      pageSize: 10000,
      pageNum: 1,
    },
    //  返回的字段映射
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },

  {
    prop: "WERKS",
    propName: "werksName",
    label: "工厂",
    dictOptionCode: "VKORG",
    // slotName: "WERKS",
    append: true,
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
      },
    ],
    attr: {
      // disabled: true,
    },
  },

  {
    prop: "VKBUR",
    label: "销售办公室",
    width: 140,
    type: "select",
    optionsCode: "vkbur",
    slotName: "VKBUR",
  },

  {
    prop: "KUNNR",
    label: "客户名称",
    append: true,
    dictOptionCode: "KUNNR",
    attr: {
      // readonly: true,
    },
    setBackParams: (prop, data, self) => {
      let datainfo = self.dataInfo;
      let type = self.$route?.params?.type;
      if (type === "add") {
        let params = getaddSearch(prop, data, self.activeProps.backProps);
        return params;
      } else {
        // console.log(datainfo,data,'lllll')
        return {
          KUNNR: data?.NAME1,
          "KUNNR,NAME1": [data?.KUNNR, data?.NAME1],
          KUNWE: datainfo?.KUNWE || data?.KUNNR,
          IHREZ: datainfo?.IHREZ || data?.NAME1,
          TELF1: datainfo?.TELF1 || data?.telNumber,
          harvestAddress: datainfo?.harvestAddress || data?.STREET,
          shipMethod: datainfo?.shipMethod || data?.KVGR2,
          PLTYP: datainfo?.PLTYP || data?.PLTYP,
          ZTERM: datainfo?.ZTERM || data?.ZTERM,
        };
      }
    },
    backProps: [
      {
        backProp: "KUNNR",
        backLable: "NAME1",
        prop: "KUNNR",
      },

      {
        backProp: "KUNNR",
        backLable: "KUNNR",
        prop: "KUNWE",
        propLabels: ["KUNWE", "KUNNR"],
      },
      {
        backProp: "NAME1",
        backLable: "NAME1",
        prop: "IHREZ",
        propLabels: ["IHREZ", "NAME1"],
      },

      {
        backProp: "telNumber",
        backLable: "telNumber",
        prop: "TELF1",
        propLabels: ["TELF1", "telNumber"],
      },
      {
        backProp: "STREET",
        backLable: "STREET",
        prop: "harvestAddress",
        propLabels: ["harvestAddress", "STREET"],
      },
      {
        backProp: "KVGR2",
        backLable: "KVGR2",
        prop: "shipMethod",
        propLabels: ["shipMethod", "KVGR2"],
      },
      {
        prop: "PLTYP",
        propLabels: ["PLTYP", "PLTYP"],
      },

      {
        prop: "ZTERM",
        propLabels: ["ZTERM", "ZTERM"],
      },
    ],
    otherBind: (item, row, self) => {
      if (!row.VKORG) {
        return {
          disabled: true,
        };
      } else {
        return {
          disabled: false,
        };
      }
    },
    setPropParams: (activeProps, dataInfo, params) => {
      // console.log(params,'params')
      return {
        ...params,
        VKORG: dataInfo["VKORG,salesOrgName"][0],
      };
    },
  },
  {
    prop: "KUNWE",
    label: "客户编号",
    // type: "select",
    // options: [
    //   {
    //     value: "0000600001",
    //     label: "0000600001",
    //   },
    //   {
    //     value: "0000601318",
    //     label: "0000601318",
    //   },
    // ],
  },
  {
    prop: "IHREZ",
    label: "客户联系人",
    width: 140,
  },

  {
    prop: "PLTYP",
    label: "价格清单",
    optionsCode: "pltyp",
    type: "select",
    slotName: "PLTYP",
  },
  {
    label: "付款方式",
    type: "select",
    prop: "ZTERM",
    optionsCode: "zterm",
    slotName: "ZTERM",
  },

  {
    prop: "TELF1",
    label: "客户联系人电话",
    width: 140,
  },
  {
    prop: "harvestAddress",
    label: "收货地址",
  },
  {
    prop: "AUDAT",
    label: "单据日期",
    type: "date",
  },

  {
    prop: "BSTDK",
    label: "客户需求时间",
    width: 140,
    type: "date",
  },

  {
    prop: "WAERK",
    label: "凭证货币",
    type: "select",
    optionsCode: "waerk",
    // options: [
    //   {
    //     value: "CNY",
    //     label: "CNY",
    //   },
    //   {
    //     value: "USD",
    //     label: "USD",
    //   },

    //   {
    //     value: "EUR",
    //     label: "EUR",
    //   },
    // ],
  },

  {
    prop: "AUART",
    label: "报价单类型",
    width: 100,
    type: "select",
    optionsCode: "auart",
    slotName: "auart",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "ZZSFFK",
    label: "是否急件",
    type: "select",
    optionsCode: "zzsffk",
    slotName: "zzsffk",
    // options: [
    //   {
    //     value: "0",
    //     label: "是",
    //   },
    //   {
    //     value: "1",
    //     label: "否",
    //   },
    // ],
  },

  {
    prop: "PRSDT",
    label: "定价日期",
    type: "date",
  },
  {
    prop: "ANGDT,BNDDT",
    label: "需求有效日期",
    width: 140,
    type: "daterange",
  },

  {
    prop: "zzcustomerL2",
    label: "紧急类型",
    type: "select",
    optionsCode: "zzcustomer_l2",
    slotName: "zzcustomerL2",
  },

  {
    prop: "AUGRU",
    label: "申请原因",
  },
  {
    prop: "VDATU",
    label: "要求到货时间",
    type: "date",
  },
  {
    prop: "ABRVW",
    label: "使用用途",
    type: "select",
    optionsCode: "abrvw",
    slotName: "ABRVW",
  },

  {
    prop: "KONDA",
    label: "价格组",
    type: "select",
    options: KONDAOptionsList,
    slotName: "KONDA",
  },

  {
    prop: "ihrezE",
    label: "需求单来源系统",
    width: 140,
    type: "select",
    slotName: "ihrezE",
    attr: {
      disabled: true,
    },
    optionsCode: "ihreze",
    // options: [
    //   {
    //     value: "CRM",
    //     label: "CRM",
    //   },
    //   {
    //     value: "SAS",
    //     label: "SAS",
    //   },
    // ],
  },

  {
    prop: "shipMethod",
    label: "发运方式",
    type: "select",
    // options: shipMethoOptions,
    optionsCode: "ship_method",
    slotName: "shipMethod",
    // backProps: [
    //   {
    //     backProp: "value",
    //     backLable: "label",
    //     prop: "shipMethod",
    //   },
    // ],
  },

  {
    prop: "BSTKD",
    label: "来源编号",
    width: 120,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "salesPerson",
    label: "销售人员",
    width: 120,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "VKGRP",
    label: "销售组",
    type: "select",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "recDocNo",
    label: "整改文件号",
    width: 140,
  },

  {
    prop: "ordertype",
    label: "销售订单类型",
    optionsCode: "ordertype",
    slotName: "ordertype",
    type: "select",
    width: 140,
  },

  {
    prop: "province,city,region",
    label: "省市县",
    type: "cascader",
    options,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "hMemo",
    label: "抬头备注",
    searchOut: true,
  },

  {
    prop: "docStatus",
    label: "单据状态",
    // hiddenSearch:true,
    type: "select",
    slotName: "docStatus",
    optionsCode: "demand_doc_status",
    width: 140,
    fixed: "right",
    attr: {
      disabled: true,
    },
  },
  {
    prop: "ERDAT",
    label: "创建日期",
    type: "date",
    attr: {
      disabled: true,
    },
  },
  {
    prop: "ERNAM",
    label: "创建人",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "pushStatus",
    label: "推送状态",
    // hiddenSearch: true,
    type: "select",
    width: 140,
    slotName: "pushStatus",
    optionsCode: "demand_send_status",
    fixed: "right",
    attr: {
      disabled: true,
    },
  },
  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
].map((item) => {
  item.width = item?.width ? item.width : 140;
  return item;
});

export const columnsKeysArr = [];
export const columnsKeys = columns
  .filter(({ prop }) => prop !== "caozuo")
  .map(({ prop }) => prop)
  .forEach((key) => {
    if (key.indexOf(",") > -1) {
      key.split(",").forEach((key1) => {
        columnsKeysArr.push(key1);
      });
    } else {
      columnsKeysArr.push(key);
    }
  });

const _columns = JSON.parse(JSON.stringify(columns));

export const getDemandHeader = (demandDetail, type) => {
  let { pushStatus = "", docStatus = "" } = demandDetail;

  const columnsList = JSON.parse(JSON.stringify(columns)).map((item) => {
    if (
      ["S", "commit"].includes(pushStatus) ||
      ["CONF", "TROD"].includes(docStatus)
    ) {
      item.attr = {
        ...item.attr,
        disabled: true,
      };
    }
    if (
      type !== "look" &&
      (["INIT"].includes(docStatus) || ["draft"].includes(pushStatus)) &&
      ["harvestAddress", "KUNWE", "IHREZ", "TELF1", "VKBUR"].includes(item.prop)
    ) {
      item.attr = {
        ...item.attr,
        disabled: false,
      };
    } else if (
      ["harvestAddress", "KUNWE", "IHREZ", "TELF1", "VKBUR"].includes(item.prop)
    ) {
      item.attr = {
        ...item.attr,
        disabled: true,
      };
    }

    if (
      ["shipMethod", "hMemo", "PLTYP"].includes(item.prop) &&
      ["INIT", ""].includes(docStatus)
    ) {
      item.attr = {
        ...item.attr,
        disabled: false,
      };
    } else if (["shipMethod", "hMemo", "PLTYP"].includes(item.prop)) {
      item.attr = {
        ...item.attr,
        disabled: true,
      };
    }

    return item;
  });

  return columnsList;
};

export const addHeaderSearchData = JSON.parse(JSON.stringify(columns)).map(
  (item) => {
    // if(item.prop === 'WERKS'){
    //   item.backProps = undefined
    // }

    return item;
  }
);

export const lookcolumns = _columns.map((item) => {
  return {
    ...item,
    attr: {
      ...item.attr,
      disabled: true,
    },
  };
});

export const columnsSearch = _columns
  .map((item) => {
    if (item.searchOut) {
      item.searchOut = false;
    }
    if (item.prop !== "WERKS") {
      item.attr = {};
    }

    return {
      ...item,
    };
  })
  .slice(0, 9);

export const demandColumns = JSON.parse(JSON.stringify(columns));

// 需要接口请求的数据
export const dictOptionCodeList = _columns.filter(
  ({ dictOptionCode }) => dictOptionCode
);

// 映射code
export const optionsCodeList = _columns.filter(
  ({ optionsCode }) => optionsCode
);

const ListKeys = [];
optionsCodeList.forEach((item) => {
  ListKeys.push(item.optionsCode);
});

export const optionsCodeListKeys = ListKeys;

export const optionsCodeListSelect = _columns.filter(({ options }) => options);

const ListselectKeys = [];
const ListselectObj = {};

optionsCodeListSelect.forEach((item) => {
  ListselectKeys.push(item.prop);
  ListselectObj[item.prop] = item.options;
});

export const optionsCodeListSelectKeys = ListselectKeys;
export const optionsCodeListSelectKeysData = ListselectObj;

// 字段映射

export const changeDataList = (data) => {
  const obj = { ...data };
  Object.keys(data).forEach((key) => {
    if (optionsCodeListSelectKeys.includes(key)) {
      const value = data[key];
      const options = optionsCodeListSelectKeysData[key];
      const index = options.findIndex(({ value: v }) => v === value);
      const label = index > -1 ? options[index].label : value;

      obj[key] = label;
    }
  });

  // console.log(obj,'index')
  return obj;
};

const keys = [];
export const getToUpperCase = (item) => {
  keys.forEach((ites) => {
    const upperStr = ites.toUpperCase();
    item[upperStr] = item[ites];
  });
  return item;
};

export const salesOrderLineCloumn = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: 300,
    append: true,
    attr: {
      // readonly: true,
    },
    otherBind: (item, row) => {
      return {
        disabled: row.lineStatus ? true : false,
      };
    },
    backProps: [
      {
        backProp: "ZZMATNR",
        backLable: "ZZMATNR",
        prop: "ZZMATNR",
        propLabels: ["ZZMATNR", "ZZMATNR"],
      },
      {
        backProp: "maktxZh",
        backLable: "MAKTX",
        prop: "MAKTX",
        propLabels: ["MAKTX", "MAKTX"],
      },
      {
        backProp: "MEINS",
        backLable: "MSEHT",
        prop: "MEINS",
        propLabels: ["MEINS", "MEINS"],
      },

      {
        backProp: "ZZMLYLX",
        backLable: "ZZMLYLX",
        prop: "ZZMLYLX",
        propLabels: ["ZZMLYLX", "ZZMLYLX"],
      },

      {
        backProp: "BESKZ",
        backLable: "BESKZ",
        prop: "BESKZ",
        propLabels: ["BESKZ", "BESKZ"],
      },

      {
        backProp: "MVGR2",
        backLable: "MVGR2",
        prop: "MVGR2",
        propLabels: ["MVGR2", "MVGR2"],
      },

      {
        backProp: "goodsLocation",
        backLable: "goodsLocation",
        prop: "storageLocation",
        propLabels: ["storageLocation", "goodsLocation"],
      },
      {
        backProp: "ZZMSQBZ",
        backLable: "ZZMSQBZ",
        prop: "ZZMSQBZ",
        propLabels: ["ZZMSQBZ", "ZZMSQBZ"],
      },
    ],
  },

  {
    prop: "KWMENG",
    label: "申请数量",
    width: 140,
    type: "number",
  },

  {
    prop: "ZZMLYLX",
    label: "来源类型",
    width: 140,
    type: "select",
    optionsCode: "zzmlylx",
    selectotherBind: (item, row) => {
      if (["PTOD", "TROD"].includes(row.lineStatus)) {
        return {
          disabled: true,
        };
      }
    },
  },
  {
    prop: "ZZMSQBZ",
    label: "是否授权",
    width: 200,
    optionsCode: "y_n",
    isShowPrebtn:true,
    type: "select",
    attr: {
      disabled: true,
    },
  },

  // {
  //   prop: "BESKZ",
  //   label: "采购类型",
  //   width: 140,
  //   optionsCode: "beskz",
  //   type: "select",
  //   attr: {
  //     disabled: true,
  //   },
  // },

  {
    prop: "KBETR",
    label: "含税单价",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "netpr",
    label: "净价",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  // {
  //   prop: "KBETR1",
  //   label: "不含税单价",
  //   width: 140,
  //   attr: {
  //     disabled: true,
  //   },
  // },
  {
    prop: "bstkdE",
    label: "发动机编号",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "iMemo",
    label: "行备注",
    width: 200,
  },
  {
    prop: "ordertype",
    label: "订单类型",
    optionsCode: "ordertype",
    slotName: "ordertype",
    type: "select",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "ABGRU",
    label: "拒绝原因",
    width: 140,
    attr: {
      disabled: true,
    },
    slotName: "ABGRU",
    fixed: "right",
    // width:140
  },
  {
    prop: "MVGR2",
    label: "仓储组",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "storageLocation",
    label: "货位",
    width: 140,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "lineStatus",
    label: "行状态",
    fixed: "right",
    slotName: "lineStatus",
    // width:140
    attr: {
      disabled: true,
    },
  },
];

export const rderLineCloumnKeys = salesOrderLineCloumn.map(({ prop }) => prop);

export const getExcelId = (excelData) => {
  const data = JSON.parse(JSON.stringify(excelData));

  const obj = {};
  salesOrderLineCloumn.forEach((item) => {
    obj[item.label] = item.prop;
  });
  const _data = data.map((item) => {
    const obj2 = {};
    Object.keys(item).forEach((key) => {
      const prop = obj[key];
      obj2[prop] = item[key];
    });
    return obj2;
  });
  return _data;
};

export const tihuanLineCloumn = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    // width: 200,
  },

  {
    prop: "ZZMATNRName",
    label: "物料名称",
    // width: 140,
  },
  {
    prop: "iMemo",
    label: "行备注",
    slotName: "iMemo",
    // width: 200,
  },
];

export const lookLineCloumn = salesOrderLineCloumn.map((item) => {
  return {
    ...item,
    isShowPrebtn:false,
    attr: {
      ...item.attr,
      disabled: true,
      
    },
  };
});

export const ordersOptions = [
  {
    value: "ZMCD",
    label: "专卖船电订单",
  },
  {
    value: "ZMCR",
    label: "专卖贷项凭单请求",
  },
  {
    value: "ZMCT",
    label: "专卖整改件订单",
  },
  {
    value: "ZMDL",
    label: "专卖动力订单",
  },

  {
    value: "ZMDR",
    label: "专卖借项凭单请求",
  },
  {
    value: "ZMFM",
    label: "专卖农机订单",
  },
  {
    value: "ZMGC",
    label: "专卖绿色销售订单",
  },
  {
    value: "ZMNP",
    label: "专卖新品投放订单",
  },

  {
    value: "ZMOL",
    label: "专卖油品订单",
  },
  {
    value: "ZMOR",
    label: "专卖常规订单",
  },
  {
    value: "ZMQT",
    label: "专卖报价单(需求单)",
  },
  {
    value: "ZMRE",
    label: "专卖销售退货订单",
  },

  {
    value: "ZMRP",
    label: "专卖再制造件订单",
  },
  {
    value: "ZMSV",
    label: "专卖服务订单",
  },
  {
    value: "ZMUS",
    label: "专卖滞销件订单",
  },
  {
    value: "ZMVD",
    label: "专卖销售供应商订单",
  },
];

const obj = {};
export const salesOrderLineCloumnData = salesOrderLineCloumn.map((item) => {
  obj[item.prop] = item.prop;
});

export const salesOrder2 = salesOrderLineCloumnData;

export const getMetadata = () => {
  const list = list;
  salesOrderLineCloumn.forEach((item, index) => {
    let time = new Date();
    const id = `${time.getTime()}${index}`;
    obj.id = id;
    obj[item.prop] = item.prop; // ;
  });

  list.push(obj);
  list[0].children = list;
  return [...list, ...list];
};
