<template>
  <div class="salesAdd" v-loading="loading">
    <!-- <BT /> -->

    <div @click="closeEdit">
      <ComSearch
        :searchData="columns"
        :searchInfo="searchInfo"
        :showNum="9"
        labelWidth="140px"
        :showBtn="false"
        :cancelShow="
          !['TROD', 'QTFC', 'ZCRM'].includes(demandDetail.docStatus) &&
          pageType === 'edit'
        "
        :handFuntion="handleCancel"
        :rules="demandHandRules"
        :addSearch="addSearch"
        :checksearchMsg="checksearchMsg"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        @submitEmits="handDemand"
        @handenOk="searchHandenOk"
      >
        <template #searchButton v-if="pageType !== 'look'">
          <el-button
            v-if="
              !['commit'].includes(demandDetail.pushStatus) &&
              !['TROD', 'QTFC', 'ZCRM'].includes(demandDetail.docStatus)
            "
            v-hasPermi="['sales/demand:save', 'sales/demand:demand:edit']"
            type="primary"
            size="mini"
            :disabled="loading"
            @click="submitTip('save')"
          >
            {{ demandDetail.demandHeadId ? "修改" : "保存" }}
          </el-button>
          <el-button
            v-if="
              !['S', 'commit'].includes(demandDetail.pushStatus) &&
              !demandDetail.docStatus
            "
            v-hasPermi="['sales/demand:commit']"
            :disabled="loading"
            type="primary"
            size="mini"
            @click="submitTip('commit')"
            >提交</el-button
          >
          <template
            v-if="
              ['S', 'E'].includes(demandDetail.pushStatus) &&
              (demandDetail.docStatus === 'INIT' ||
                (demandDetail.VBELN &&
                  salesOrderLineList.filter(
                    ({ lineStatus }) => lineStatus === 'INIT'
                  ).length))
            "
          >
            <el-button
              v-hasPermi="['sales/demand:confirm']"
              :disabled="
                loading ||
                salesOrderLineList.filter(
                  ({ lineStatus }) => lineStatus === 'INIT'
                ).length === 0
              "
              type="primary"
              size="mini"
              @click="submitTip('confirm')"
              >确认</el-button
            >
          </template>
        </template>
      </ComSearch>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="需求明细" name="damend"> </el-tab-pane>
      <el-tab-pane label="销售订单" name="sales"> </el-tab-pane>
      <!-- <el-tab-pane label="申请明细" name="pur"></el-tab-pane> -->
    </el-tabs>
    <SalesOrder ref="salesTable" v-show="activeName === 'sales'" />
    <LineTable
      v-show="activeName === 'damend'"
      :btnProps="btnProps"
      :dataProps="dataProps"
      :isShowRightBtn="true"
      :showHeaderConfig="true"
      :isShowCancel="false"
      :lineList="salesOrderLineList"
      :lineCloumn="salesOrderLineCloumn"
      :requiredKeys="['KWMENG', 'ZZMATNR']"
      :selectable="(row) => selectable(row)"
      @changeLineList="(val) => (salesOrderLineList = val)"
      @refreshList="getDetail"
      ref="demandLineTable"
    >
      <template #lineTableHeader v-if="pageType !== 'look'">
        <el-button
          v-if="
          !['TROD', 'QTFC', 'ZCRM'].includes(demandDetail.docStatus) &&
          pageType === 'edit'
        "
          v-hasPermi="['sales/demand:cancel']"
          type="danger"
          size="mini"
          @click="handFuntion('cancel')"
          >批量取消</el-button
        >
        <el-button
          v-if="['INIT', 'CONF'].includes(demandDetail.docStatus)"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-hasPermi="['sales/demand/line:pipei']"
          @click="handFuntion('pipei')"
          >批量匹配</el-button
        >
        <el-button
          v-if="['CONF'].includes(demandDetail.docStatus)"
          v-hasPermi="['sales/demand/line:changeSales']"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="submitTip('tosales')"
          :disabled="
            salesOrderLineList.filter(({ lineStatus }) => lineStatus === 'PTOD')
              .length === 0
          "
          >需求单转销售订单</el-button
        >
        <el-button
          v-if="![''].includes(demandDetail.docStatus)"
          v-hasPermi="['sales/demand/line:export']"
          @click="handleExport"
          type="primary"
          icon="el-icon-download"
          size="mini"
          >导出</el-button
        >
      </template>

      <template #lineStatus="row">
        <dict-tag
          :options="dict.type.demand_line_status"
          :value="row.lineStatus"
        />
      </template>
      <template #ABGRU="row">
        <dict-tag :options="dict.type.abgru" :value="row.ABGRU" />
      </template>
      <template #lineBtn="row">
        <el-button
          v-if="!['TROD'].includes(row.lineStatus) &&
            !row.ABGRU &&
            row.demandHeadId
          "
          v-hasPermi="['sales/demand:cancel']"
          type="danger"
          size="mini"
          @click.stop="handFuntion('cancel', row)"
          >取消</el-button
        >
        <el-button
          v-if="['INIT'].includes(row.lineStatus) && !row.ABGRU"
          v-hasPermi="['sales/demand:confirm']"
          type="primary"
          size="mini"
          @click.stop="oneSetConf(row)"
          >确认</el-button
        >
      </template>
    </LineTable>

    <el-dialog
      title="选择订单类型"
      :visible.sync="orderVisible"
      width="40%"
      @close="orderVisible = false"
    >
      <div class="orderType">
        <span style="width: 120px">首选订单类型</span>
        <el-select
          v-model="oriderValue"
          placeholder="请选择首选订单类型"
          style="width: 100%"
        >
          <el-option
            v-for="item in orderTypeList"
            :key="item.value"
            :label="`${item.label}(${item.value})`"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="orderType">
        <span style="width: 120px">次选订单类型</span>
        <el-select
          v-model="oriderValue2"
          placeholder="请选择次选订单类型"
          style="width: 100%"
        >
          <el-option
            v-for="item in orderTypeList"
            :key="item.value"
            :label="`${item.label}(${item.value})`"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="handenOk">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="批量替换物料"
      width="80%"
      :top="'0px'"
      class="ZZMATNRName"
      :visible="changeVisible"
      @close="changeVisible = false"
    >
      <ZZMATNRChange
        :column="tihuanLineCloumn"
        :tableData="pipeiList"
        ref="ZZMATNRChange"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handFuntion('pipeiOK')"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import SalesOrder from "../salesOrder/index.vue";
import ComSearch from "@/components/ComSearch/index.vue";

import {
  columns,
  salesOrderLineCloumn,
  ordersOptions,
  lookLineCloumn,
  getToUpperCase,
  tihuanLineCloumn,
  addHeaderSearchData,
  optionsCodeListKeys,
  getDemandHeader,
  columnsKeysArr,
  rderLineCloumnKeys,
} from "./config";
import ComModalSearch from "@/components/ComModalSearch/index.vue";
import ZZMATNRChange from "./ZZMATNRChange.vue";
import LineTable from "@/components/LineTable/index.vue";
import { checkObj, checkArr } from "@/utils/validate.js";

import {
  getDemand,
  addDemand,
  updateDemand,
  updateDemandStatus,
  queryPartsInfo,
} from "@/api/sales/demand/demand";
import { addHead } from "@/api/sales/salesOrder/head";
import {
  initSearchInfo,
  changeKeys,
  demandHandRules,
  btnProps,
  initParams,
  lookBtnProps,
  lookLineData,
  changeLineData,
  changePipeiList,
} from "./units";
import { getaddSearch } from "@/components/ComSearch/utils";

const titleName = {
  add: "新增",
  edit: "修改",
  look: "",
};

export default {
  name: "DemandDetail",
  dicts: [
    "demand_line_status",
    "abgru",
    "zzmlylx",
    "beskz",
    "ordertype",
    ...optionsCodeListKeys,
  ].filter((key) => key !== "meins"),
  dictsKey: ["VKORG", "KUNNR"],
  components: {
    ComSearch,
    ZZMATNRChange,
    ComModalSearch,
    LineTable,
    SalesOrder,
  },
  props: {},
  computed: {
    btnProps() {
      let btns = btnProps;
      if (
        ["commit"].includes(this.demandDetail.pushStatus) ||
        ["TROD", "QTFC", "ZCRM",'CONF'].includes(this.demandDetail.docStatus)
      ) {
        this.salesOrderLineCloumn = lookLineCloumn;
        btns = lookBtnProps;
      }

      return btns;
    },
    orderTypeList() {
      return this.$store.state.dictData.dictDataOptions.ordertype;
    },
  },
  data() {
    const pageType = this.$route?.params?.type || "";
    // console.log(initSearchInfo,'initSearchInfo')
    return {
      tosalesSearchInfoData: {},
      addHeaderSearchData,
      tihuanLineCloumn,
      initParams,
      loading: false,
      demandHandRules,
      pageType,
      changeVisible: false,
      oriderValue: "",
      oriderValue2: "",
      columns: columns,
      salesOrderLineCloumn: salesOrderLineCloumn,
      salesOrderLineList: [],
      title: titleName[pageType] || "",
      activeName: "damend",
      demandDetail: {
        pushStatus: "",
        docStatus: "",
      },
      ordersOptions,
      orderVisible: false,
      searchInfo: {
        ERNAM: this.$store.state.user.name,
        ...initSearchInfo,
      },
      newLineData: [],
      pipeiList: [],
      dataProps: {
        initLineData: {
          delFlag: "0",
          KWMENG: 1,
          ZZMATNR: undefined,
          // ZZMLYLX: "1",
          // BESKZ: "E",
        },
      },
    };
  },
  activated() {
    this.title = titleName[this.$route.params.type] || "";
  },
  created() {
    this.pageType = this.$route?.params?.type || "";
    this.clickType = this.$route?.query?.clickType;

    this.getDetail();
  },
  watch: {},
  methods: {
    addSearch(type, data, backProps) {
      // console.log(data,'dadada')
      if (type === "KUNNR") {
        return {
          KUNNR: data.KUNNR,
          KUNWE: data.KUNNR,
          IHREZ: data.NAME1,
          TELF1: data.telNumber,
          harvestAddress: data.STREET,
          shipMethod: data?.KVGR2,
        };
      } else {
        const obj = getaddSearch(type, data, backProps);
        return obj;
      }
    },
    selectable(row) {
      return !row.ABGRU && row.lineStatus !== "TROD";
    },
    getDetail(clickType = "") {
      if (this.$route.params.demandHeadId === "null" || this.loading) return;
      this.loading = true;
      getDemand(this.$route.params.demandHeadId)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.demandDetail = changeKeys(
              getToUpperCase(JSON.parse(JSON.stringify(res?.data)))
            );
            this.salesOrderLineList = lookLineData(
              res?.data?.demandLineList,
              this.demandDetail
            );
            this.columns = getDemandHeader(
              this.demandDetail,
              this.$route.params.type
            );

            if (this.demandDetail.VBELN) {
              this.$refs.salesTable.comitList({
                // VBELN: this.demandDetail.VBELN,
                demandNo: this.demandDetail.demandNo,
              });
            }
            this.searchInfo = {
              ...this.demandDetail,
              salesOrgName: "",
              werksName: "",
            };
            this.oriderValue = this.demandDetail.ordertype;

            let type = this.clickType || clickType;
            // console.log(clickType,'clickType')

            // if (['CONF','commit'].includes(type)) {
            //   this.clickType = ''
            //   setTimeout(() => {
            //     // console.log(res?.data,'res?.data')
            //     this.resubmit(res?.data);
            //   }, 1000);
            // }
          }

          this.$refs.demandLineTable.initProjict();
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
          this.demandDetail = {};
          this.salesOrderLineList = [];
          this.$refs.demandLineTable.initProjict();

          this.searchInfo = {};
        });
    },
    resubmit(data) {
      // console.log(data,'coooo')
      if (
        data?.docStatus === "INIT" ||
        (data?.VBELN &&
          data?.demandLineList.filter(({ lineStatus }) => lineStatus === "INIT")
            .length)
      ) {
        this.getDetail();
      } else if (data.pushStatus === "commit") {
        this.getDetail();
      }
    },
    handleClick(val, event) {
      this.activeName = val.name;
    },

    handleCancel(data) {
      let list = [...this.$refs.demandLineTable.newlineList];
      if (data) {
        list = list.map((item) => {
          if (item.demandLineId === data.demandLineId) {
            item.ABGRU = '12';
          }
          return item;
        });
        this.$refs.demandLineTable.salesOrderLineList = list;
        this.submitTip("save");

        return;
      } else {
        const multipleSelection = this.$refs.demandLineTable.multipleSelection;
        let newMultipleSelection = multipleSelection.length
          ? multipleSelection
          : this.$refs.demandLineTable.newlineList;

        newMultipleSelection = newMultipleSelection
          .filter(({ lineStatus = '',ABGRU = '',demandHeadId = '' }) => lineStatus !== "TROD" && !ABGRU && demandHeadId)
          .map(({ demandLineId }) => demandLineId);


        if (newMultipleSelection.length === 0) {
          this.$modal.msgError("暂无数据取消");
          return;
        }
        list = list.map((item) => {
          return {
            ...item,
            ABGRU: newMultipleSelection.includes(item.demandLineId)
              ? '12'
              : undefined,
          };
        });

        this.$refs.demandLineTable.salesOrderLineList = list;
        this.submitTip("save");
      }
    },

    handFuntion(type, data) {
      if (type === "cancel") {
        this.$confirm("是否取消该数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.handleCancel(data);
        });
      } else if (type === "pipei") {
        const multipleSelection = [
          ...this.$refs.demandLineTable.multipleSelection,
        ];
        if (!multipleSelection || multipleSelection.length === 0) {
          this.$modal.msgError("请选择行信息");
          return;
        }
        if (
          multipleSelection.findIndex(
            ({ lineStatus }) => !["INIT", "PTOD"].includes(lineStatus)
          ) > -1
        ) {
          this.$modal.msgError(
            "所选行数据状态应为“待确认、待转订单”，请重新选择"
          );
          return;
        }
        this.getQueryPartsInfo();
        this.changeVisible = true;
      } else if (type === "pipeiOK") {
        const selectIds = Object.keys(this.$refs.ZZMATNRChange.selectIds);
        if (selectIds.length === 0) {
          this.$modal.msgError("暂无可替换的数据");
          return;
        }
        const list = [...this.$refs.demandLineTable.newlineList];
        const data = changePipeiList(list, this.$refs.ZZMATNRChange.selectIds);
        this.$refs.demandLineTable.salesOrderLineList = data;
        this.submitTip("save");
        this.changeVisible = false;
        this.$refs.ZZMATNRChange.editId = "";
        this.$refs.ZZMATNRChange.editItem = {};
      } else if (type === "tosales") {
        // const multipleSelection = [
        //   ...this.$refs.demandLineTable.multipleSelection,
        // ];
        const lineList = this.$refs.demandLineTable.newlineList.filter(({ lineStatus,ABGRU })=>(lineStatus === 'PTOD' && ABGRU !== '12'));



        if (!lineList || lineList.length === 0) {
          this.$modal.msgError("暂无行信息转销售订单");
          return;
        }
        if (!this.orderTypeList || this.orderTypeList.length === 0) {
          this.$forceUpdate();
        }

        if (!this?.demandDetail?.ordertype) {
          this.oriderValue = "ZMOR";
        }

        this.orderVisible = true;
      }
    },

    submitTip(type) {
      if (type === "confirm" && this.demandDetail.ihrezE === "采购申请") {
        this.$refs.ComSearchInfo.onSubmit("commit");
      } else {
        this.$refs.ComSearchInfo.onSubmit(type);
      }
    },
    async handDemand(searchInfoData = {}, type) {
      // 需求转销售
      if (type === "tosales") {
        this.tosalesSearchInfoData = searchInfoData;
        this.handFuntion("tosales", searchInfoData);

        return;
      }

      if (this.salesOrderLineList.length === 0 && type !== "save") {
        this.$modal.msgError("请添加行信息");
        if (this.$refs.demandLineTable.checkList()) return;
        return;
      }

      let list = this.$refs.demandLineTable.newlineList || [];
      if (this.loading) return;
      this.loading = true;

      let POSNR = 0;
      list.forEach((item) => {
        if (Number(item?.POSNR) >= POSNR) {
          POSNR = Number(item.POSNR);
        }
      });


      list = list.map((item) => {
        const newPOSNR = item?.POSNR || Number(POSNR) + 10;
        if(!item?.POSNR){
          POSNR = Number(POSNR) + 10;
        }
        
        return {
          ...item,
          KWMENG: item?.KWMENG ? item.KWMENG * 1 : 1,
          KBETR: item?.KBETR ? item?.KBETR * 1 : 0,
          KBETR1: item?.KBETR1 ? item.KBETR1 * 1 : 0,
          demandLineId: !item?.demandHeadId ? undefined : item?.demandLineId,
          POSNR: `${newPOSNR}`,
          VBELN: searchInfoData?.VBELN,
          demandNo: searchInfoData?.demandNo,
          BESKZ: item?.BESKZ !== "X" ? item?.BESKZ : "",
        };
      });

      // console.log(list,'list')


      // return

      // 保存
      if (type === "save") {
        const params = {
          ...this.demandDetail,
          ...searchInfoData,
          action: "add",
          demandLineList: list,
          delFlag: "0",
          delLineId: [],
        };

        let handeFun = addDemand;
        // 修改
        if (this.demandDetail?.demandHeadId) {
          const delLineId = this.$refs.demandLineTable.delLineId;
          params.action = "upd";
          handeFun = updateDemand;
          params.delLineId = delLineId;
        }

        handeFun(params)
          .then((res) => {
            this.loading = false;
            if (res?.data && !this?.demandDetail?.demandHeadId) {
               this.$refs?.demandLineTable?.initProjict();
              this.$store
                .dispatch("tagsView/delPage", { name: "DemandDetail" })
                .then(() => {
                  this.$router.replace(
                    `/sales/demand-detail/index/edit/${res?.data?.demandHeadId}`
                  );
                });

              this.$modal.msgSuccess("新增成功");
            } else if (res.code === 200) {
              this.getDetail();
              this.$modal.msgSuccess(res?.msg || res?.data?.msg || "修改成功");
            }
          })
          .catch((error) => {
            console.log(error);
          })
          .finally(() => {
            this.loading = false;
          });
      } else if (type === "commit") {
        const delLineId = this.$refs.demandLineTable.delLineId;
        // 提交
        const params = {
          ...this.demandDetail,
          ...searchInfoData,
          action: "submit",
          demandLineList: list,
          delLineId,
        };

        addDemand(params)
          .then((res) => {
            this.loading = false;
            this.$refs?.demandLineTable?.initProjict();
            if (res?.data && !this?.demandDetail?.demandHeadId) {
              // this.$store.dispatch("tagsView/delPage", { name: "DemandDetail" });
              this.$store
                .dispatch("tagsView/delPage", { name: "DemandDetail" })
                .then(() => {
                  this.$router.replace({
                    path: `/sales/demand-detail/index/edit/${res?.data?.demandHeadId}`,
                    query: { clickType: "commit" },
                  });
                });
              this.$modal.msgSuccess("提交成功");
            } else if (res.code === 200) {
              this.$modal.msgSuccess("提交成功");
              this.getDetail("commit");
            }
          })
          .catch((error) => {
            console.log(error);
          })
          .finally(() => {
            this.loading = false;
          });
      } else if (type === "confirm") {
        const delLineId = this.$refs.demandLineTable.delLineId || [];
        const params = {
          ...this.demandDetail,
          ...searchInfoData,
          action: "upd",
          demandLineList: list,
          delLineId,
        };

        const indexZZ = list?.findIndex(({ demandHeadId }) => !demandHeadId);

        if (indexZZ > -1 || this.demandDetail.docStatus !== "CONF") {
          try {
            const checkHeader = checkObj(
              columnsKeysArr,
              this.demandDetail,
              searchInfoData
            );
            const checkLine = checkArr(
              rderLineCloumnKeys,
              this.demandDetail?.demandLineList,
              list
            );
            // 如果有修改先走修改后再调用确认
            if (checkHeader || checkLine) {
              const res1 = await updateDemand(params);
              if (res1.code === 200) {
                this.setConf(list, { ...params, indexZZ });
              } else {
                this.loading = false;
              }
            } else {
              this.setConf(list, { ...params, indexZZ });
            }
          } catch (error) {
            this.loading = false;
          }
        } else {
          this.setConf(list, { ...params, indexZZ });
        }
      }
    },

    confirmsLine() {
      this.$confirm("是否全部行确认", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.submitTip("confirm");
      });
    },

    oneSetConf(obj) {
      if (!obj?.KWMENG) {
        this.$message({
          type: "error",
          message: `请输入数量`,
        });
        return;
      }
      if (!obj?.ZZMATNR) {
        this.$message({
          type: "error",
          message: `请选择专卖物料号`,
        });
        return;
      }
      const list = [obj];

      const params = {
        ...this.demandDetail,
      };
      this.setConf(list, params);
    },

    setConf(list, params) {
      try {
        if (this.demandDetail.docStatus !== "CONF") {
          // 头确认
          updateDemandStatus([
            {
              UPMD: "H",
              VBELN: params?.VBELN,
              CFStatus: "CONF",
            },
          ]);
        }
        const list2 = list.filter(({ lineStatus,ABGRU }) =>( lineStatus === "INIT") && (ABGRU != '12'));

        if (!list2 || list2.length === 0) {
          this.loading = false;
          this.$modal.msgSuccess("暂无确认的数据");
          return;
        }
        const newData = list2.map((item) => {
          return {
            UPMD: "I",
            VBELN: item?.VBELN || params?.VBELN,
            POSNR: Number(item?.POSNR),
            CFStatus: "PTOD",
          };
        });
        // 确认
        updateDemandStatus(newData)
          .then((res) => {
            this.loading = false;
            this.getDetail("CONF");
            if (res.code) {
              this.$modal.msgSuccess("操作成功");
            }
          })
          .catch((error) => {
            this.loading = false;
            console.log(error);
            this.getDetail("CONF");
            if (params?.indexZZ > -1) {
            }
          });
      } catch (error) {
        this.loading = false;
        console.log(error, "error");
      }
    },

    //转销售订单
    handenOk() {
      let unsetList = [];
      let multipleSelection = this.$refs.demandLineTable.multipleSelection;

      if (!multipleSelection || multipleSelection.length === 0) {
        multipleSelection = this.$refs.demandLineTable.newlineList;
      }
      let list = multipleSelection.filter((item) => (item.lineStatus === "PTOD") && (item.ABGRU != '12'));
      unsetList = multipleSelection.filter(
        (item) => item.lineStatus !== "PTOD"
      );

      // console.log(unsetList,'unsetList')
      if (list && list.length === 0) {
        this.$message({
          type: "error",
          message: `暂无转销售订单的数据`,
        });
        return;
      }

      list = list.map((item) => {
        return {
          ...item,
          // ABGRU:undefined,
          KWMENG: item?.KWMENG ? item.KWMENG * 1 : 1,
          KBETR: item?.KBETR ? item?.KBETR * 1 : 0,
          KBETR1: item?.KBETR1 ? item.KBETR1 * 1 : 0,
          salesLineId: item.demandLineId || "",
          KWMENG_SD: 0,
          KWMENG_TH: 0,
          KWMENG_CK: 0,
          KWMENG_QX: 0,
          KWMENG_SQ: 0,
        };
      });
      if (!this.oriderValue) {
        this.$message({
          type: "error",
          message: `请选择首先订单类型`,
        });

        return;
      }
      let orderTypes = [this.oriderValue];
      if (this.oriderValue2) {
        orderTypes.push(this.oriderValue2);
      }
      const params = {
        ...this.demandDetail,
        ...this.tosalesSearchInfoData,
        action: "submit",
        type: "demand",
        salesOrderLineList: list,
        delFlag: 0,
        orderTypes: orderTypes,
        errMsg: "",
      };

      delete params.demandLineList;
      if (this.loading) return;
      this.loading = true;
      addHead(params)
        .then((res) => {
          this.loading = false;
          this.$modal.msgSuccess(res.data?.msg || "操作成功");
          if (res.code === 200) {
            this.getDetail();
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log(error, "error");
        });
      this.orderVisible = false;
    },
    searchHandenOk(type, data, callBack) {
      if (type === "salesOrg") {
        callBack({
          VKORG: data?.salesOrg || "",
          salesOrgName: data?.salesOrgName || "",
          WERKS: data?.werks,
          werksName: data?.werksName,
        });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        `sales/demand/export/${this.demandDetail.demandHeadId}`,
        {
          ...this.queryParams,
        },
        `需求单行明细_.xlsx`
      );
    },
    closeEdit() {
      this.$refs.demandLineTable.closeEdit();
    },
    getQueryPartsInfo() {
      const multipleSelection =
        this.$refs.demandLineTable.multipleSelection.map(
          ({ ZZMATNR }) => ZZMATNR
        );
      if (multipleSelection && multipleSelection.length === 0) {
        this.$message({
          type: "error",
          message: `请选择匹配的数据`,
        });
        return;
      }
      queryPartsInfo(multipleSelection)
        .then((res) => {
          // console.log(res, "res");
          if (res.code === 200) {
            this.pipeiList = changeLineData(res.data);
            // console.log(this.pipeiList, "this.pipeiList");
          } else {
            this.pipeiList = [];
          }
        })
        .catch(() => {
          this.pipeiList = [];
        });
    },
    checksearchMsg(item, dataInfo) {
      if (item.prop === "KUNNR" && !dataInfo["VKORG"]) {
        this.$message({
          type: "error",
          message: `请选择选择销售组织`,
        });
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.salesAdd {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
}

.lineBtn {
  display: flex;
  justify-content: space-between;
}

.orderType {
  display: flex;
  line-height: 36px;
  margin-bottom: 12px;
}

.ZZMATNRName {
  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0px !important;
  }

  ::v-deep .el-dialog__body {
    padding: 0px;
  }
}
</style>
