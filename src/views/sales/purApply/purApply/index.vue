<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="采购申请编号" prop="BANFN">
        <el-input
          v-model="queryParams.BANFN"
          placeholder="请输入采购申请编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购申请行项目" prop="BANFNS">
        <el-input
          v-model="queryParams.BANFNS"
          placeholder="请输入采购申请行项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料号" prop="MATNR">
        <el-input
          v-model="queryParams.MATNR"
          placeholder="请输入物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专卖物料号" prop="ZZMATNR">
        <el-input
          v-model="queryParams.ZZMATNR"
          placeholder="请输入专卖物料号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购组织" prop="EKORG">
        <el-input
          v-model="queryParams.EKORG"
          placeholder="请输入采购组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购组" prop="EKGRP">
        <el-input
          v-model="queryParams.EKGRP"
          placeholder="请输入采购组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工厂" prop="WERKS">
        <el-input
          v-model="queryParams.WERKS"
          placeholder="请输入工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales/purApply:purApply:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales/purApply:purApply:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales/purApply:purApply:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales/purApply:purApply:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purApplyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="purApplyId" />
      <el-table-column label="采购申请编号" align="center" prop="BANFN" />
      <el-table-column label="采购申请行项目" align="center" prop="BANFNS" />
      <el-table-column label="物料号" align="center" prop="MATNR" />
      <el-table-column label="专卖物料号" align="center" prop="ZZMATNR" />
      <el-table-column label="采购申请凭证类型" align="center" prop="BSART" />
      <el-table-column label="采购组织" align="center" prop="EKORG" />
      <el-table-column label="采购组" align="center" prop="EKGRP" />
      <el-table-column label="工厂" align="center" prop="WERKS" />
      <el-table-column label="数量" align="center" prop="KWMENG" />
      <el-table-column label="库存地点" align="center" prop="LGORT" />
      <el-table-column label="行备注" align="center" prop="iMemo" />
      <el-table-column label="单据状态" align="center" prop="docStatus" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales/purApply:purApply:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales/purApply:purApply:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采购申请编号" prop="BANFN">
          <el-input v-model="form.BANFN" placeholder="请输入采购申请编号" />
        </el-form-item>
        <el-form-item label="采购申请行项目" prop="BANFNS">
          <el-input v-model="form.BANFNS" placeholder="请输入采购申请行项目" />
        </el-form-item>
        <el-form-item label="物料号" prop="MATNR">
          <el-input v-model="form.MATNR" placeholder="请输入物料号" />
        </el-form-item>
        <el-form-item label="专卖物料号" prop="ZZMATNR">
          <el-input v-model="form.ZZMATNR" placeholder="请输入专卖物料号" />
        </el-form-item>
        <el-form-item label="采购申请凭证类型" prop="BSART">
          <el-input v-model="form.BSART" placeholder="请输入采购申请凭证类型" />
        </el-form-item>
        <el-form-item label="采购组织" prop="EKORG">
          <el-input v-model="form.EKORG" placeholder="请输入采购组织" />
        </el-form-item>
        <el-form-item label="采购组" prop="EKGRP">
          <el-input v-model="form.EKGRP" placeholder="请输入采购组" />
        </el-form-item>
        <el-form-item label="工厂" prop="WERKS">
          <el-input v-model="form.WERKS" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="数量" prop="KWMENG">
          <el-input v-model="form.KWMENG" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="库存地点" prop="LGORT">
          <el-input v-model="form.LGORT" placeholder="请输入库存地点" />
        </el-form-item>
        <el-form-item label="行备注" prop="iMemo">
          <el-input v-model="form.iMemo" placeholder="请输入行备注" />
        </el-form-item>
        <el-form-item label="删除表示" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除表示" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurApply, getPurApply, delPurApply, addPurApply, updatePurApply } from "@/api/sales/purApply/purApply";

export default {
  name: "PurApply",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购申请表格数据
      purApplyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        BANFN: null,
        BANFNS: null,
        MATNR: null,
        ZZMATNR: null,
        EKORG: null,
        EKGRP: null,
        WERKS: null,
        docStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购申请列表 */
    getList() {
      this.loading = true;
      listPurApply(this.queryParams).then(response => {
        this.purApplyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purApplyId: null,
        BANFN: null,
        BANFNS: null,
        MATNR: null,
        ZZMATNR: null,
        BSART: null,
        EKORG: null,
        EKGRP: null,
        WERKS: null,
        KWMENG: null,
        LGORT: null,
        iMemo: null,
        docStatus: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.purApplyId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const purApplyId = row.purApplyId || this.ids
      getPurApply(purApplyId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改采购申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.purApplyId != null) {
            updatePurApply(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurApply(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const purApplyIds = row.purApplyId || this.ids;
      this.$modal.confirm('是否确认删除采购申请编号为"' + purApplyIds + '"的数据项？').then(function() {
        return delPurApply(purApplyIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/purApply/purApply/export', {
        ...this.queryParams
      }, `purApply_.xlsx`)
    }
  }
};
</script>
