<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="SAP销售订单号" prop="vbelnSo">
        <el-input
          v-model="queryParams.vbelnSo"
          placeholder="请输入销售订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAP交货单号" prop="vbelnDr">
        <el-input
          v-model="queryParams.vbelnDr"
          placeholder="请输入销售交货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAS交货单号" prop="salesDeliveryNo">
        <el-input
          v-model="queryParams.salesDeliveryNo"
          placeholder="请输入销售外向交货单单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sales/salesDelivery:salesDelivery:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sales/salesDelivery:salesDelivery:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sales/salesDelivery:salesDelivery:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['sales/salesDelivery:salesDelivery:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="salesDeliveryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="deliveryHeadId" />
      <el-table-column label="系统编码" align="center" prop="ivSysno" />
      <el-table-column label="接口编码" align="center" prop="ivItfno" />
      <el-table-column label="操作类型" align="center" prop="TYPE" />
      <el-table-column label="销售订单号" align="center" prop="vbelnSo" />
      <el-table-column label="销售交货单号" align="center" prop="vbelnDr" />
      <el-table-column label="装运点" align="center" prop="VSTEL" />
      <el-table-column label="计划发货日期" align="center" prop="LFDAT" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.LFDAT, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="抬头备注" align="center" prop="hMemo" />
      <el-table-column label="销售外向交货单单号" align="center" prop="salesDeliveryNo" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sales/salesDelivery:salesDelivery:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sales/salesDelivery:salesDelivery:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改销售外向交货单头信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="系统编码" prop="ivSysno">
          <el-input v-model="form.ivSysno" placeholder="请输入系统编码" />
        </el-form-item>
        <el-form-item label="接口编码" prop="ivItfno">
          <el-input v-model="form.ivItfno" placeholder="请输入接口编码" />
        </el-form-item>
        <el-form-item label="销售订单号" prop="vbelnSo">
          <el-input v-model="form.vbelnSo" placeholder="请输入销售订单号" />
        </el-form-item>
        <el-form-item label="销售交货单号" prop="vbelnDr">
          <el-input v-model="form.vbelnDr" placeholder="请输入销售交货单号" />
        </el-form-item>
        <el-form-item label="装运点" prop="VSTEL">
          <el-input v-model="form.VSTEL" placeholder="请输入装运点" />
        </el-form-item>
        <el-form-item label="计划发货日期" prop="LFDAT">
          <el-date-picker clearable
            v-model="form.LFDAT"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划发货日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="抬头备注" prop="hMemo">
          <el-input v-model="form.hMemo" placeholder="请输入抬头备注" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="销售外向交货单单号" prop="salesDeliveryNo">
          <el-input v-model="form.salesDeliveryNo" placeholder="请输入销售外向交货单单号" />
        </el-form-item>
        <el-divider content-position="center">销售外向交货单行信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddSalesDeliveryLine">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteSalesDeliveryLine">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="salesDeliveryLineList" :row-class-name="rowSalesDeliveryLineIndex" @selection-change="handleSalesDeliveryLineSelectionChange" ref="salesDeliveryLine">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="行号" prop="POSNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.POSNR" placeholder="请输入行号" />
            </template>
          </el-table-column>
          <el-table-column label="物料号" prop="MATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.MATNR" placeholder="请输入物料号" />
            </template>
          </el-table-column>
          <el-table-column label="专卖物料号" prop="ZZMATNR" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ZZMATNR" placeholder="请输入专卖物料号" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="KWMENG" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.KWMENG" placeholder="请输入数量" />
            </template>
          </el-table-column>
          <el-table-column label="工厂" prop="WERKS" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.WERKS" placeholder="请输入工厂" />
            </template>
          </el-table-column>
          <el-table-column label="库存地点" prop="LGORT" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.LGORT" placeholder="请输入库存地点" />
            </template>
          </el-table-column>
          <el-table-column label="行备注" prop="iMemo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.iMemo" placeholder="请输入行备注" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSalesDelivery, getSalesDelivery, delSalesDelivery, addSalesDelivery, updateSalesDelivery } from "@/api/sales/salesDelivery/salesDelivery";

export default {
  name: "SalesDelivery",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedSalesDeliveryLine: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 销售外向交货单头信息表格数据
      salesDeliveryList: [],
      // 销售外向交货单行信息表格数据
      salesDeliveryLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vbelnSo: null,
        vbelnDr: null,
        salesDeliveryNo: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询销售外向交货单头信息列表 */
    getList() {
      this.loading = true;
      listSalesDelivery(this.queryParams).then(response => {
        this.salesDeliveryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deliveryHeadId: null,
        ivSysno: null,
        ivItfno: null,
        TYPE: null,
        vbelnSo: null,
        vbelnDr: null,
        VSTEL: null,
        LFDAT: null,
        hMemo: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        salesDeliveryNo: null
      };
      this.salesDeliveryLineList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.deliveryHeadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加销售外向交货单头信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const deliveryHeadId = row.deliveryHeadId || this.ids
      getSalesDelivery(deliveryHeadId).then(response => {
        this.form = response.data;
        this.salesDeliveryLineList = response.data.salesDeliveryLineList;
        this.open = true;
        this.title = "修改销售外向交货单头信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.salesDeliveryLineList = this.salesDeliveryLineList;
          if (this.form.deliveryHeadId != null) {
            updateSalesDelivery(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSalesDelivery(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const deliveryHeadIds = row.deliveryHeadId || this.ids;
      this.$modal.confirm('是否确认删除销售外向交货单头信息编号为"' + deliveryHeadIds + '"的数据项？').then(function() {
        return delSalesDelivery(deliveryHeadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 销售外向交货单行信息序号 */
    rowSalesDeliveryLineIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 销售外向交货单行信息添加按钮操作 */
    handleAddSalesDeliveryLine() {
      let obj = {};
      obj.POSNR = "";
      obj.MATNR = "";
      obj.ZZMATNR = "";
      obj.KWMENG = "";
      obj.WERKS = "";
      obj.LGORT = "";
      obj.iMemo = "";
      this.salesDeliveryLineList.push(obj);
    },
    /** 销售外向交货单行信息删除按钮操作 */
    handleDeleteSalesDeliveryLine() {
      if (this.checkedSalesDeliveryLine.length == 0) {
        this.$modal.msgError("请先选择要删除的销售外向交货单行信息数据");
      } else {
        const salesDeliveryLineList = this.salesDeliveryLineList;
        const checkedSalesDeliveryLine = this.checkedSalesDeliveryLine;
        this.salesDeliveryLineList = salesDeliveryLineList.filter(function(item) {
          return checkedSalesDeliveryLine.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleSalesDeliveryLineSelectionChange(selection) {
      this.checkedSalesDeliveryLine = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sales/salesDelivery/salesDelivery/export', {
        ...this.queryParams
      }, `salesDelivery_.xlsx`)
    }
  }
};
</script>
