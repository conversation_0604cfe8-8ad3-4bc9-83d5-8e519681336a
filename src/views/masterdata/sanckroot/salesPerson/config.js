import { listSales<PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON>, delS<PERSON><PERSON><PERSON>, addS<PERSON><PERSON><PERSON>, update<PERSON><PERSON><PERSON><PERSON> } from "@/api/master/salesPerson";

export const cloumn = [
  {
    label: "销售人员",
    type: "input",
    prop: "salesPerson",
  },

  {
    label: "主管",
    type: "input",
    prop: "supervisor",
  },


];

export const pageConfig = {
  indexPageConfig: {
    title: "扫码销售人员主管配置",
    name: "SalesPerson",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "id",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:salesPerson:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:salesPerson:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/salesPerson/export",
        clickType:'export',
        hasPermi:['master:salesPerson:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:salesPerson:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:salesPerson:remove']


      },
    ],
    // 列表
    getList: listSalesPerson,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addSalesPerson,
    // 更新
    updataFuntion: updateSalesPerson,
    // 删除
    delFunction: delSalesPerson,
    // 详情
    detailFunction: getSalesPerson,
  },
};
