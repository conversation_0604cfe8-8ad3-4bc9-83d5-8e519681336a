import { listLocation, getLocation, delLocation, addLocation, updateLocation } from "@/api/master/location";

export const cloumn = [
  {
    label: "库存地点",
    type: "input",
    prop: "location",
  },

  {
    label: "库存地点描述",
    type: "input",
    prop: "locationName",
  },


];

export const pageConfig = {
  indexPageConfig: {
    title: " 扫码子库配置",
    name: "Location",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "id",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:location:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:location:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/location/export",
        clickType:'export',
        hasPermi:['master:location:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:location:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:location:remove']


      },
    ],
    // 列表
    getList: listLocation,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addLocation,
    // 更新
    updataFuntion: updateLocation,
    // 删除
    delFunction: delLocation,
    // 详情
    detailFunction: getLocation,
  },
};
