import { listExclusion, getExclusion, delExclusion, addExclusion, updateExclusion } from "@/api/master/exclusion";

export const cloumn = [
  {
    label: "库存地点",
    type: "input",
    prop: "location",
  },

  {
    label: "库存地点描述",
    type: "input",
    prop: "locationName",
  },


];

export const pageConfig = {
  indexPageConfig: {
    title: "扫码排除子库配置",
    name: "Exclusion",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "id",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:exclusion:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:exclusion:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/exclusion/export",
        clickType:'export',
        hasPermi:['master:exclusion:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:exclusion:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:exclusion:remove']


      },
    ],
    // 列表
    getList: listExclusion,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addExclusion,
    // 更新
    updataFuntion: updateExclusion,
    // 删除
    delFunction: delExclusion,
    // 详情
    detailFunction: getExclusion,
  },
};
