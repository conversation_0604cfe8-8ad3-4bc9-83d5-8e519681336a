import { listCategory, getCategory, delCategory, addCategory, updateCategory } from "@/api/master/category";

export const cloumn = [
  {
    label: "物料大类",
    type: "input",
    prop: "zwldl",
  },

  {
    label: "物料大类描述",
    type: "input",
    prop: "zwldlName",
  },

    {
    label: "物料小类",
    type: "input",
    prop: "zwlxl",
  },

  {
    label: "物料小类描述",
    type: "input",
    prop: "zwlxlName",
  },


];

export const pageConfig = {
  indexPageConfig: {
    title: "扫码物料大类和物料小类配置 ",
    name: "Category",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "id",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:category:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:category:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/category/export",
        clickType:'export',
        hasPermi:['master:category:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:category:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:category:remove']


      },
    ],
    // 列表
    getList: listCategory,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addCategory,
    // 更新
    updataFuntion: updateCategory,
    // 删除
    delFunction: delCategory,
    // 详情
    detailFunction: getCategory,
  },
};
