import { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial } from "@/api/master/material";

export const cloumn = [
  {
    label: "物料号",
    type: "input",
    prop: "matnr",
  },

  {
    label: "物料名称",
    type: "input",
    prop: "matnrName",
  },


];

export const pageConfig = {
  indexPageConfig: {
    title: "扫码物料编号配置",
    name: "Material",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "id",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:material:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:material:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/material/export",
        clickType:'export',
        hasPermi:['master:material:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:material:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:material:remove']


      },
    ],
    // 列表
    getList: listMaterial,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addMaterial,
    // 更新
    updataFuntion: updateMaterial,
    // 删除
    delFunction: delMaterial,
    // 详情
    detailFunction: getMaterial,
  },
};
