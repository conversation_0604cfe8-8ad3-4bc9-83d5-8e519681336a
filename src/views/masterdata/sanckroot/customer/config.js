import { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer } from "@/api/master/customer";

export const cloumn = [
  {
    label: "客户代码",
    type: "input",
    prop: "kunnr",
  },

  {
    label: "客户名称",
    type: "input",
    prop: "kunnrName",
  },


];

export const pageConfig = {
  indexPageConfig: {
    title: "扫码客户配置",
    name: "SanckCustomer",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "id",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:customer:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:customer:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/customer/export",
        clickType:'export',
        hasPermi:['master:customer:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:customer:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:customer:remove']


      },
    ],
    // 列表
    getList: listCustomer,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addCustomer,
    // 更新
    updataFuntion: updateCustomer,
    // 删除
    delFunction: delCustomer,
    // 详情
    detailFunction: getCustomer,
  },
};
