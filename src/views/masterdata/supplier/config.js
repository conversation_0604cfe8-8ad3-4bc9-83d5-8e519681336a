
export const columns = [
    {
    label: "供应商编码",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "供应商名称",
    type: "input",
    prop: "KUNNR",
  },
      {
    label: "供应商简称",
    type: "input",
    prop: "KUNNR",
  },

  {
    label: "供应商类型",
    type: "input",
    prop: "VKORG",
  },


  {
    label: "是否启用",
    type: "input",
    prop: "VTWEG",
  },
 
];

export const lineColumns = [
  {
    prop:'LIFNR',
   label:'供应商编号',
   width:'140px',
  },
   {
    prop:'EKORG',
   label:'采购组织',
   width:'140px',
  },

    {
    prop:'ZGYSLB',
   label:'供应商类别',
   width:'140px',
  },
   {
    prop:'ZGYSLBTXT',
   label:'供应商类别描述',
   width:'140px',
  },
  

    {
    prop:'ZSQBS',
   label:'授权标识',
   width:'140px',
  },
   {
    prop:'ZSQBSTXT',
   label:'授权标识描述',
   width:'140px',
  },

  
    {
    prop:'ZJSBS',
   label:'寄售标识',
   width:'140px',
  },
   {
    prop:'ZJSBSTXT',
   label:'寄售标识描述',
   width:'140px',
  },

  
    {
    prop:'ZGYSL',
   label:'供应商分类',
   width:'140px',
  },
   {
    prop:'ZGYSLTXT',
   label:'供应商分类描述',
   width:'140px',
  },
  {
    prop:'ZXKBL',
   label:'现款比例',
   width:'140px',
  },
  

]


export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

