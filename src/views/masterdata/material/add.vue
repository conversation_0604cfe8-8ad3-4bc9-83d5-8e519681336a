<template>
  <div class="add" v-loading="loading">
    <div class="pageHeader">
      <div v-if="pageType !== 'look'">
        <el-button type="primary" size="mini" @click="submitTip('save')"
          >{{ pageType === "add" ? "保存" : "修改" }}
        </el-button>
      </div>
    </div>
    <ComSearch
      :searchData="searchData"
      :searchInfo="searchInfo"
      :showNum="16"
      labelWidth="140px"
      :showBtn="false"
      :rules="rules"
      collapseTitle="更多内容"
      ref="ComSearchInfo"
      @submitEmits="submitEmits"
      @changeSearchInfo="(val)=>{
        searchInfo = val
      }"
    />
    <LineTable
      :lineList="masterLineList"
      :btnProps="btnProps"
      :lineCloumn="lineColumns"
      :requiredKeys="['ZZMATNR', 'salesOrgName']"
      @changeLineList="(val)=>masterLineList=val"
    >
    </LineTable>
  </div>
</template>
<script>
import _ from "lodash";
import ComSearch from "@/components/ComSearch/index.vue";
import LineTable from "@/components/LineTable/index.vue";
import { columns, lineColumns, btnProps } from "./config";
import { getMaster, addMaster, updateMaster } from "@/api/master/master";

export default {
  name: "MaterialDetail",
  components: {
    ComSearch,
    LineTable,
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      searchInfo: {},
      rules: {},
      searchData: columns,
      pageType,
      lineColumns,
      btnProps,
      masterLineList: [],
      detail: {},
    };
  },

  created() {
    this.getMaster()
  },
  watch: {},
  methods: {
    submitTip(type) {
      this.$refs.ComSearchInfo.onSubmit(type);
    },
    submitEmits(searchInfoData = {}) {
      const params = {
        ...this.detail,
        ...searchInfoData,
        masterLineList:this.masterLineList
      };
      let handeFun = addMaster;
      // 修改
      if (this.detail?.masterHeadId) {
        params.action = "upd";
        handeFun = updateMaster;
      }

      handeFun(params)
        .then((res) => {
          this.loading = false;
          if (res?.data && !this?.detail?.masterHeadId) {
            this.$store
              .dispatch("tagsView/delPage", { name: "MaterialDetail" })
              .then(() => {
                this.$router.replace(
                  `/sales/demand-detail/index/edit/${res?.data?.masterHeadId}`
                );
              });
            this.$modal.msgSuccess("新增成功");
          } else if (res.code === 200) {
            this.getMaster();
            this.$modal.msgSuccess("修改成功");
          }
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 修改按钮操作 */
    getMaster() {
      if (this.$route.params.type === 'add') return;
      const masterHeadId = this.$route.params.id;
      getMaster(masterHeadId).then((response) => {
        this.searchInfo = response.data;
        this.detail = response.data;
        this.masterLineList = response.data.masterLineList;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.add {
  padding: 20px;
}

.pageHeader {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 20px;
}
</style>
