import { listMaster, getMaster, delMaster, addMaster, updateMaster } from "@/api/master/master";

export const cloumn = [
  "ZZMATNR	专卖物料号",
  "MTART	物料类型编码",
  "MTBEZ	物料类型中文描述",
  "MEINS	基本单位",
  "MSEHT	基本单位中文描述",
  "MATKL	物料组",
  "WGBEZ	物料组中文描述",
  "SPART	产品组",
  "BISMT	旧物料号",
  "MAKTX_ZH	中文物料描述",
  "MAKTX_EN	英文物料描述",
  "BRGEW	毛重",
  "GEWEI	重量单位",
  "VOLUM	体积",
  "VOLEH	体积单位",
  "ZPARH	图号",
  "ZSPLX	商品类型",
  "ZTZZL	图纸重量",
  "ZYWLB	业务类别",
  "ZBZBZL	标准包装量",
  "ZXSBZL	销售包装量",
  "ZXSBZLZJ	销售包装量注解",
  "ZFHBZL	发货包装量",
  "ZFHBZLZJ	发货包装量注解",
  "ZSBQ	三包期（月）",
  "ZLJBH	启用零件加工编号",
  "ZBWBS	保外标识",
  "ZSYJX	适用机型",
  "ZBZLX	包装类型",
  "ZGHJZRQ	供货截止日期",
  "ZSFZMKF	是否专卖开发",
  "ZGGXH	规格",
  "ZMLMCXS	适用马力/设定摩擦系数",
  "ZHJCS	花键参数",
  "ZWLDL	物料大类（产品大类）",
  "ZWLXL	物料小类",
  "ZZXFL	专项分类",
  "ZWLZTM	状态码",
  "ZGLFWM	是否关联防伪码",
  "ZBZJS	包装件数",
  "ZNQCC	内腔尺寸",
  "ZDHCO	刀横尺寸",
  "ZBZJG	包装结构",
  "ZBZYO	包装用材",
  "ZFZBCWL	辅助包装物料号",
  "ERNAM	对象创建者的姓名",
  "ERSDA	创建日期",
  "AENAM	更改对象用户的名称",
  "ZSPLXTXT	商品类型描述",
  "ZBZLXTXT	包装类型描述",
  "ZWLDLTXT	物料大类（产品大类）描述",
  "ZWLXLTXT	物料小类描述",
  "ZZXFLTXT	专项分类描述",
  "ZWLZTMTXT	状态码描述",
].map((item) => {
  const obj = {};
  const [prop, label] = item.split("	");
  if (["ERSDA", "ZGHJZRQ"].includes(prop)) {
    obj.type = "date";
  }

  return {
    prop,
    label,
    width:140,
    ...obj,
  };
});


export const lineColumns = [
  {
    prop: "ZZMATNR",
    label: "专卖物料号",
    width: "200px",
  },
  {
    prop: "VKORG",
    label: "销售组织",
    width: "200px",
    append:true,
    attr: {
      readonly: true,
    },
    propName: "salesOrgName",
    dictOptionCode: "VKORG",
    //  返回的字段映射
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },
  {
    prop: "WERKS",
    label: "工厂",
    width: "200px",
    dictOptionCode: "VKORG",
    propName: "werksName",
    backProps: [{
      backProp: "werks",
      backLable: "werksName",
      prop: "VKORG",
    }],
    attr: {
      disabled: true,
    },
  },

  {
    prop: "EKGRP",
    label: "采购组",
    width: "140px",
  },
  {
    prop: "EKNAM",
    label: "采购组描述",
    width: "140px",
  },

  {
    prop: "BESKZ",
    label: "采购类型",
    width: "140px",
  },
  {
    prop: "MVGR1",
    label: "物料组1",
    width: "140px",
  },

  {
    prop: "WGBEZ1",
    label: "物料组1描述",
    width: "140px",
  },
  {
    prop: "MVGR2",
    label: "物料组2",
    width: "140px",
  },
  {
    prop: "WGBEZ2",
    label: "物料组2描述",
    width: "140px",
  },
  
  {
    prop: "goodsLocation",
    label: "货位",
    // width:140
  },
    
  {
    prop: "ZZMSQBZ",
    label: "是否授权",
    // width:140
  },

];

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["master:master:remove"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["master:master:remove"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["master:master:remove"],
  },
};

const dataProp = ['ZZMATNR','MTART','MTBEZ','ZSPLX','ZYWLB','ZBZLX','ZSPLXTXT']

const searchData = cloumn.filter((item)=>(dataProp.includes(item.prop)))

export const pageConfig = {

  indexPageConfig: {
    title: "物料主数据",
    columns: cloumn,
    searchData: searchData,
    tableProps: {
      rowKey: "masterHeadId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:master:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:master:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "master/master/export",
        clickType:'export',
        hasPermi:['master:master:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:master:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:master:remove']


      },
    ],
    // 列表
    getList: listMaster,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'90%',
    // detailPageUrl: "/index",
  },
  addPageConfig: {
    lineCloumn: cloumn,
    labelWidth:'140px',
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      // item.span = 24
      return item
    }),
    addSearchProps: {},
    lineTableProps: {
      rowKey: "masterLineId",
      listKey:'masterLineList'
    },
    headerBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    lintHeaderBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    headerSearchInfo:{},
    // 新增
    addFunction: addMaster,
    // 更新
    updataFuntion: updateMaster,
    // 删除
    delFunction: delMaster,
    // 详情
    detailFunction: getMaster,
    lineCloumn:lineColumns.map((item)=>{
      item.width = item.width || 140
      return item

    }),
    btnProps:{}
  },
};
