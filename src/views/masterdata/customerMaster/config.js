import { listCustomerMaster, getCustomerMaster, delCustomerMaster, addCustomerMaster, updateCustomerMaster } from "@/api/master/customerMaster";

export const cloumn = [
  {
    label: "客户账户组",
    type: "input",
    prop: "KTOKD",
  },

  {
    label: "客户编码",
    type: "input",
    prop: "KUNNR",
  },

  {
    label: "公司代码",
    type: "input",
    prop: "BUKRS",
  },

  {
    label: "销售组织",
    type: "input",
    prop: "VKORG",
  },


    {
    label: "分销渠道",
    type: "input",
    prop: "VTWEG",
  },

  {
    label: "产品组",
    type: "input",
    prop: "SPART",
  },

  {
    label: "称谓",
    type: "input",
    prop: "titleMedi",
  },

  {
    label: "客户名称(中)",
    type: "input",
    prop: "NAME1",
  },


  
  {
    label: "客户名称(英)",
    type: "input",
    prop: "NAME2",
  },

  {
    label: "收货地址2",
    type: "input",
    prop: "STREET",
  },

  {
    label: "客户名称3",
    type: "input",
    prop: "NAME3",
  },

  {
    label: "客户名称4",
    type: "input",
    prop: "NAME4",
  },

  {
    label: "搜索项",
    type: "input",
    prop: "SORT1",
  },

  {
    label: "客户名称缩写",
    type: "input",
    prop: "SORT2",
  },

   {
    label: "客户详细地址(备用)",
    type: "input",
    prop: "strSuppl1",
  },

   {
    label: "客户详细地址(备用)2",
    type: "input",
    prop: "strSuppl2",
  },

  {
    label: "客户详细地址(备用)3",
    type: "input",
    prop: "strSuppl3",
  },

   {
    label: "客户所在地的邮政编码",
    type: "input",
    prop: "postCode1",
  },


  
   {
    label: "城市",
    type: "input",
    prop: "CITY1",
  },

  {
    label: "客户所属国家",
    type: "input",
    prop: "COUNTRY",
  },

   {
    label: "省/州",
    type: "input",
    prop: "REGION",
  },

  
   {
    label: "语言",
    type: "input",
    prop: "LANGU",
  },

  {
    label: "客户联系电话",
    type: "input",
    prop: "telNumber",
  },

   {
    label: "移动电话",
    type: "input",
    prop: "mobNumber",
  },

    
   {
    label: "客户传真号码",
    type: "input",
    prop: "LANGUfaxNumber",
  },

  {
    label: "客户电子邮件地址",
    type: "input",
    prop: "smtpAddr",
  },

   {
    label: "注释",
    type: "input",
    prop: "REMARK",
  },


  
    
   {
    label: "供应商",
    type: "input",
    prop: "LIFNR",
  },

  {
    label: "客户增值税等记号",
    type: "input",
    prop: "STCEG",
  },

   {
    label: "客户评级结果记录",
    type: "input",
    prop: "KUKLA",
  },

      
   {
    label: "银行国家代码",
    type: "input",
    prop: "BANKS",
  },

  {
    label: "银行代码",
    type: "input",
    prop: "BANKL",
  },

   {
    label: "银行帐户号码",
    type: "input",
    prop: "BANKN",
  },


        
   {
    label: "帐户持有人姓名",
    type: "input",
    prop: "KOINH",
  },

  {
    label: "银行名称",
    type: "input",
    prop: "BANKA",
  },

   {
    label: "排序标准",
    type: "input",
    prop: "ZUAWA",
  },

     {
    label: "旧客户编码",
    type: "input",
    prop: "ALTKN",
  },

  {
    label: "付款条件",
    type: "input",
    prop: "ZTERM",
  },

   {
    label: "国际贸易条款",
    type: "input",
    prop: "INCO1",
  },
   {
    label: "国际贸易条款2",
    type: "input",
    prop: "INCO2",
  },
  
     {
    label: "客户使用的交易货币",
    type: "input",
    prop: "WAERS",
  },

  {
    label: "销售大区划分",
    type: "input",
    prop: "BZIRK",
  },

   {
    label: "客户所属的销售办公室",
    type: "input",
    prop: "VKBUR",
  },

    {
    label: "客户所属的销售小组",
    type: "input",
    prop: "VKGRP",
  },

   {
    label: "客户组",
    type: "input",
    prop: "KDGRP",
  },


      {
    label: "价格组",
    type: "input",
    prop: "KONDA",
  },

   {
    label: "客户定价组",
    type: "input",
    prop: "KALKS",
  },

        {
    label: "价格清单",
    type: "input",
    prop: "PLTYP",
  },

   {
    label: "客户统计组",
    type: "input",
    prop: "VERSG",
  },

  
        {
    label: "暂时不使用",
    type: "input",
    prop: "LPRIO",
  },

   {
    label: "出货条件",
    type: "input",
    prop: "VSBED",
  },
  
   
        {
    label: "交付客户货物的出货工厂",
    type: "input",
    prop: "VWERK",
  },

   {
    label: "客户的科目分配组",
    type: "input",
    prop: "KTGRD",
  },

  {
    label: "税分类",
    type: "input",
    prop: "TAXKD",
  },
   {
    label: "发运方式",
    type: "input",
    prop: "KVGR2",
  },

].map((item)=>{
  item.width = item.width || 140
  return item
});


export const pageConfig = {
  indexPageConfig: {
    title: "客户主数据",
    columns: cloumn,
    searchData: cloumn.slice(0,6),
    tableProps: {
      rowKey: "stomerMasterId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:customerMaster:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:customerMaster:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "master/customerMaster/export",
        clickType:'export',
        hasPermi:['master:customerMaster:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:customerMaster:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:customerMaster:remove']


      },
    ],
    // 列表
    getList: listCustomerMaster,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'90%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      // item.span = 24
      return item
    }),
    addShowNum:cloumn.length,
    addSearchProps: {
    },
    headerSearchInfo:{},
    // 新增
    addFunction: addCustomerMaster,
    // 更新
    updataFuntion: updateCustomerMaster,
    // 删除
    delFunction: delCustomerMaster,
    // 详情
    detailFunction: getCustomerMaster,
  },
};
