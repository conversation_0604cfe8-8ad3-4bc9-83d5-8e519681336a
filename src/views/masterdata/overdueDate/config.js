import { listOverdueDate, getOverdueDate, delOverdueDate, addOverdueDate, updateOverdueDate } from "@/api/master/overdueDate";

export const cloumn = [
  {
    label: "销售组织",
    type: "input",
    prop: "vkorg",
  },

  {
    label: "客户类型",
    type: "input",
    prop: "customerType",
  },

    {
    label: "退货时间",
    type: "input",
    prop: "returnDate",
  },

];

export const pageConfig = {
  indexPageConfig: {
    title: "退货超期配置 ",
    name: "OverdueDate",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "vkorg",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:overdueDate:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:overdueDate:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/overdueDate/export",
        clickType:'export',
        hasPermi:['master:overdueDate:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:overdueDate:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:overdueDate:remove']


      },
    ],
    // 列表
    getList: listOverdueDate,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addOverdueDate,
    // 更新
    updataFuntion: updateOverdueDate,
    // 删除
    delFunction: delOverdueDate,
    // 详情
    detailFunction: getOverdueDate,
  },
};
