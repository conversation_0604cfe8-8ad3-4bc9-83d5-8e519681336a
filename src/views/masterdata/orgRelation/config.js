
import { listOrgRelation, getOrgRelation, delOrgRelation, addOrgRelation, updateOrgRelation } from "@/api/sales/orgRelation/orgRelation";


export const cloumn = [
  {
    label: "集团/成本控制范围",
    type: "input",
    prop: "kokes",
  },

  {
    label: "公司",
    type: "input",
    prop: "company",
  },

  {
    label: "公司名称",
    type: "input",
    prop: "companyName",
  },

  {
    label: "工厂",
    type: "input",
    prop: "werks",
  },


  
  {
    label: "工厂名称",
    type: "input",
    prop: "werksName",
  },

  {
    label: "采购组织",
    type: "input",
    prop: "purOrg",
  },


    
  {
    label: "采购组织名称",
    type: "input",
    prop: "purOrgName",
  },

  {
    label: "销售组织",
    type: "input",
    prop: "salesOrg",
  },


   {
    label: "销售组织名称",
    type: "input",
    prop: "salesOrgName",
  },


      
  {
    label: "供应商编号",
    type: "input",
    prop: "lifnr",
  },

  {
    label: "客户编号",
    type: "input",
    prop: "kunnr",
  },


   {
    label: "是否专卖总部",
    type: "input",
    prop: "zmzbFlag",
  },


  
   {
    label: "地址",
    type: "input",
    prop: "sasAddress",
    searchOut:true
  },

  {
    label: "联系人",
    type: "input",
    prop: "IHREZ",
  },

  {
    label: "客户联系电话",
    type: "input",
    prop: "TELF1",
  },
  
  {
    label: "发运方式",
    type: "select",
    prop: "shipMethod",
    optionsCode: "ship_method",
    backProps: [
      {
        backProp: "dictValue",
        backLable: "dictLabel",
        prop: "shipMethod",
      },
    ], 
  },


].map((item)=>{
  item.width = item.width || 140
  return item
});

export const pageConfig = {
  indexPageConfig: {
    title: "组织关系",
    name: "OrgRelation",
    columns: cloumn,
    searchData: cloumn.slice(0,9),
    tableProps: {
      rowKey: "orgId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['orgRelation:orgRelation:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['orgRelation:orgRelation:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "orgRelation/orgRelation/export",
        clickType:'export',
        hasPermi:['orgRelation:orgRelation:export']

      },
    ],
    tableRowBtnwidth:200,
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['orgRelation:orgRelation:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['orgRelation:orgRelation:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['orgRelation:orgRelation:remove']


      },
    ],
    // 列表
    getList: listOrgRelation,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'90%',
    detailPageUrl: "/index",
  },
  addPageConfig: {
    name: "OrgRelationDetail",
    lineCloumn: [],
    labelWidth:'140px',
    addShowNum:cloumn.length,
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      // item.span = 24
      return item
    }),
    headerBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    lintHeaderBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    headerSearchInfo:{},
    // 新增
    addFunction: addOrgRelation,
    // 更新
    updataFuntion: updateOrgRelation,
    // 删除
    delFunction: delOrgRelation,
    // 详情
    detailFunction: getOrgRelation,
  },
};
