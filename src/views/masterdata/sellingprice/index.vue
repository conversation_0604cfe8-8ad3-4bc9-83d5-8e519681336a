<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 6 }"
      :tableProps="tableProps"
      ref="stock"
    >
      <template #searchButton>
        <el-button size="mini" :type="'primary'" icon="el-icon-plus"
          >导出</el-button
        >
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";

import { listInventory } from "@/api/inventory/stock";
import {
  columns,
  getListData,
} from "./config";

export default {
  name: "Sellingprice",
  components: {
    ComTableList,
    ComModalTable,
  },
  data() {
    return {
      columns,
      searchData: columns,
      tableProps: {
        height: 460,
      },
     
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return {
        code: 200,
        rows: getListData(),
        total: getListData().length,
      };
      const res = await listInventory(params);
      return res;
    },
  },
};
</script>
