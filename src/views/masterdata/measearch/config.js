export const searchData = [
  {
    label: "发动机编号",
    type: "input",
    prop: "",
  },
  {
    label: "物料图号",
    type: "input",
    prop: "",
  },
  {
    label: "物料名称",
    type: "input",
    prop: "",
  },
];

export const columns = [
  {
    label: "集团",
    type: "input",
    prop: "",
  },
  {
    label: "订单编号",
    type: "input",
    prop: "",
  },
  {
    label: "发动机编号",
    type: "input",
    prop: "",
  },

  {
    label: "订单数量",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "状态机图号",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "状态机名称",
  },

  {
    prop: "",
    label: "订单时间",
    width: 120,
  },
  {
    prop: "H",
    label: "服务订单",
    width: 140,
  },

];


export const columnsBoms = [
  {
    label: "层数",
    type: "input",
    prop: "",
  },
  {
    label: "工序号",
    type: "input",
    prop: "",
  },
  {
    label: "图号",
    type: "input",
    prop: "",
  },

  {
    label: "物料名称",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "图示",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "物料分类",
  },

  {
    prop: "",
    label: "数量",
    width: 120,
  },
  {
    prop: "H",
    label: "三位码",
    width: 140,
  },


   {
    prop: "",
    label: "虚拟件",
  },

  {
    prop: "",
    label: "数据来源",
    width: 120,
  },
  {
    prop: "caozuo",
    label: "操作",
    width: 140,
  },

];



export const columnscom = [
 
  {
    label: "父物料图号",
    type: "input",
    prop: "",
  },

  {
    label: "父物料名称",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "父物料三位码",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "组装件子件",
  },


];


export const columnsgai = [
 
  {
    label: "系列",
    type: "input",
    prop: "",
  },

  {
    label: "计提盖缸总成型号",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "排放",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "气缸体组件",
  },
{
    prop: "",
    label: "气缸盖组件",
  },
  
  {
    label: "活塞",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "连杆部件",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "曲轴",
  },


    
  {
    label: "凸轮轴",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "喷油器",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "缸内制动器件",
  },


];


export const columnsgaitable = [
 
  {
    label: "系列",
    type: "input",
    prop: "",
  },

  {
    label: "计提盖缸总成型号",
    type: "input",
    prop: "MATNR",
  },
    {
    label: "名称",
    type: "input",
    prop: "",
  },

  {
    label: "排放",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "气缸体组件",
  },
{
    prop: "",
    label: "气缸盖组件",
  },
  
  {
    label: "活塞",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "连杆部件",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "曲轴",
  },


    
  {
    label: "凸轮轴",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "喷油器",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "缸内制动器件",
  },


  
    
  {
    label: "前吊耳",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "曲轴正时曲轮",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "凸轮轴正时曲轮",
  },


      
  {
    label: "摆臂轴部件",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "缸内制动摇臂轴组件",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "平衡轴部件",
  },


   {
    label: "排期门桥组件",
    type: "input",
    prop: "MATNR",
  },
  {
    label: "排期门桥数量",
    type: "input",
    prop: "",
  },

  {
    prop: "",
    label: "曲轴后端法兰",
  },

  {
    prop: "",
    label: "曲轴齿轮",
  },



];





export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};



export const options = [
  {
    value:'1',
    label:'曲轴/机体/凸轮轴'
  },
    {
    value:'2',
    label:'曲轴瓦/止推片/活塞环'
  },
    {
    value:'3',
    label:'精品组件'
  }
]