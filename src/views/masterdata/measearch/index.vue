<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
      :searchProps="{ showNum: 6 }"
      :tableProps="tableProps"
      ref="stock"
    >
      <template><h3>发动机订单信息</h3></template>
      <template #searchButton>
        <el-button size="mini" :type="'primary'" icon="el-icon-plus"
          >导出</el-button
        >
      </template>
    </ComTableList>

    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="备件BOM" name="first">
        <ComTableList
          :searchShow="false"
          :searchData="[]"
          :column="columnsBoms"
          :getTableList="getList"
          :searchProps="{ showNum: 6 }"
          :tableProps="tableProps"
          ref="stock"
        >
        </ComTableList>
      </el-tab-pane>
      <el-tab-pane label="组装件" name="second">
        <ComTableList
          :searchShow="false"
          :searchData="[]"
          :column="columnscom"
          :getTableList="getList"
          :searchProps="{ showNum: 6 }"
          :tableProps="tableProps"
          ref="stock"
        >
          <template>
            <el-select v-model="value" placeholder="请选择分类" style="margin-bottom: 20px;">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </ComTableList>
      </el-tab-pane>
      <el-tab-pane label="机体缸盖总成" name="third">
        <ComTableList
          :searchShow="false"
          :searchData="[]"
          :column="columnsgai"
          :getTableList="getList"
          :searchProps="{ showNum: 6 }"
          :tableProps="tableProps"
          ref="stock"
        >
        </ComTableList>

        <ComTableList
          :searchShow="false"
          :searchData="[]"
          :column="columnsgaitable"
          :getTableList="getList"
          :searchProps="{ showNum: 6 }"
          :tableProps="tableProps"
          ref="stock"
        >
        <template><h3>表格匹配</h3></template>
        </ComTableList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import ComModalTable from "@/components/ComModalSearch/ComModalTable.vue";

import { listInventory } from "@/api/inventory/stock";
import {
  searchData,
  columns,
  getListData,
  columnsBoms,
  columnscom,
  columnsgai,
  columnsgaitable,
  options
} from "./config";


export default {
  name: "Measearch",
  components: {
    ComTableList,
    ComModalTable,
  },
  data() {
    return {
      columnsBoms,
      columnscom,
      columnsgai,
      columnsgaitable,
      columns,
      searchData: searchData,
      activeName: "first",
      options,
      tableProps: {
        // height: 460,
      },
      searchProps: {
        rules: {
          WERKS: [{ required: true, message: "请输入工厂", trigger: "blur" }],
        },
      },
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return {
        code: 200,
        rows: getListData(),
        total: getListData().length,
      };
      const res = await listInventory(params);
      return res;
    },
  },
};
</script>
