import { listCustomerMaterials, getCustomerMaterials, delCustomerMaterials, addCustomerMaterials, updateCustomerMaterials,pushCusMaterials } from "@/api/master/CustomerMaterials";

export const cloumn = [
  {
    label: "销售组织",
    type: "input",
    prop: "VKORG",
  },
  {
    label: "分销渠道",
    type: "input",
    prop: "VTWEG",
  },
  {
    label: "客户",
    type: "input",
    prop: "KUNNR",
  },

  {
    label: "专卖物料号",
    type: "input",
    prop: "ZZMATNR",
  },
  {
    label: "客户物料号",
    type: "input",
    prop: "KDMAT",
  },

  {
    prop: "POSTX",
    label: "客户物料描述",
  },
];

export const pageConfig = {
  indexPageConfig: {
    title: "客户物料主数据-宇通",
    name: "LgortInfo",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "werksId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['werksInfo:werksInfo:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['werksInfo:werksInfo:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "werksInfo/werksInfo/export",
        clickType:'export',
        hasPermi:['werksInfo:werksInfo:export']

      },
      {
        name: "推送",
        icon: "el-icon-plus",
        type: "primary",
        hasPermi:['master:CustomerMaterials:export'],
        click:(self)=>{
           pushCusMaterials().then((res)=>{
            self.$modal.msgSuccess(res?.msg || "操作成功");
          })
        }
      }, 
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['werksInfo:werksInfo:edit']

      },
      {
        name: "详情",
        type: "text",
        // click: () => {},
        requestUrl: "",
        icon: "el-icon-edit",
        clickType:'look',
        hasPermi:['werksInfo:werksInfo:detail']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['werksInfo:werksInfo:remove']


      },
    ],
    // 列表
    getList: listCustomerMaterials,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
    detailPageUrl: "/index",
  },
  addPageConfig: {
    name: "WerksInfoDetail",
    lineCloumn: cloumn,
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    title: "标题",
    lineTableProps: {
      rowKey: "werksId",
    },
    headerBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    lintHeaderBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    headerSearchInfo:{},
    // 新增
    addFunction: addCustomerMaterials,
    // 更新
    updataFuntion: updateCustomerMaterials,
    // 删除
    delFunction: delCustomerMaterials,
    // 详情
    detailFunction: getCustomerMaterials,
  },
};