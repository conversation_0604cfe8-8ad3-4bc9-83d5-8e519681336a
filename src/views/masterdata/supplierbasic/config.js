import { listSupplierbasic, getSupplierbasic, delSupplierbasic, addSupplierbasic, updateSupplierbasic } from "@/api/master/supplierbasic";

export const cloumn = [
  {
    label: "供应商编号",
    type: "input",
    prop: "LIFNR",
  },

  {
    label: "名称 1",
    type: "input",
    prop: "NAME1",
  },

  {
    label: "账户组",
    type: "input",
    prop: "KTOKK",
  },

  {
    label: "账户组描述",
    type: "input",
    prop: "KTOKKTXT",
  },


    {
    label: "税号",
    type: "input",
    prop: "STCEG",
  },

  {
    label: "简称1",
    type: "input",
    prop: "SORT1",
  },

  {
    label: "简称2",
    type: "input",
    prop: "SORT2",
  },

  {
    label: "授权属性",
    type: "input",
    prop: "ZSQBS",
  },


  
  {
    label: "寄售/非寄售",
    type: "input",
    prop: "ZJSBS",
  },

  {
    label: "收货地址2",
    type: "input",
    prop: "STREET",
  },

  {
    label: "联系人",
    type: "input",
    prop: "NAME",
  },

  {
    label: "联系人电话",
    type: "input",
    prop: "TELF1",
  },

  {
    label: "联系人邮件",
    type: "input",
    prop: "smtpAddr",
  },

  {
    label: "管车平台服务站站号",
    type: "input",
    prop: "STCD3",
  },

   {
    label: "CRM系统服务站编号",
    type: "input",
    prop: "STCD4",
  },


].map((item)=>{
  item.width = item.width || 140
  return item
});

export const lineCloumn = [
    {
    label: "供应商编号",
    type: "input",
    prop: "LIFNR",
  },

  {
    label: "采购组织",
    type: "input",
    prop: "EKORG",
  },

  {
    label: "供应商类别",
    type: "input",
    prop: "ZGYSLB",
  },

   {
    label: "供应商类别描述",
    type: "input",
    prop: "ZGYSLBTXT",
  },

  {
    label: "授权标识",
    type: "input",
    prop: "ZSQBS",
  },

  {
    label: "授权标识描述",
    type: "input",
    prop: "ZSQBSTXT",
  },

     {
    label: "寄售标识",
    type: "input",
    prop: "ZJSBS",
  },

  {
    label: "寄售标识描述",
    type: "input",
    prop: "ZJSBSTXT",
  },

  {
    label: "供应商分类",
    type: "input",
    prop: "ZGYSL",
  },

       {
    label: "供应商分类描述",
    type: "input",
    prop: "ZGYSLTXT",
  },

  {
    label: "现款比例",
    type: "input",
    prop: "ZXKBL",
  },

].map((item)=>{
  item.width = item.width || 140
  return item
});

export const pageConfig = {
  indexPageConfig: {
    title: "供应商主数据",
    name: "Supplierbasic",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "pplierMasterBasicId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:supplierbasic:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:supplierbasic:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "master/supplierbasic/export",
        clickType:'export',
        hasPermi:['master:supplierbasic:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:supplierbasic:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:supplierbasic:remove']


      },
    ],
    // 列表
    getList: listSupplierbasic,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'90%',
    detailPageUrl: "/index",
  },
  addPageConfig: {
    lineCloumn: cloumn,
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      // item.span = 24
      return item
    }),
    addSearchProps: {},
    lineTableProps: {
      rowKey: "pplierMasterPurId",
      listKey:'supplierMasterPurInfoList'
    },
    headerBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    lintHeaderBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    headerSearchInfo:{},
    // 新增
    addFunction: addSupplierbasic,
    // 更新
    updataFuntion: updateSupplierbasic,
    // 删除
    delFunction: delSupplierbasic,
    // 详情
    detailFunction: getSupplierbasic,
    lineCloumn:lineCloumn.map((item)=>{
      item.width = item.width || 140
      return item

    })
  },
};
