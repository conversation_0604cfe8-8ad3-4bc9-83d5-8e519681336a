import {
  listWerksInfo,
  getWerksInfo,
  delWerksInfo,
  addWerksInfo,
  updateWerksInfo,
} from "@/api/sales/werksInfo/werksInfo";

export const cloumn = [
  {
    label: "工厂编码",
    type: "input",
    prop: "werks",
  },

  {
    label: "工厂名称",
    type: "input",
    prop: "werksName",
  },

  {
    label: "库存地点编码",
    type: "input",
    prop: "lgort",
  },

  {
    label: "库存地点名称",
    type: "input",
    prop: "lgortName",
  },
{
    label: "是否按仓储组",
    type: "input",
    prop: "flag",
  },
  
];

export const pageConfig = {
  indexPageConfig: {
    title: "工厂-库存地点",
    name: "LgortInfo",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "werksId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['werksInfo:werksInfo:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['werksInfo:werksInfo:remove']


      },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "werksInfo/werksInfo/export",
        clickType:'export',
        hasPermi:['werksInfo:werksInfo:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['werksInfo:werksInfo:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['werksInfo:werksInfo:remove']


      },
    ],
    // 列表
    getList: listWerksInfo,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
    detailPageUrl: "/index",
  },
  addPageConfig: {
    name: "WerksInfoDetail",
    lineCloumn: [],
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    title: "标题",
    lineTableProps: {
      rowKey: "werksId",
    },
    headerBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    lintHeaderBtn: [
      {
        name: "导出",
        icon: "el-icon-plus",
        type: 'primary',
        clickFuntion: () => {},
      },
    ],
    headerSearchInfo:{},
    // 新增
    addFunction: addWerksInfo,
    // 更新
    updataFuntion: updateWerksInfo,
    // 删除
    delFunction: delWerksInfo,
    // 详情
    detailFunction: getWerksInfo,
  },
};
