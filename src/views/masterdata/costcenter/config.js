import { listCostCenterConfig, getCostCenterConfig, delCostCenterConfig, addCostCenterConfig, updateCostCenterConfig } from "@/api/master/costCenterConfig";

export const cloumn = [
  {
    label: "公司代码",
    type: "input",
    prop: "bukrs",
  },

  {
    label: "公司名称",
    type: "input",
    prop: "butxt",
  },

  {
    label: "成本中心编码",
    type: "input",
    prop: "kostl",
  },

  {
    label: "成本中心短文本",
    type: "input",
    prop: "ktext",
  },

    {
    label: "负责人",
    type: "input",
    prop: "verak",
  },
];

export const pageConfig = {
  indexPageConfig: {
    title: "成本中心",
    name: "Costcenter",
    columns: cloumn,
    searchData: cloumn,
    tableProps: {
      rowKey: "costCenterConfigId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['master:costCenterConfig:add']
      },
       {
        name: "批量删除",
        icon: "el-icon-delete",
        type: "danger",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:costCenterConfig:remove']


      },
       {
        name: "导出",
        icon: "el-icon-download",
        type: "primary",
        requestUrl: "master/costCenterConfig/export",
        clickType:'export',
        hasPermi:['master:costCenterConfig:export']

      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['master:costCenterConfig:edit']

      },
      // {
      //   name: "详情",
      //   type: "text",
      //   // click: () => {},
      //   requestUrl: "",
      //   icon: "el-icon-edit",
      //   clickType:'look',
      //   hasPermi:['werksInfo:werksInfo:detail']

      // },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['master:costCenterConfig:remove']


      },
    ],
    // 列表
    getList: listCostCenterConfig,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    labelWidth:'140px',
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    headerSearchInfo:{},
    // 新增
    addFunction: addCostCenterConfig,
    // 更新
    updataFuntion: updateCostCenterConfig,
    // 删除
    delFunction: delCostCenterConfig,
    // 详情
    detailFunction: getCostCenterConfig,
  },
};
