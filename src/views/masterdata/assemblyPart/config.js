export const columns = [
  {
    label: "组装件物料号",
    type: "input",
    prop: "zzzcZzmatnr",
    
  },

   {
    label: "组装件最小组装数量",
    type: "input",
    prop: "zzzcMengeBom",
  
  },
   {
    label: "组装件子件序号",
    type: "input",
    prop: "ZZZJXH",
  },
  {
    label: "组装件子件物料号",
    type: "input",
    prop: "zzzjZzmatnr",
  },
   {
    label: "组装件子件数量",
    type: "input",
    prop: "zzzjMenge",
  },
  {
    label: "操作",
    prop: "caozuo",
    slotName:'caozuo',
    hiddenSearch: true,
    width:140,
  },
]

export const searchListData = JSON.parse(JSON.stringify(columns)).map((item)=>{
 item.attr = {}
 return item
}).slice(0,2);


export const assemblyColumns = JSON.parse(JSON.stringify(columns))
