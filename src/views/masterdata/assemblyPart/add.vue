<template>
  <el-dialog
    :title="title + '组装件'"
    :visible.sync="dialogVisible"
    width="50%"
    class="ComModalSearch"
    @close="close"
  >
    <div class="add" v-loading="loading">
      <ComSearch
        :searchData="searchData"
        :searchInfo="searchInfo"
        :showNum="searchData.length"
        labelWidth="140px"
        :showHeader="false"
        :rules="rules"
        collapseTitle="更多内容"
        ref="ComSearchInfo"
        @submitEmits="submitEmits"
      />
    </div>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: right; margin-top: 20px"
    >
      <el-button type="primary" size="mini" @click="save">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import ComSearch from "@/components/ComSearch/index.vue";
import { columns } from "./config";
import {
  addAssemblyPart,
  updateAssemblyPart,
  getAssemblyPart,
} from "@/api/master/assemblyPart";
import { computed } from "vue";

export default {
  name: "AssemblyPartDetail",
  props: {
    assemblyPartId: {
      type: String,
      default() {
        return "";
      },
    },
    detail: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    title() {
      // console.log(this.detail,'0000')
      const text = this?.searchInfo?.assemblyPartId ? "修改" : "新增";
      return text;
    },
  },
  components: {
    ComSearch,
  },
  props: {},
  data() {
    const pageType = this.$route?.params?.type || "";
    return {
      loading: false,
      searchInfo: this.$props.detail,
      rules: {},
      searchData: columns,
      pageType,
      dialogVisible: false,
    };
  },

  created() {
    // console.log(this, "keyslineChildren");
  },
  watch: {},
  methods: {
    close() {
      this.searchInfo = {};
      this.detail = {};
      this.dialogVisible = false;
    },
    save() {
      this.$refs.ComSearchInfo.onSubmit("save");
    },
    getDetail(assemblyPartId) {
      if (this.loading) return;
      this.loading = true;
      getAssemblyPart(assemblyPartId)
        .then((response) => {
          this.loading = false;
          this.searchInfo = response.data;
          this.detail = response.data;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    submitEmits(data, type) {
      if (this.loading) return;
      this.loading = true;
      const params = {
        ...this.detail,
        ...data,
      };
      const fun = this.searchInfo?.assemblyPartId
        ? updateAssemblyPart
        : addAssemblyPart;
      fun(params)
        .then((res) => {
          this.loading = false;

          if (res.code === 200) {
            this.searchInfo = {};
            this.detail = {};
            this.dialogVisible = false;
            this.$emit("submitEmits");
            this.$modal.msgSuccess(res.msg);
          }
        })
        .catch((error) => {
          console.log(error, "error");
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .UniformCss .el-col-8 {
  width: 100%;
}
</style>
