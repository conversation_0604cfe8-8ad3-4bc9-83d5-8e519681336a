<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchListData"
      :column="columns"
      :getTableList="getList"
      ref="ComTableList"
    >
     <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd('add',{})"
          >新增</el-button
        >
      </template>
       <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            v-hasPermi="['master:assemblyPart:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-hasPermi="['master:assemblyPart:remove']"
          >删除</el-button>

        </div>
      </template>
    </ComTableList>
    <Add ref="Add" :detail="detail" @submitEmits="submitEmits"/>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";
import Add from './add.vue'
import { listAssemblyPart,delAssemblyPart } from "@/api/master/assemblyPart";
import { columns ,searchListData} from "./config";

export default {
  name: "AssemblyPart",
  components: {
    ComTableList,
    Add
  },
  data() {
    return {
      columns,
      searchListData,
      detail:{}
    };
  },
  created() {
  },
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return listAssemblyPart(params);
     
    },
    gotoAdd(){
      this.$refs.Add.dialogVisible = true
    },
    handleUpdate(row){
      this.detail = row
      // this.$refs.Add.searchInfo = this.detail
      this.$refs.Add.dialogVisible = true
      this.$refs.Add.getDetail(row.assemblyPartId)
    },
    submitEmits(){
      this.$refs.ComTableList.submitEmits()
    },
    handleDelete(row) {
      const assemblyPartIds = row.assemblyPartId;
      this.$modal.confirm('是否确认删除SAS组装件BOM信息编号为"' + assemblyPartIds + '"的数据项？').then(function() {
        return delAssemblyPart(assemblyPartIds);
      }).then(() => {
        this.$refs.ComTableList.submitEmits()
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
   
  },
};
</script>
