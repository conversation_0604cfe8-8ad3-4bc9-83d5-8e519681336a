
export const columns = [
  {
    label: "客户名称",
    type: "input",
    prop: "NAME1",
  },
  {
    label: "客户编码",
    type: "input",
    prop: "KUNNR",
  },
  {
    label: "税务登记号",
    type: "input",
    prop: "STCEG",
  },

  {
    label: "是否启用",
    type: "input",
    prop: "PLTYP",
  },
 
];

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

