import request from '@/utils/request'

// 查询移库调拨盘点领用头信息列表
export function listInventoryManage(query) {
  return request({
    url: '/sales/inventoryManage/list',
    method: 'get',
    params: query
  })
}

// 查询移库调拨盘点领用头信息详细
export function getInventoryManage(zsasOrderHeaderId) {
  return request({
    url: '/sales/inventoryManage/' + zsasOrderHeaderId,
    method: 'get'
  })
}

// 新增移库调拨盘点领用头信息
export function addInventoryManage(data) {
  return request({
    url: '/sales/inventoryManage',
    method: 'post',
    data: data
  })
}

// 修改移库调拨盘点领用头信息
export function updateInventoryManage(data) {
  return request({
    url: '/sales/inventoryManage',
    method: 'put',
    data: data
  })
}

// 删除移库调拨盘点领用头信息
export function delInventoryManage(zsasOrderHeaderId) {
  return request({
    url: '/sales/inventoryManage/' + zsasOrderHeaderId,
    method: 'delete'
  })
}


// 扫码列表

export function barcodeFlow(data) {
  return request({
    url: '/sales/inventory/barcodeFlow',
    method: 'post',
    data: data
  })
}

// 列表信息
export function queryScanBarInfo(data) {
  return request({
    url: '/sales/inventory/queryScanBarInfo',
    method: 'post',
    data: data
  })
}

// 列表信息
export function queryWmsScanInfo(data) {
  return request({
    url: '/sales/inventory/queryWmsScanInfo',
    method: 'post',
    data: data
  })
}


// 获取扫码详情
export function getCheckBarCode(data) {
  return request({
    url: '/sales/inventory/checkBarCode',
    method: 'post',
    data: data
  })
}

// 头行信息
export function queryDocDetailsInfo(data) {
  return request({
    url: '/sales/inventory/queryDocDetailsInfo',
    method: 'post',
    data: data
  })
}

// 行的子信息详情
export function queryScanDetailsInfo(data) {
  return request({
    url: '/sales/inventory/queryScanDetailsInfo',
    method: 'post',
    data: data
  })
}
// 明细删除
export function delScanDetailsLine(data) {
  return request({
    url: '/sales/inventory/delScanDetailsLine',
    method: 'post',
    data: data
  })
}

// 整单删除
export function delScanDetails(data) {
  return request({
    url: '/sales/inventory/delScanDetails/' + data,
    method: 'delete',
    data: data
  })
}



export function inventoryExport(data) {
  return request({
    url: '/sales/inventory/export',
    method: 'delete',
    data: data
  })
}





