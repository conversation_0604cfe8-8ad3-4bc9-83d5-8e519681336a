import request from "@/utils/request";

// 查询库存信息列表
export function listInventory(query) {
  return request({
    url: "/sales/inventory/list",
    method: "get",
    params: query,
  });
}

// 库存查询
export async function queryInventory(query) {
  try {
    const res = await request({
      url: "/sales/inventory/queryInventory",
      method: "post",
      data: query,
    });

    if (res.code === 200 && res.rows && res.rows.length) {
      let rows = res.rows.filter(({ LABST, SPEME, LABST1 }) => {
        // console.log(LABST,SPEME,LABST1,(LABST*1 === 0 && SPEME*1 ===0 && LABST1*1 === 0),'LABST1')
        return !(LABST * 1 === 0 && SPEME * 1 === 0 && LABST1 * 1 === 0);
      });
      res.rows = rows;
      res.total = rows.length;
    }
     return res;
  } catch (error) {
    // return error;
    console.log(error)
   return Promise.reject(new Error(error))
   
  }


}

// 查询库存信息详细
export function getInventory(inventoryId) {
  return request({
    url: "/sales/inventory/" + inventoryId,
    method: "get",
  });
}

// 新增库存信息
export function addInventory(data) {
  return request({
    url: "/sales/inventory",
    method: "post",
    data: data,
  });
}

// 修改库存信息
export function updateInventory(data) {
  return request({
    url: "/sales/inventory",
    method: "put",
    data: data,
  });
}

// 删除库存信息
export function delInventory(inventoryId) {
  return request({
    url: "/sales/inventory/" + inventoryId,
    method: "delete",
  });
}
