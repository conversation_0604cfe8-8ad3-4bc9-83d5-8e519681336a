import request from '@/utils/request'

// 查询采购订单头信息列表
export function listPurOrder(query) {
  return request({
    url: '/sales/purOrder/list',
    method: 'get',
    params: query
  })
}

// 查询采购订单头信息详细
export function getPurOrder(purOrderHeadId) {
  return request({
    url: '/sales/purOrder/' + purOrderHeadId,
    method: 'get'
  })
}

// 新增采购订单头信息
export function addPurOrder(data) {
  return request({
    url: '/sales/purOrder',
    method: 'post',
    data: data
  })
}

// 修改采购订单头信息
export function updatePurOrder(data) {
  return request({
    url: '/sales/purOrder',
    method: 'put',
    data: data
  })
}

// 删除采购订单头信息
export function delPurOrder(purOrderHeadId) {
  return request({
    url: '/sales/purOrder/' + purOrderHeadId,
    method: 'delete'
  })
}
