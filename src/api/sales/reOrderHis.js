import request from '@/utils/request'

// 查询期初历史退货列表
export function listReOrderHis(query) {
  return request({
    url: '/sales/reOrderHis/list',
    method: 'get',
    params: query
  })
}

// 查询期初历史退货详细
export function getReOrderHis(reOrderId) {
  return request({
    url: '/sales/reOrderHis/' + reOrderId,
    method: 'get'
  })
}

// 新增期初历史退货
export function addReOrderHis(data) {
  return request({
    url: '/sales/reOrderHis',
    method: 'post',
    data: data
  })
}

// 修改期初历史退货
export function updateReOrderHis(data) {
  return request({
    url: '/sales/reOrderHis',
    method: 'put',
    data: data
  })
}

// 删除期初历史退货
export function delReOrderHis(reOrderId) {
  return request({
    url: '/sales/reOrderHis/' + reOrderId,
    method: 'delete'
  })
}
