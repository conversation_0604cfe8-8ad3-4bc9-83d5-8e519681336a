import request from '@/utils/request'

// 查询期初历史发货数据列表
export function listReShipmentHis(query) {
  return request({
    url: '/sales/reShipmentHis/list',
    method: 'get',
    params: query
  })
}

// 查询期初历史发货数据详细
export function getReShipmentHis(reShipmentId) {
  return request({
    url: '/sales/reShipmentHis/' + reShipmentId,
    method: 'get'
  })
}

// 新增期初历史发货数据
export function addReShipmentHis(data) {
  return request({
    url: '/sales/reShipmentHis',
    method: 'post',
    data: data
  })
}

// 修改期初历史发货数据
export function updateReShipmentHis(data) {
  return request({
    url: '/sales/reShipmentHis',
    method: 'put',
    data: data
  })
}

// 删除期初历史发货数据
export function delReShipmentHis(reShipmentId) {
  return request({
    url: '/sales/reShipmentHis/' + reShipmentId,
    method: 'delete'
  })
}
