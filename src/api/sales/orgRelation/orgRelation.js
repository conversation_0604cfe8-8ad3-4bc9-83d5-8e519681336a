import request from '@/utils/request'

// 查询组织关系列表
export function listOrgRelation(query) {
  return request({
    url: '/sales/orgRelation/list',
    method: 'post',
    data: query

  })
}

export function listOrgRelation2(query) {
  return request({
    url: '/sales/orgRelation/list',
    method: 'get',
    params: {
      pageNum:1,
      pageSize:100000
    }
  })
}

// 查询组织关系详细
export function getOrgRelation(orgId) {
  return request({
    url: '/sales/orgRelation/' + orgId,
    method: 'get'
  })
}

// 新增组织关系
export function addOrgRelation(data) {
  return request({
    url: '/sales/orgRelation',
    method: 'post',
    data: data
  })
}

// 修改组织关系
export function updateOrgRelation(data) {
  return request({
    url: '/sales/orgRelation',
    method: 'put',
    data: data
  })
}

// 删除组织关系
export function delOrgRelation(orgId) {
  return request({
    url: '/sales/orgRelation/' + orgId,
    method: 'delete'
  })
}
