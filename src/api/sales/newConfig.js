import request from '@/utils/request'

// 查询服务站新品投放清单列表
export function listNewConfig(query) {
  return request({
    url: '/sales/newConfig/list',
    method: 'get',
    params: query
  })
}

// 查询服务站新品投放清单详细
export function getNewConfig(stationNewConfigId) {
  return request({
    url: '/sales/newConfig/' + stationNewConfigId,
    method: 'get'
  })
}

// 新增服务站新品投放清单
export function addNewConfig(data) {
  return request({
    url: '/sales/newConfig',
    method: 'post',
    data: data
  })
}

// 修改服务站新品投放清单
export function updateNewConfig(data) {
  return request({
    url: '/sales/newConfig',
    method: 'put',
    data: data
  })
}

// 删除服务站新品投放清单
export function delNewConfig(stationNewConfigId) {
  return request({
    url: '/sales/newConfig/' + stationNewConfigId,
    method: 'delete'
  })
}
