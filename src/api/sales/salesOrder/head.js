import request from '@/utils/request'

// 查询销售订单头信息列表
export function listHead(query) {
  return request({
    url: '/sales/salesOrder/list',
    method: 'get',
    params: query
  })
}

// 查询销售订单头信息详细
export function getHead(salesHeadId) {
  return request({
    url: '/sales/salesOrder/' + salesHeadId,
    method: 'get'
  })
}

// 新增销售订单头信息
export function addHead(data) {
  return request({
    url: '/sales/salesOrder',
    method: 'post',
    data: data
  })
}

// 修改销售订单头信息
export function updateHead(data) {
  return request({
    url: '/sales/salesOrder',
    method: 'put',
    data: data
  })
}

// 删除销售订单头信息
export function delHead(salesHeadId) {
  return request({
    url: '/sales/salesOrder/' + salesHeadId,
    method: 'delete'
  })
}


// 库存查询
export function queryInventory(data) {
  return request({
    url: '/sales/inventory/queryInventory' ,
    method: 'post',
    data
  })
}

// 库存预占
export function queryInventoryyuzhan(data) {
  return request({
    url: '/sales/inventory/inventoryPre' ,
    method: 'post', 
    data
  })
}

// 库存释放
export function inventoryLockCancel(data) {
  return request({
    url: '/sales/inventory/inventoryLockCancel' ,
    method: 'post',
    data
  })
}

