import request from '@/utils/request'

// 查询采购申请列表
export function listPurApply(query) {
  return request({
    url: '/sales/purApply/list',
    method: 'get',
    params: query
  })
}

// 查询采购申请详细
export function getPurApply(purApplyId) {
  return request({
    url: '/sales/purApply/' + purApplyId,
    method: 'get'
  })
}

// 新增采购申请
export function addPurApply(data) {
  return request({
    url: '/sales/purApply',
    method: 'post',
    data: data
  })
}

// 修改采购申请
export function updatePurApply(data) {
  return request({
    url: '/sales/purApply/',
    method: 'put',
    data: data
  })
}

// 删除采购申请
export function delPurApply(purApplyId) {
  return request({
    url: '/sales/purApply/' + purApplyId,
    method: 'delete'
  })
}
