import request from "@/utils/request";

// 查询需求单头信息列表
export function listDemand(query) {
  // console.log(query,'0000')
  return request({
    url: "/sales/demand/list",
    method: "get",
    params: query,
  });
}

// 查询需求单头信息详细
export function getDemand(demandHeadId) {
  return request({
    url: "/sales/demand/" + demandHeadId,
    method: "get",
  });
}

// 新增需求单头信息
export function addDemand(data) {
  return request({
    url: "/sales/demand",
    method: "post",
    data: data,
  });
}

// 修改需求单头信息
export function updateDemand(data) {
  return request({
    url: "/sales/demand",
    method: "put",
    data: data,
  });
}
// 确认
export function updateDemandStatus(data) {
  return request({
    url: "/sales/demand/updateDemandStatus",
    method: "post",
    data: data,
  });
}

// 删除需求单头信息
export function delDemand(demandHeadId) {
  return request({
    url: "/sales/demand/" + demandHeadId,
    method: "delete",
  });
}

export function queryPartsInfo(data) {
  return request({
    url: "/sales/inventory/queryPartsInfo",
    method: "post",
    data,
  });
}
