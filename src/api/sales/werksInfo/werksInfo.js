import request from "@/utils/request";

// 查询工厂-库存地点信息列表
export async function listWerksInfo(query) {
   const res = await request({
      url: "/sales/werksInfo/list",
      method: "post",
      data:query
    });

    if(res.code === 200){
      res.rows = res.rows?.map((item)=>{
        item.zzzzjLgortName = item.lgortName
        return item
      })
    }
    res.key = 'LGORT'
    return res;
}

// 查询工厂-库存地点信息详细
export function getWerksInfo(werksId) {
  return request({
    url: "/sales/werksInfo/" + werksId,
    method: "get",
  });
}

// 新增工厂-库存地点信息
export function addWerksInfo(data) {
  return request({
    url: "/sales/werksInfo",
    method: "post",
    data: data,
  });
}

// 修改工厂-库存地点信息
export function updateWerksInfo(data) {
  return request({
    url: "/sales/werksInfo",
    method: "put",
    data: data,
  });
}

// 删除工厂-库存地点信息
export function delWerksInfo(werksId) {
  return request({
    url: "/sales/werksInfo/" + werksId,
    method: "delete",
  });
}
