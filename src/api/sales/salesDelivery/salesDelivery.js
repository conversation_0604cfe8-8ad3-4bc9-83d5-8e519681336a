import request from '@/utils/request'

// 查询销售外向交货单头信息列表
export function listSalesDelivery(query) {
  return request({
    url: '/sales/salesDelivery/list',
    method: 'get',
    params: query
  })
}

// 出库
export function inventoryAccountPost(deliveryHeadId) {
  return request({
    url: '/sales/inventory/inventoryAccountPost/' + deliveryHeadId,
    method: 'get'
  })
}

// 查询销售外向交货单头信息详细
export function getSalesDelivery(deliveryHeadId) {
  return request({
    url: '/sales/salesDelivery/' + deliveryHeadId,
    method: 'get'
  })
}

// 新增销售外向交货单头信息
export function addSalesDelivery(data) {
  return request({
    url: '/sales/salesDelivery',
    method: 'post',
    data: data
  })
}

// 修改销售外向交货单头信息
export function updateSalesDelivery(data) {
  return request({
    url: '/sales/salesDelivery',
    method: 'put',
    data: data
  })
}

// 删除销售外向交货单头信息
export function delSalesDelivery(deliveryHeadId) {
  return request({
    url: '/sales/salesDelivery/' + deliveryHeadId,
    method: 'delete'
  })
}


// 打印信息
export function printInfo(deliveryHeadId) {
  return request({
    url: '/sales/salesDelivery/queryDelivery/' + deliveryHeadId,
    method: 'get'
  })
}



