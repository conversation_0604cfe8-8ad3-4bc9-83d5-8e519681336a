import request from '@/utils/request'

// 查询客户物料主数据-宇通列表
export function listCustomerMaterials(query) {
  return request({
    url: '/master/CustomerMaterials/list',
    method: 'get',
    params: query
  })
}

// 查询客户物料主数据-宇通详细
export function getCustomerMaterials(customerMaterialsId) {
  return request({
    url: '/master/CustomerMaterials/' + customerMaterialsId,
    method: 'get'
  })
}

// 新增客户物料主数据-宇通
export function addCustomerMaterials(data) {
  return request({
    url: '/master/CustomerMaterials',
    method: 'post',
    data: data
  })
}

// 修改客户物料主数据-宇通
export function updateCustomerMaterials(data) {
  return request({
    url: '/master/CustomerMaterials',
    method: 'put',
    data: data
  })
}

// 删除客户物料主数据-宇通
export function delCustomerMaterials(customerMaterialsId) {
  return request({
    url: '/master/CustomerMaterials/' + customerMaterialsId,
    method: 'delete'
  })
}

export function pushCusMaterials(data) {
  return request({
    url: '/master/CustomerMaterials/pushCusMaterials',
    method: 'post',
    data
  })
}

