import request from '@/utils/request'

// 查询扫码销售人员主管配置列表
export function listSalesPerson(query) {
  return request({
    url: '/master/salesPerson/list',
    method: 'get',
    params: query
  })
}

// 查询扫码销售人员主管配置详细
export function getSalesPerson(id) {
  return request({
    url: '/master/salesPerson/' + id,
    method: 'get'
  })
}

// 新增扫码销售人员主管配置
export function addSalesPerson(data) {
  return request({
    url: '/master/salesPerson',
    method: 'post',
    data: data
  })
}

// 修改扫码销售人员主管配置
export function updateSalesPerson(data) {
  return request({
    url: '/master/salesPerson',
    method: 'put',
    data: data
  })
}

// 删除扫码销售人员主管配置
export function delSalesPerson(id) {
  return request({
    url: '/master/salesPerson/' + id,
    method: 'delete'
  })
}
