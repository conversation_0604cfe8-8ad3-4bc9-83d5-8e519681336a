import request from '@/utils/request'

// 查询SAS组装件BOM信息列表
export async function listAssemblyPart(query) {
  const res = await request({
    url: '/master/assemblyPart/list',
    method: 'post',
    data: query
  })

  res.key='zzzcZzmatnr'

  return res
}

// 查询SAS组装件BOM信息详细
export function getAssemblyPart(assemblyPartId) {
  return request({
    url: '/master/assemblyPart/' + assemblyPartId,
    method: 'get'
  })
}

// 新增SAS组装件BOM信息
export function addAssemblyPart(data) {
  return request({
    url: '/master/assemblyPart',
    method: 'post',
    data: data
  })
}

// 修改SAS组装件BOM信息
export function updateAssemblyPart(data) {
  return request({
    url: '/master/assemblyPart',
    method: 'put',
    data: data
  })
}

// 删除SAS组装件BOM信息
export function delAssemblyPart(assemblyPartId) {
  return request({
    url: '/master/assemblyPart/' + assemblyPartId,
    method: 'delete'
  })
}
