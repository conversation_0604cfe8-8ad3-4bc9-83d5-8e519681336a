import request from '@/utils/request'

// 查询扫码物料编号配置列表
export function listMaterial(query) {
  return request({
    url: '/master/material/list',
    method: 'get',
    params: query
  })
}

// 查询扫码物料编号配置详细
export function getMaterial(id) {
  return request({
    url: '/master/material/' + id,
    method: 'get'
  })
}

// 新增扫码物料编号配置
export function addMaterial(data) {
  return request({
    url: '/master/material',
    method: 'post',
    data: data
  })
}

// 修改扫码物料编号配置
export function updateMaterial(data) {
  return request({
    url: '/master/material',
    method: 'put',
    data: data
  })
}

// 删除扫码物料编号配置
export function delMaterial(id) {
  return request({
    url: '/master/material/' + id,
    method: 'delete'
  })
}
