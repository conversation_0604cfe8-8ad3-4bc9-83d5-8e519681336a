import request from '@/utils/request'

// 查询供应商主数据采购信息列表
export function listSupplierPur(query) {
  return request({
    url: '/master/supplierPur/list',
    method: 'get',
    params: query
  })
}

// 查询供应商主数据采购信息详细
export function getSupplierPur(supplierMasterPurId) {
  return request({
    url: '/master/supplierPur/' + supplierMasterPurId,
    method: 'get'
  })
}

// 新增供应商主数据采购信息
export function addSupplierPur(data) {
  return request({
    url: '/master/supplierPur',
    method: 'post',
    data: data
  })
}

// 修改供应商主数据采购信息
export function updateSupplierPur(data) {
  return request({
    url: '/master/supplierPur',
    method: 'put',
    data: data
  })
}

// 删除供应商主数据采购信息
export function delSupplierPur(supplierMasterPurId) {
  return request({
    url: '/master/supplierPur/' + supplierMasterPurId,
    method: 'delete'
  })
}
