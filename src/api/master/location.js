import request from '@/utils/request'

// 查询扫码子库配置列表
export function listLocation(query) {
  return request({
    url: '/master/location/list',
    method: 'get',
    params: query
  })
}

// 查询扫码子库配置详细
export function getLocation(id) {
  return request({
    url: '/master/location/' + id,
    method: 'get'
  })
}

// 新增扫码子库配置
export function addLocation(data) {
  return request({
    url: '/master/location',
    method: 'post',
    data: data
  })
}

// 修改扫码子库配置
export function updateLocation(data) {
  return request({
    url: '/master/location',
    method: 'put',
    data: data
  })
}

// 删除扫码子库配置
export function delLocation(id) {
  return request({
    url: '/master/location/' + id,
    method: 'delete'
  })
}
