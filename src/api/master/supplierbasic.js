import request from '@/utils/request'

// 查询供应商主数据基本信息列表
export function listSupplierbasic(query) {
  return request({
    url: '/master/supplierbasic/list',
    method: 'get',
    params: query
  })
}

export function listSupplierbasic2(query) {
  return request({
    url: '/master/supplierbasic/list',
    method: 'get',
    params: {
      ...query,
      pageSize:100000
    }
  })
}

// 查询供应商主数据基本信息详细
export function getSupplierbasic(supplierMasterBasicId) {
  return request({
    url: '/master/supplierbasic/' + supplierMasterBasicId,
    method: 'get'
  })
}

// 新增供应商主数据基本信息
export function addSupplierbasic(data) {
  return request({
    url: '/master/supplierbasic',
    method: 'post',
    data: data
  })
}

// 修改供应商主数据基本信息
export function updateSupplierbasic(data) {
  return request({
    url: '/master/supplierbasic',
    method: 'put',
    data: data
  })
}

// 删除供应商主数据基本信息
export function delSupplierbasic(supplierMasterBasicId) {
  return request({
    url: '/master/supplierbasic/' + supplierMasterBasicId,
    method: 'delete'
  })
}
