import request from '@/utils/request'

// 查询退货超期配置列表
export function listOverdueDate(query) {
  return request({
    url: '/master/overdueDate/list',
    method: 'get',
    params: query
  })
}

// 查询退货超期配置详细
export function getOverdueDate(vkorg) {
  return request({
    url: '/master/overdueDate/' + vkorg,
    method: 'get'
  })
}

// 新增退货超期配置
export function addOverdueDate(data) {
  return request({
    url: '/master/overdueDate',
    method: 'post',
    data: data
  })
}

// 修改退货超期配置
export function updateOverdueDate(data) {
  return request({
    url: '/master/overdueDate',
    method: 'put',
    data: data
  })
}

// 删除退货超期配置
export function delOverdueDate(vkorg) {
  return request({
    url: '/master/overdueDate/' + vkorg,
    method: 'delete'
  })
}
