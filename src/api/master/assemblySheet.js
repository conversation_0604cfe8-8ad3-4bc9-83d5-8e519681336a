import request from '@/utils/request'

// 查询SAS组装单信息列表
export function listAssemblySheet(query) {
  return request({
    url: '/master/AssemblySheet/list',
    method: 'get',
    params: query
  })
}

// 查询SAS组装单信息详细
export function getAssemblySheet(assemblySheetId) {
  return request({
    url: '/master/AssemblySheet/' + assemblySheetId,
    method: 'get'
  })
}

// 新增SAS组装单信息
export function addAssemblySheet(data) {
  return request({
    url: '/master/AssemblySheet',
    method: 'post',
    data: data
  })
}

// 修改SAS组装单信息
export function updateAssemblySheet(data) {
  return request({
    url: '/master/AssemblySheet',
    method: 'put',
    data: data
  })
}

// 删除SAS组装单信息
export function delAssemblySheet(assemblySheetId) {
  return request({
    url: '/master/AssemblySheet/' + assemblySheetId,
    method: 'delete'
  })
}
