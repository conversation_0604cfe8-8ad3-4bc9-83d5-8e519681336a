import request from '@/utils/request'
// 查询需求单头信息列表
export async function queryKunnrList(query) {
  // console.log(query,'0000')
 const res = await request({
    url: "/master/customerMaster/queryKunnrList",
    method: "post",
    data: {
     ...query,
      "kunnrList":[]
    },
  });

  // console.log(res,'reslist')

  return res
}
// 查询客户主数据列表
export async function listCustomerMaster(query) {
  const res = await request({
    url: '/master/customerMaster/list',
    method: 'post',
    data:query
  })

  res.key = 'KUNNR'
  // console.log(res,'res')
  return res
}

// 查询客户主数据详细
export function getCustomerMaster(customerMasterId) {
  return request({
    url: '/master/customerMaster/' + customerMasterId,
    method: 'get'
  })
}

// 新增客户主数据
export function addCustomerMaster(data) {
  return request({
    url: '/master/customerMaster',
    method: 'post',
    data: data
  })
}

// 修改客户主数据
export function updateCustomerMaster(data) {
  return request({
    url: '/master/customerMaster',
    method: 'put',
    data: data
  })
}

// 删除客户主数据
export function delCustomerMaster(customerMasterId) {
  return request({
    url: '/master/customerMaster/' + customerMasterId,
    method: 'delete'
  })
}
