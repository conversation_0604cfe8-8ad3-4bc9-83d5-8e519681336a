import request from '@/utils/request'

// 查询扫码客户配置列表
export function listCustomer(query) {
  return request({
    url: '/master/customer/list',
    method: 'get',
    params: query
  })
}

// 查询扫码客户配置详细
export function getCustomer(id) {
  return request({
    url: '/master/customer/' + id,
    method: 'get'
  })
}

// 新增扫码客户配置
export function addCustomer(data) {
  return request({
    url: '/master/customer',
    method: 'post',
    data: data
  })
}

// 修改扫码客户配置
export function updateCustomer(data) {
  return request({
    url: '/master/customer',
    method: 'put',
    data: data
  })
}

// 删除扫码客户配置
export function delCustomer(id) {
  return request({
    url: '/master/customer/' + id,
    method: 'delete'
  })
}
