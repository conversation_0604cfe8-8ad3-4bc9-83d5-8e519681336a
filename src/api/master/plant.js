import request from '@/utils/request'

// 查询扫码工厂配置列表
export function listPlant(query) {
  return request({
    url: '/master/plant/list',
    method: 'get',
    params: query
  })
}

// 查询扫码工厂配置详细
export function getPlant(id) {
  return request({
    url: '/master/plant/' + id,
    method: 'get'
  })
}

// 新增扫码工厂配置
export function addPlant(data) {
  return request({
    url: '/master/plant',
    method: 'post',
    data: data
  })
}

// 修改扫码工厂配置
export function updatePlant(data) {
  return request({
    url: '/master/plant',
    method: 'put',
    data: data
  })
}

// 删除扫码工厂配置
export function delPlant(id) {
  return request({
    url: '/master/plant/' + id,
    method: 'delete'
  })
}
