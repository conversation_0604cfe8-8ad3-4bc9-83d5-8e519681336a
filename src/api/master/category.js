import request from '@/utils/request'

// 查询扫码物料大类和物料小类配置列表
export function listCategory(query) {
  return request({
    url: '/master/category/list',
    method: 'get',
    params: query
  })
}

// 查询扫码物料大类和物料小类配置详细
export function getCategory(id) {
  return request({
    url: '/master/category/' + id,
    method: 'get'
  })
}

// 新增扫码物料大类和物料小类配置
export function addCategory(data) {
  return request({
    url: '/master/category',
    method: 'post',
    data: data
  })
}

// 修改扫码物料大类和物料小类配置
export function updateCategory(data) {
  return request({
    url: '/master/category',
    method: 'put',
    data: data
  })
}

// 删除扫码物料大类和物料小类配置
export function delCategory(id) {
  return request({
    url: '/master/category/' + id,
    method: 'delete'
  })
}
