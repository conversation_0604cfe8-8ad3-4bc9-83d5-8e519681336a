import request from '@/utils/request'

// 查询物料主数据头信息列表
export async function listMaster(query) {
  // console.log(query,'query')


  let data = {
    ...query,
    WERKS:query?.werks || query?.WERKS || undefined
  }
  if(data?.WERKS ==='null' || data?.WERKS === null){
     data.WERKS = undefined
  }
  // console.log(data,'dadaa')
  const res = await request({
    url: '/master/master/list',
    method: 'post',
    data:data
  })

  if(res.code === 200 && res.rows){

    const obj = {
      'E':'自制生产',
      'F':'外部采购',
      'X':''
    }
    res.rows = res.rows?.map((item)=>{
      // item.ZZMLYLX =
      item.BESKZName = obj[item.BESKZ] || item?.BESKZ
      return item

    })
  }
  res.key='ZZMATNR'

  return res
}

// 查询物料主数据头信息详细
export function getMaster(masterHeadId) {
  return request({
    url: '/master/master/' + masterHeadId,
    method: 'get'
  })
}

// 新增物料主数据头信息
export function addMaster(data) {
  return request({
    url: '/master/master',
    method: 'post',
    data: data
  })
}

// 修改物料主数据头信息
export function updateMaster(data) {
  return request({
    url: '/master/master',
    method: 'put',
    data: data
  })
}

// 删除物料主数据头信息
export function delMaster(masterHeadId) {
  return request({
    url: '/master/master/' + masterHeadId,
    method: 'delete'
  })
}
