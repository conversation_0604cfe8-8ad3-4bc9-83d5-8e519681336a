import request from '@/utils/request'

// 查询成本中心配置列表
export async function listCostCenterConfig(query) {
  const res = await request({
    url: '/master/costCenterConfig/list',
    method: 'post',
    data:query
  })

  res.key = 'KOSTL'

  return res
}

// 查询成本中心配置详细
export function getCostCenterConfig(costCenterConfigId) {
  return request({
    url: '/master/costCenterConfig/' + costCenterConfigId,
    method: 'get'
  })
}

// 新增成本中心配置
export function addCostCenterConfig(data) {
  return request({
    url: '/master/costCenterConfig',
    method: 'post',
    data: data
  })
}

// 修改成本中心配置
export function updateCostCenterConfig(data) {
  return request({
    url: '/master/costCenterConfig',
    method: 'put',
    data: data
  })
}

// 删除成本中心配置
export function delCostCenterConfig(costCenterConfigId) {
  return request({
    url: '/master/costCenterConfig/' + costCenterConfigId,
    method: 'delete'
  })
}
