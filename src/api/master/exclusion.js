import request from '@/utils/request'

// 查询扫码排除子库配置列表
export function listExclusion(query) {
  return request({
    url: '/master/exclusion/list',
    method: 'get',
    params: query
  })
}

// 查询扫码排除子库配置详细
export function getExclusion(id) {
  return request({
    url: '/master/exclusion/' + id,
    method: 'get'
  })
}

// 新增扫码排除子库配置
export function addExclusion(data) {
  return request({
    url: '/master/exclusion',
    method: 'post',
    data: data
  })
}

// 修改扫码排除子库配置
export function updateExclusion(data) {
  return request({
    url: '/master/exclusion',
    method: 'put',
    data: data
  })
}

// 删除扫码排除子库配置
export function delExclusion(id) {
  return request({
    url: '/master/exclusion/' + id,
    method: 'delete'
  })
}
