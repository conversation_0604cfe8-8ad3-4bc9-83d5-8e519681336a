import request from '@/utils/request'

// 查询采购内向交货单头信息列表
export function listPurDelivery(query) {
  return request({
    url: '/purchase/purDelivery/list',
    method: 'get',
    params: query
  })
}

// 查询采购内向交货单头信息详细
export function getPurDelivery(purchaseHeadId) {
  return request({
    url: '/purchase/purDelivery/' + purchaseHeadId,
    method: 'get'
  })
}

// 新增采购内向交货单头信息
export function addPurDelivery(data) {
  return request({
    url: '/purchase/purDelivery',
    method: 'post',
    data: data
  })
}

// 修改采购内向交货单头信息
export function updatePurDelivery(data) {
  return request({
    url: '/purchase/purDelivery',
    method: 'put',
    data: data
  })
}

// 删除采购内向交货单头信息
export function delPurDelivery(purchaseHeadId) {
  return request({
    url: '/purchase/purDelivery/' + purchaseHeadId,
    method: 'delete'
  })
}


export const inventoryAccountRu = (data)=>{
   return request({
    url: '/sales/inventory/inventoryDeliveryAccount/' + data,
    method: 'get',
  })
}
